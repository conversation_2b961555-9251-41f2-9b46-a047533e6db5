# 🎮 Gamification API Documentation for Frontend

## 📋 Overview
Complete API documentation for integrating Synlearnia gamification features into frontend applications. All endpoints require authentication via Bear<PERSON> token.

**Base URL**: `http://localhost:8888/api`

## 🔐 Authentication
All requests must include Authorization header:
```
Authorization: Bearer <your_jwt_token>
```

---

## 🎭 Avatar System APIs

### 1. Initialize Avatar System (ONE-TIME ONLY)
**⚠️ IMPORTANT**: Only call this ONCE per user - when they first register or if avatar data is missing.

```http
POST /api/avatar/initialize
```

**Headers:**
```
Authorization: Bearer <token>
Content-Type: application/json
```

**Response:**
```json
{
    "success": true,
    "message": "Avatar system initialized successfully",
    "data": {
        "default_avatars_count": 5,
        "default_frame": "basic_frame",
        "default_emojis_count": 10,
        "equipped_avatar": {
            "avatar_id": 1,
            "avatar_name": "Friendly Cat",
            "avatar_code": "cat_001",
            "image_path": "/avatars/cat_001.png"
        }
    }
}
```

**Error Response:**
```json
{
    "success": false,
    "message": "Avatar system already initialized",
    "error": "User already has avatar customization"
}
```

### 2. Get User Avatar Data
**Use this to load user's avatar inventory and equipped items**

```http
GET /api/avatar/my-data
```

**Response:**
```json
{
    "success": true,
    "data": {
        "customization": {
            "user_id": 122,
            "equipped_avatar_id": 1,
            "equipped_frame_id": 1,
            "equipped_name_effect_id": null,
            "show_level": true,
            "show_title": true,
            "show_badges": false
        },
        "equipped_avatar": {
            "avatar_id": 1,
            "avatar_name": "Friendly Cat",
            "avatar_code": "cat_001",
            "description": "A friendly orange cat",
            "image_path": "/avatars/cat_001.png",
            "rarity": "COMMON",
            "rarity_display": "Phổ Biến",
            "rarity_color": "#9ca3af",
            "unlock_type": "DEFAULT",
            "is_default": true,
            "is_premium": false
        },
        "equipped_frame": {
            "frame_id": 1,
            "frame_name": "Basic Frame",
            "frame_code": "basic_001",
            "image_path": "/frames/basic_001.png",
            "rarity": "COMMON"
        },
        "inventory": {
            "avatars": [
                {
                    "inventory_id": 1,
                    "item_type": "AVATAR",
                    "item_id": 1,
                    "quantity": 1,
                    "obtained_from": "DEFAULT",
                    "obtained_at": "2025-01-30T10:00:00.000Z",
                    "Avatar": {
                        "avatar_id": 1,
                        "avatar_name": "Friendly Cat",
                        "avatar_code": "cat_001",
                        "image_path": "/avatars/cat_001.png",
                        "rarity": "COMMON",
                        "rarity_color": "#9ca3af"
                    }
                }
            ],
            "frames": [...],
            "emojis": [...]
        },
        "statistics": {
            "total_avatars": 5,
            "total_frames": 3,
            "total_emojis": 10
        }
    }
}
```

### 3. Get Display Info (For showing user in UI)
**Use this to display user's avatar/frame in chat, leaderboard, etc.**

```http
GET /api/avatar/display-info
```

**Response:**
```json
{
    "success": true,
    "data": {
        "user_id": 122,
        "user_name": "John Doe",
        "avatar": {
            "avatar_id": 1,
            "avatar_name": "Friendly Cat",
            "avatar_code": "cat_001",
            "image_path": "/avatars/cat_001.png",
            "rarity": "COMMON",
            "rarity_color": "#9ca3af"
        },
        "frame": {
            "frame_id": 1,
            "frame_name": "Basic Frame",
            "frame_code": "basic_001",
            "image_path": "/frames/basic_001.png"
        },
        "name_effect": null,
        "customization_settings": {
            "show_level": true,
            "show_title": true,
            "show_badges": false
        }
    }
}
```

### 4. Equip Avatar/Frame
**Change user's equipped avatar or frame**

```http
POST /api/avatar/equip
```

**Request Body:**
```json
{
    "item_type": "avatar",  // or "frame"
    "item_id": 5
}
```

**Response:**
```json
{
    "success": true,
    "message": "Avatar equipped successfully",
    "data": {
        "equipped_avatar": {
            "avatar_id": 5,
            "avatar_name": "Cool Dog",
            "avatar_code": "dog_002",
            "image_path": "/avatars/dog_002.png"
        }
    }
}
```

### 5. Get Available Avatars/Frames
**Browse all available items in the system**

```http
GET /api/avatar/avatars
GET /api/avatar/frames
GET /api/avatar/emojis
```

**Response:**
```json
{
    "success": true,
    "data": {
        "items": [
            {
                "avatar_id": 1,
                "avatar_name": "Friendly Cat",
                "avatar_code": "cat_001",
                "description": "A friendly orange cat",
                "image_path": "/avatars/cat_001.png",
                "rarity": "COMMON",
                "rarity_display": "Phổ Biến",
                "rarity_color": "#9ca3af",
                "unlock_type": "DEFAULT",
                "unlock_description": "Có sẵn từ đầu",
                "is_default": true,
                "is_premium": false,
                "decomposition_value": 5
            }
        ],
        "total_count": 25,
        "rarity_distribution": {
            "COMMON": 10,
            "UNCOMMON": 8,
            "RARE": 5,
            "EPIC": 2,
            "LEGENDARY": 0
        }
    }
}
```

---

## 👑 Title & Badge System APIs

### 1. Get User's Gamification Info
**Complete overview of user's progress**

```http
GET /api/gamification/me
```

**Response:**
```json
{
    "success": true,
    "data": {
        "level_info": {
            "current_level": 15,
            "total_points": 2500,
            "points_to_next_level": 200,
            "tier_name": "Bronze",
            "tier_color": "#cd7f32"
        },
        "title_stats": {
            "unlocked": 8,
            "total_available": 25,
            "completion_rate": 32,
            "active_title": {
                "title_id": 3,
                "title_name": "Học Giả",
                "tier_name": "Bronze",
                "color": "#cd7f32"
            }
        },
        "badge_stats": {
            "unlocked": 12,
            "total_available": 30,
            "completion_rate": 40,
            "rarity_breakdown": {
                "COMMON": 8,
                "UNCOMMON": 3,
                "RARE": 1,
                "EPIC": 0,
                "LEGENDARY": 0
            }
        },
        "currency": {
            "syncoin": 1500,
            "kristal": 25
        },
        "daily_limits": {
            "status": "REMOVED",
            "note": "Daily earning limits have been completely removed - users can earn unlimited currency"
        },
        "daily_limits": {
            "removed": true,
            "note": "Daily earning limits have been completely removed from the system"
        }
    }
}
```

### 2. Get User's Titles
**List all titles (unlocked and locked)**

```http
GET /api/gamification/titles
```

**Response:**
```json
{
    "success": true,
    "data": {
        "unlocked_titles": [
            {
                "user_title_id": 1,
                "title_id": 1,
                "title_name": "Tân Binh",
                "tier_name": "Wood",
                "color": "#8B4513",
                "unlock_condition": "Đạt cấp độ 1",
                "unlocked_at": "2025-01-30T10:00:00.000Z",
                "is_active": false
            },
            {
                "user_title_id": 2,
                "title_id": 3,
                "title_name": "Học Giả",
                "tier_name": "Bronze",
                "color": "#cd7f32",
                "unlock_condition": "Đạt cấp độ 10",
                "unlocked_at": "2025-01-30T12:00:00.000Z",
                "is_active": true
            }
        ],
        "locked_titles": [
            {
                "title_id": 5,
                "title_name": "Bậc Thầy",
                "tier_name": "Silver",
                "color": "#C0C0C0",
                "unlock_condition": "Đạt cấp độ 25",
                "required_level": 25,
                "user_level": 15,
                "progress_percentage": 60
            }
        ],
        "active_title": {
            "title_id": 3,
            "title_name": "Học Giả",
            "tier_name": "Bronze",
            "color": "#cd7f32"
        }
    }
}
```

### 3. Set Active Title
**Change user's displayed title**

```http
POST /api/gamification/titles/set-active
```

**Request Body:**
```json
{
    "title_id": 5
}
```

**Response:**
```json
{
    "success": true,
    "message": "Title activated successfully",
    "data": {
        "active_title": {
            "title_id": 5,
            "title_name": "Bậc Thầy",
            "tier_name": "Silver",
            "color": "#C0C0C0"
        }
    }
}
```

### 4. Get User's Badges
**List all badges (unlocked and locked)**

```http
GET /api/gamification/badges
```

**Response:**
```json
{
    "success": true,
    "data": {
        "unlocked_badges": [
            {
                "user_badge_id": 1,
                "badge_id": 1,
                "badge_name": "First Steps",
                "badge_code": "first_steps",
                "description": "Complete your first quiz",
                "icon_path": "/badges/first_steps.png",
                "rarity": "COMMON",
                "rarity_color": "#9ca3af",
                "category": "ACHIEVEMENT",
                "unlocked_at": "2025-01-30T10:00:00.000Z"
            }
        ],
        "locked_badges": [
            {
                "badge_id": 10,
                "badge_name": "Quiz Master",
                "badge_code": "quiz_master",
                "description": "Complete 100 quizzes",
                "icon_path": "/badges/quiz_master.png",
                "rarity": "EPIC",
                "rarity_color": "#8b5cf6",
                "category": "ACHIEVEMENT",
                "unlock_condition": "Complete 100 quizzes",
                "progress": {
                    "current": 45,
                    "required": 100,
                    "percentage": 45
                }
            }
        ],
        "statistics": {
            "total_unlocked": 12,
            "total_available": 30,
            "completion_rate": 40,
            "rarity_breakdown": {
                "COMMON": 8,
                "UNCOMMON": 3,
                "RARE": 1,
                "EPIC": 0,
                "LEGENDARY": 0
            }
        }
    }
}
```

---

## 🔄 Synchronization & Debug APIs

### 1. Sync User Gamification Data
**Force sync all gamification data (use when data seems out of sync)**

```http
POST /api/gamification/sync
```

**Response:**
```json
{
    "success": true,
    "message": "Gamification data synchronized successfully",
    "data": {
        "sync_results": {
            "level_updated": true,
            "new_titles_unlocked": 2,
            "new_badges_unlocked": 1,
            "avatar_unlocks": 0,
            "currency_updated": true
        },
        "new_unlocks": {
            "titles": [
                {
                    "title_id": 7,
                    "title_name": "Chuyên Gia",
                    "tier_name": "Silver"
                }
            ],
            "badges": [
                {
                    "badge_id": 15,
                    "badge_name": "Streak Master",
                    "rarity": "RARE"
                }
            ]
        }
    }
}
```

### 2. Debug User State
**Get detailed debug info (for development/troubleshooting)**

```http
GET /api/gamification/debug
```

**Response:**
```json
{
    "success": true,
    "data": {
        "user_info": {
            "user_id": 122,
            "username": "john_doe",
            "email": "<EMAIL>"
        },
        "level_info": {
            "current_level": 15,
            "total_points": 2500,
            "tier_name": "Bronze"
        },
        "avatar_system": {
            "has_customization": true,
            "equipped_avatar_id": 1,
            "equipped_frame_id": 1,
            "total_avatars": 5,
            "total_frames": 3
        },
        "title_system": {
            "total_unlocked": 8,
            "active_title_id": 3,
            "missing_unlocks": []
        },
        "badge_system": {
            "total_unlocked": 12,
            "missing_unlocks": []
        },
        "currency": {
            "syncoin": 1500,
            "kristal": 25
        },
        "last_sync": "2025-01-30T14:30:00.000Z"
    }
}
```

---

## 🎯 Frontend Integration Guide

### 1. Initial Setup (User Registration/Login)
```javascript
// Check if user has avatar system initialized
const checkAvatarSystem = async () => {
    try {
        const response = await fetch('/api/avatar/my-data', {
            headers: { 'Authorization': `Bearer ${token}` }
        });

        if (!response.ok) {
            // Initialize avatar system for new user
            await fetch('/api/avatar/initialize', {
                method: 'POST',
                headers: { 'Authorization': `Bearer ${token}` }
            });
        }
    } catch (error) {
        console.error('Avatar system check failed:', error);
    }
};
```

### 2. Display User Avatar in UI
```javascript
const getUserDisplayInfo = async () => {
    const response = await fetch('/api/avatar/display-info', {
        headers: { 'Authorization': `Bearer ${token}` }
    });
    const data = await response.json();

    return {
        avatar: data.data.avatar.image_path,
        frame: data.data.frame.image_path,
        showLevel: data.data.customization_settings.show_level,
        showTitle: data.data.customization_settings.show_title
    };
};
```

### 3. Handle Level Up Events
```javascript
// Listen for level up events and sync data
const handleLevelUp = async () => {
    const syncResponse = await fetch('/api/gamification/sync', {
        method: 'POST',
        headers: { 'Authorization': `Bearer ${token}` }
    });

    const syncData = await syncResponse.json();

    if (syncData.data.new_unlocks.titles.length > 0) {
        // Show title unlock notification
        showNotification('New title unlocked!', syncData.data.new_unlocks.titles);
    }

    if (syncData.data.new_unlocks.badges.length > 0) {
        // Show badge unlock notification
        showNotification('New badge unlocked!', syncData.data.new_unlocks.badges);
    }
};
```

### 4. Error Handling
```javascript
const handleApiError = (error, response) => {
    if (response.status === 401) {
        // Token expired, redirect to login
        window.location.href = '/login';
    } else if (response.status === 404) {
        // Resource not found, might need initialization
        console.warn('Resource not found, check if user needs initialization');
    } else {
        // Other errors
        console.error('API Error:', error);
    }
};
```

---

## 📱 Common Use Cases

### Avatar Selection Screen
1. Call `GET /api/avatar/my-data` to get inventory
2. Call `GET /api/avatar/avatars` to get all available avatars
3. Show owned vs locked avatars
4. Use `POST /api/avatar/equip` to change avatar

### Profile/Settings Screen
1. Call `GET /api/gamification/me` for overview
2. Call `GET /api/gamification/titles` for title management
3. Call `GET /api/gamification/badges` for badge collection
4. Use `POST /api/gamification/titles/set-active` to change title

### Leaderboard/Chat Display
1. Call `GET /api/avatar/display-info` for each user
2. Show avatar + frame + title if enabled
3. Cache results for performance

### Troubleshooting Sync Issues
1. Call `GET /api/gamification/debug` to check state
2. Call `POST /api/gamification/sync` to force sync
3. Check for missing unlocks in debug response

---

## 💰 Currency System Changes

### 🚫 Daily Limits Removed
**IMPORTANT**: All daily earning limits have been completely removed from the system.

#### Previous Behavior (REMOVED):
- SynCoin: 1,000 per day limit
- Kristal: 100 per day limit
- Quiz completion: 500 SynCoin per day limit
- API would return `daily_limit_reached: true`

#### Current Behavior:
- **Unlimited earning** - no daily restrictions
- All currency APIs always succeed (if user has access)
- `daily_limit_reached` always returns `false`
- No need to handle daily limit errors in frontend

#### Frontend Impact:
```javascript
// OLD CODE - Remove these checks
if (response.data.daily_limit_reached) {
    showError("Daily limit reached!");
    return;
}

// NEW CODE - Simplified, no limit checks needed
const result = await awardCurrency(userId, 'SYNC', 500);
// Will always succeed if user is authenticated
```

#### API Response Changes:
```json
{
    "success": true,
    "data": {
        "amount_awarded": 500,
        "new_balance": 2500,
        "daily_limit_reached": false,  // Always false now
        "remaining_capacity": null     // Always null now
    }
}
```

---

## ⚠️ Important Notes

1. **Initialize Once**: Only call `/api/avatar/initialize` once per user
2. **Cache Data**: Cache avatar/title data and refresh periodically
3. **Handle Errors**: Always handle 401 (unauthorized) and 404 (not found) errors
4. **Sync After Events**: Call sync after major events (level up, quiz completion)
5. **Performance**: Use display-info endpoint for UI, my-data for detailed views
6. **Real-time**: Consider WebSocket events for real-time gamification updates

---

## 🚀 Quick Start Checklist

### For New Users:
- [ ] Call `/api/avatar/initialize` once
- [ ] Call `/api/gamification/me` to get initial state
- [ ] Set up error handling for all API calls

### For Existing Users:
- [ ] Call `/api/avatar/display-info` for UI display
- [ ] Call `/api/gamification/sync` if data seems outdated
- [ ] Handle level up events with sync calls

### For Development:
- [ ] Use `/api/gamification/debug` for troubleshooting
- [ ] Test all error scenarios (401, 404, 500)
- [ ] Implement proper loading states and error messages
```
