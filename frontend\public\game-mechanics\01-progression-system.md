# 📈 CƠ CHẾ 1: HỆ THỐNG CẤP ĐỘ & DANH HIỆU

## 🎯 <PERSON><PERSON><PERSON> Tiê<PERSON>

- <PERSON><PERSON><PERSON> cảm gi<PERSON>c tiến bộ rõ ràng và lâu dài
- <PERSON><PERSON><PERSON>ến khích tham gia thường xuyên vào Quiz Racing
- Cung cấp mục tiêu ngắn hạn và dài hạn
- <PERSON><PERSON> tầng người chơi theo dedication và performance

## 📊 Hệ Thống Điểm Kinh <PERSON>ệ<PERSON> (XP)

### Nguồn XP Duy Nhất: Quiz Racing

**Tất cả XP đều đến từ Quiz Racing performance.**

> **Chi tiết tính toán XP:** Xem [Quiz Racing Reward Calculation](06-quiz-racing-system.md) để biết công thức cụ thể cho XP calculation dựa trên performance, ranking, và achievements.

**Không có nguồn XP nào khác** - players chỉ có thể kiếm XP thông qua việc tham gia và thể hiện tốt trong Quiz Racing sessions.

## 🏆 Hệ Thống Cấp Độ (Level)

### Công Thức Tính XP Cần Thiết

**Level 1-60 (Early to Mid Game):**

- Level 2: 100 XP
- Level 3: 115 XP (100 × 1.15)
- Level 4: 132 XP (115 × 1.15)
- Level n: XP_level_trước × 1.15 (tăng 15% mỗi level)

**Level 61-120 (End Game):**

- Sau level 60, mỗi level cần thêm 800 XP cố định
- Level 61: Level_60_XP + 800
- Level 62: Level_61_XP + 800
- Cứ thế tiếp tục cho đến level 120+

### Chi Tiết Phần Thưởng Lên Cấp

#### SynCoin mỗi level:

- Level 1-12 (Wood): Level × 10 SynCoin
- Level 13-36 (Bronze-Silver): Level × 15 SynCoin
- Level 37-72 (Gold-Onyx): Level × 20 SynCoin
- Level 73-108 (Sapphire-Amethyst): Level × 25 SynCoin
- Level 109+ (Master): Level × 30 SynCoin

#### Avatar Unlock Schedule:

- **Level 1**: `chick` (avatar khởi tạo)
- **Level 3**: `dog`
- **Level 5**: `rabbit`
- **Level 8**: `cow`
- **Level 10**: `bear`
- **Level 13**: `elephant` (Bronze tier)
- **Level 15**: `frog`
- **Level 18**: `pig`
- **Level 20**: `duck`
- **Level 22**: `panda`
- **Level 25**: `monkey` (Silver tier)
- **Level 28**: `horse`
- **Level 30**: `goat`
- **Level 33**: `chicken`
- **Level 35**: `buffalo`
- **Level 37**: `zebra` (Gold tier)
- **Level 40**: `giraffe`
- **Level 43**: `hippo`
- **Level 45**: `rhino`
- **Level 48**: `gorilla`
- **Level 50**: `crocodile` (Platinum tier)
- **Level 55**: `snake`
- **Level 60**: `owl`
- **Level 62**: `parrot` (Onyx tier)
- **Level 65**: `penguin`
- **Level 70**: `walrus`
- **Level 73**: `whale` (Sapphire tier)
- **Level 78**: `narwhal`
- **Level 83**: `moose`
- **Level 85**: `sloth` (Ruby tier)
- **Level 97**: Bonus avatar unlock (Amethyst tier)
- **Level 109**: Ultimate avatar unlock (Master tier)

#### Trứng Milestone Rewards:

- **Level 5, 15, 25, 35, 45**: `basic-egg`
- **Level 12, 24, 36, 48, 60**: `cracked-egg`
- **Level 20, 40, 60, 72, 84**: `royal-egg`
- **Level 50 (Platinum tier)**: `legendary-egg`
- **Level 73 (Sapphire tier)**: `dragon-egg`
- **Level 85 (Ruby tier)**: `mythical-egg`
- **Level 97 (Amethyst tier)**: `rainbow-egg`
- **Level 109 (Master tier)**: `dominus-egg`

#### Kristal Milestone Rewards:

- **Level 10**: 25 Kristal
- **Level 20**: 50 Kristal
- **Level 30**: 75 Kristal
- **Level 40**: 100 Kristal
- **Level 50**: 150 Kristal
- **Level 60**: 200 Kristal
- **Level 73**: 300 Kristal (Sapphire unlock)
- **Level 85**: 400 Kristal (Ruby unlock)
- **Level 97**: 500 Kristal (Amethyst unlock)
- **Level 109**: 1000 Kristal (Master unlock)

## 👑 Hệ Thống Tầng Danh Hiệu (Tier System)

### Chi Tiết Các Tầng & Quyền Lợi

| Tầng         | Cấp Độ   | Tên Danh Hiệu       | SynCoin Multiplier | Kristal Multiplier | Kristal Thưởng Lên Tầng |
| ------------ | -------- | ------------------- | ------------------ | ------------------ | ----------------------- |
| **Wood**     | 1-12     | Tân Binh Gỗ         | ×1.0 (baseline)    | ×1.0 (baseline)    | 0                       |
| **Bronze**   | 13-24    | Chiến Binh Đồng     | ×1.1 (+10%)        | ×1.1 (+10%)        | 50                      |
| **Silver**   | 25-36    | Tinh Anh Bạc        | ×1.2 (+20%)        | ×1.2 (+20%)        | 100                     |
| **Gold**     | 37-48    | Cao Thủ Vàng        | ×1.3 (+30%)        | ×1.3 (+30%)        | 150                     |
| **Platinum** | 49-60    | Bậc Thầy Bạch Kim   | ×1.4 (+40%)        | ×1.4 (+40%)        | 200                     |
| **Onyx**     | 61-72    | Thống Lĩnh Onyx     | ×1.5 (+50%)        | ×1.5 (+50%)        | 300                     |
| **Sapphire** | 73-84    | Hiền Triết Sapphire | ×1.6 (+60%)        | ×1.6 (+60%)        | 400                     |
| **Ruby**     | 85-96    | Chúa Tể Ruby        | ×1.7 (+70%)        | ×1.7 (+70%)        | 500                     |
| **Amethyst** | 97-108   | Đại Sư Amethyst     | ×1.8 (+80%)        | ×1.8 (+80%)        | 750                     |
| **Master**   | 109-120+ | Bậc Thầy Vô Song    | ×2.0 (+100%)       | ×2.0 (+100%)       | 1000                    |

### Tier Multiplier Effects

**SynCoin Enhancement:**

- Tất cả SynCoin earned từ Quiz Racing được nhân với tier multiplier
- Áp dụng cho: Base SynCoin + Ranking Bonus + Performance Bonus từ Quiz Racing

**Kristal Enhancement:**

- Tất cả Kristal earned từ Quiz Racing được nhân với tier multiplier
- Áp dụng cho: Base Kristal + Ranking Bonus + Performance Bonus từ Quiz Racing

**Progressive Scaling:**

- Multiplier tăng dần 10% mỗi tier từ ×1.0 (Wood) đến ×2.0 (Master)
- Khuyến khích long-term progression và dedication
- Tạo sự khác biệt rõ rệt giữa các tier trong earning potential

### Frame System

**Avatar Frames từ `avatar-frame-pack` chỉ có thể mua trong Shop:**

- Tất cả 6 frames available: `crimson-phoenix-frame`, `cyber-glitch-frame`, `drumalong-festival-frame`, `nation-of-pyro-frame`, `ocean-song-frame`, `violet-starlight-frame`
- Giá: 100-300 Kristal tùy theo design và độ hiếm
- Không có frame nào unlock tự động khi lên tier
- Một số frame đặc biệt có thể có trong rare eggs từ `eggs-icon-pack`

### Name Effects cho Tier Cao

**Hiệu ứng tên đặc biệt tự động áp dụng:**

- **Onyx (61-72)**: Viền tên màu xám đậm với hiệu ứng đá quý
- **Sapphire (73-84)**: Tên có hiệu ứng sóng nước xanh lam lấp lánh
- **Ruby (85-96)**: Tên có viền đỏ rực với hiệu ứng lửa nhẹ
- **Amethyst (97-108)**: Tên có hiệu ứng tím huyền bí với particles
- **Master (109-120+)**: Tên có hào quang vàng lấp lánh với crown icon

## 🎮 Tương Tác Với Hệ Thống Khác

### Với Quiz Racing System

- **XP là nguồn duy nhất**: Mọi XP đều đến từ việc tham gia Quiz Racing
- **Performance scaling**: Thành tích càng tốt trong quiz, XP nhận được càng cao
- **Không phân biệt cấp độ**: Quiz Racing không ghép người theo level vì sẽ có giảng viên tham gia

### Với Economy System

- **Tier benefits**: Tầng cao hơn nhận được multiplier tăng cho SynCoin/Kristal từ Quiz Racing
- **Shop access**: Các tầng cao hơn có thể mua được items độc quyền trong shop
- **Currency scaling**: Tier multiplier áp dụng cho mọi currency reward từ Quiz Racing

### Với Collection System

- **Avatar progression**: Mỗi level mở khóa 1 avatar mới theo thứ tự cố định từ `avatar-animal-pack`
- **Egg rewards**: Milestone levels nhận được các loại trứng khác nhau từ `eggs-icon-pack`
- **Progressive collection**: Unlock schedule đảm bảo players có mục tiêu dài hạn để collect

### Với Customization System

- **Progressive unlocks**: Avatar từ `avatar-animal-pack` và frames từ `avatar-frame-pack` mở khóa theo level/tier
- **Name effects**: Hiệu ứng tên special chỉ có ở các tầng cao (Onyx, Sapphire, Ruby, Amethyst, Master)
- **Status display**: Level và tier hiển thị trong profile và quiz interface

## 📊 Metrics & Analytics

### Progression Health Monitoring

**Cần theo dõi:**

- Tốc độ level up trung bình theo thời gian chơi
- Phân bố người chơi qua các tầng
- Tỷ lệ người chơi "mắc kẹt" ở level nào đó
- Sự hài lòng với tốc độ progression

**Chỉ số cân bằng:**

- Thời gian trung bình để đạt level 60: 4-6 tháng chơi đều
- Thời gian để đạt Onyx tier (level 61): 6-8 tháng
- Thời gian để đạt Master tier (level 109): 12-18 tháng
- Không quá 15% người chơi bỏ cuộc ở bất kỳ tier nào
- Phân bố lý tưởng: 40% Wood-Bronze, 30% Silver-Gold, 20% Platinum-Onyx, 10% Sapphire+

## 🚀 Implementation Notes

### Database Requirements

**Bảng player_progression:**

- player_id (UUID, primary key)
- current_xp (BIGINT, default 0)
- level (INT, default 1, range 1-120+)
- tier (ENUM: 'WOOD','BRONZE','SILVER','GOLD','PLATINUM','ONYX','SAPPHIRE','RUBY','AMETHYST','MASTER')
- total_quiz_played (INT, default 0)
- last_level_up (TIMESTAMP, nullable)
- created_at/updated_at (TIMESTAMP)

**Bảng level_rewards_claimed:**

- player_id (UUID, foreign key)
- level (INT)
- reward_type (ENUM: 'SYNCOIN','AVATAR','EGG','KRISTAL')
- reward_value (VARCHAR, ví dụ: 'bear', 'basic-egg', '150')
- claimed_at (TIMESTAMP)

**Bảng xp_history:**

- Lưu lịch sử nhận XP để analytics và audit
- Gồm: player_id (UUID), xp_gained (INT), source (ENUM: 'quiz_racing'), quiz_session_id (UUID), performance_details (JSON), gained_at (TIMESTAMP)

### Performance Optimization

**Caching Strategy:**

- Cache level và tier của player trong Redis (TTL 1 giờ)
- Cache tier benefits trong Redis (TTL 24 giờ)
- Update leaderboard real-time, cache 5 phút

**Best Practices:**

- Batch update XP cho nhiều players cùng lúc sau mỗi Quiz Racing session
- Sử dụng database triggers để tự động tính level và tier từ current_xp
- Cache tier multipliers và avatar unlock status trong Redis
- Implement rate limiting cho level up rewards để tránh spam
- Tách riêng analytics database để không ảnh hưởng performance game chính

---

**🔗 Liên Kết & Dependencies**:

- [Economy System](05-economy-system.md) - Tier multipliers và currency scaling
- [Quiz Racing System](06-quiz-racing-system.md) - Nguồn XP duy nhất và reward calculation
- [Collection System](03-collection-system.md) - Avatar unlocks và egg rewards
- [Customization System](02-customization-system.md) - Avatar frames và name effects
- [Cross-System Interactions](interactions/cross-system-interactions.md) - Tương tác tổng thể giữa các hệ thống

**📁 Asset References**:

- `avatar-animal-pack/` - 30 avatars cho progressive unlocks
- `eggs-icon-pack/` - 24 egg types cho milestone rewards
- `avatar-frame-pack/` - 6 premium frames cho shop purchases
- `vector-ranks-pack/` - Tier badges và rank icons
