# 🎮 Quiz Racing & Skills System - Frontend Package

## 📋 Tóm tắt

Hệ thống **Quiz Racing với 17 Skills** đã hoàn thành backend implementation. Package này chứa tất cả tài liệu và tools cần thiết cho frontend team.

---

## 📁 Files trong package

### 🔧 Testing & API
- **`Quiz_Racing_Skills.postman_collection.json`** - Complete Postman collection với tất cả API endpoints
- **`Quiz_Racing_Environment.postman_environment.json`** - Environment variables cho Postman
- **`test_quiz_racing_api.js`** - Node.js test script để verify API

### 📚 Documentation
- **`FRONTEND_INTEGRATION_GUIDE.md`** - **[MAIN FILE]** Hướng dẫn tổng hợp chi tiết
- **`quiz_racing_api_documentation.md`** - API documentation chi tiết
- **`skills_system.sql`** - Database schema + 17 skills data
- **`quiz_racing_schema_updates.sql`** - Racing tables schema

---

## 🚀 Quick Start (5 phút)

### 1. Import Postman Collection
```
1. Mở Postman
2. Import "Quiz_Racing_Skills.postman_collection.json"
3. Import "Quiz_Racing_Environment.postman_environment.json"  
4. Chọn environment "Quiz Racing & Skills Environment"
5. Chạy "Login Student1" để lấy auth token
6. Test các endpoints khác
```

### 2. Đọc Main Documentation
```
Mở file: FRONTEND_INTEGRATION_GUIDE.md
- Danh sách đầy đủ 18 API endpoints
- 12 WebSocket events specification  
- Code examples cho React/TypeScript
- Skills system details (17 skills, 5 categories)
- Energy system mechanics
```

---

## 🎯 Key APIs cho Frontend

### Skills Management
- `GET /api/skills/shop` - Browse 17 skills
- `POST /api/skills/purchase` - Buy skills với SynCoin/Kristal
- `POST /api/skills/loadout` - Set 4 skills cho racing
- `GET /api/skills/inventory` - User's owned skills

### Quiz Racing  
- `POST /api/quiz-racing/initialize` - Start racing session
- `GET /api/quiz-racing/session/:id` - Get session data
- WebSocket events cho real-time racing

### WebSocket Events
- `join-quiz-racing` - Join session
- `use-skill` - Execute skill
- `submit-racing-answer` - Submit answer
- `energy-update` - Energy changes
- `skill-executed` - Skill effects
- `leaderboard-update` - Real-time rankings

---

## ⚡ Energy System

```javascript
// Energy calculation
energyGain = 20% (base) + 10% (speed bonus) + 5% (streak bonus)

// 100% energy = skill selection available
// Energy resets to 0% after skill usage
```

---

## ⚔️ 17 Skills Overview

### Attack (4 skills)
- **Blackhole**: Target leader → 0 points for 3 questions
- **Steal**: Steal 50% points from player above (10% risk)
- **Break**: Reset highest streak (≥3)
- **Slow**: Reduce speed bonus for all others (2 questions)

### Defense (3 skills)  
- **Shield**: Immunity to attacks (45 seconds)
- **Lock**: Protect streak from reset (4 questions)
- **Cleanse**: Remove debuffs + restore 50% energy

### Burst (5 skills)
- **Double**: ×2 points next question (25% risk)
- **Lucky**: 60% chance wrong answer still scores
- **Triple**: ×3 points next question (40% risk)
- **Perfect**: Guaranteed correct answer
- **Quintuple**: ×5 points next question (50% risk)

### Special (3 skills)
- **Swap**: Exchange total points with random player
- **Dice**: Random benefit (1-6 roll)
- **Energy**: Instant +100% energy

### Ultimate (2 skills)
- **King**: ×2 points + immunity + guaranteed correct (3 questions)
- **Phoenix**: Bottom 3 → Top 3 jump OR lose 30% points

---

## 🔗 Backend Files Locations

```
backend/src/
├── routes/skillRoutes.js              # Skills API endpoints
├── routes/quizRacingRoutes.js         # Racing API endpoints  
├── services/skillService.js           # Skills business logic
├── services/quizRacingService.js      # Racing business logic
├── controllers/skillController.js     # Skills REST controller
├── controllers/quizRacingController.js # WebSocket controller
└── models/
    ├── skill.js                       # Skills model
    ├── userSkill.js                   # User inventory
    ├── quizSkillLoadout.js           # Loadout model
    ├── skillUsageHistory.js          # Usage tracking
    └── activeSkillEffect.js          # Active effects
```

---

## 📞 Support

### ✅ Implementation Status
- **Skills System**: 100% complete (17 skills, shop, inventory, loadout)
- **Quiz Racing**: 100% complete (WebSocket, energy, real-time)
- **Database**: 100% complete (6 tables + stored procedures)
- **API Testing**: 100% complete (Postman + Node.js scripts)

### 🛠️ Frontend Tasks
1. **Skills Shop UI** - Display 17 skills by categories
2. **Loadout Management** - Select 4 skills interface  
3. **Racing Interface** - Energy bar, skill buttons, effects display
4. **WebSocket Integration** - Real-time events handling
5. **Racing Analytics** - Leaderboard, statistics display

### 📖 Main Documentation
**Đọc file `FRONTEND_INTEGRATION_GUIDE.md` để có hướng dẫn chi tiết đầy đủ!**

---

**Ready for frontend integration! 🚀**
