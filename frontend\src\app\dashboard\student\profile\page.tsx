"use client";

import React from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/layout";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>List,
  TabsTrigger,
} from "@/components/ui/navigation";
import { Badge } from "@/components/ui/feedback";
import { Button } from "@/components/ui/forms";
import { useAuthStatus } from "@/lib/hooks/use-auth";
import { useGamification } from "@/lib/hooks/use-gamification";
import {
  TitleGallery,
  BadgeCollection,
} from "@/components/features/gamification";
import { cn } from "@/lib/utils";
import {
  User,
  Trophy,
  Award,
  Crown,
  Calendar,
  Mail,
  School,
  Edit,
  Settings,
} from "lucide-react";

export default function StudentProfilePage() {
  const { getUser } = useAuthStatus();
  const { userGamification, userTitles, userBadges, isLoading } =
    useGamification();
  const user = getUser();

  if (!user) {
    return (
      <div className="container mx-auto p-6">
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <User className="w-16 h-16 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">Chưa đăng nhập</h3>
            <p className="text-muted-foreground text-center">
              Vui lòng đăng nhập để xem thông tin cá nhân.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const activeTitle = userTitles?.find((title) => title.is_active);
  const unlockedBadges = userBadges?.filter((badge) => badge.unlocked_at) || [];
  const unlockedTitles = userTitles?.filter((title) => title.unlocked_at) || [];

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Hồ Sơ Cá Nhân</h1>
          <p className="text-muted-foreground">
            Quản lý thông tin và thành tích của bạn
          </p>
        </div>
        <Button variant="outline" className="flex items-center gap-2">
          <Edit className="h-4 w-4" />
          Chỉnh sửa
        </Button>
      </div>

      {/* Profile Overview */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col lg:flex-row items-center lg:items-start gap-6">
            {/* Avatar and Basic Info */}
            <div className="flex flex-col lg:flex-row items-center lg:items-start gap-6 flex-1">
              <div className="w-24 h-24 bg-gradient-to-br from-blue-100 to-blue-200 rounded-full flex items-center justify-center">
                <User className="w-12 h-12 text-blue-600" />
              </div>

              <div className="text-center lg:text-left space-y-2">
                <div className="space-y-1">
                  <h2 className="text-2xl font-bold">{user.fullName}</h2>
                  {activeTitle && (
                    <Badge variant="outline" className="text-sm">
                      <Crown className="h-3 w-3 mr-1" />
                      {activeTitle.Title.title_display}
                    </Badge>
                  )}
                </div>
                <p className="text-muted-foreground">{user.email}</p>
                <p className="text-sm text-muted-foreground">Sinh viên</p>
              </div>
            </div>

            {/* Quick Stats */}
            <div className="grid grid-cols-2 gap-4 lg:w-64">
              <div className="text-center p-4 bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg border border-blue-200">
                <div className="flex items-center justify-center mb-2">
                  <Crown className="h-5 w-5 text-blue-600" />
                </div>
                <div className="text-2xl font-bold text-blue-600">
                  {unlockedTitles.length}
                </div>
                <div className="text-sm text-blue-700 font-medium">
                  Danh hiệu
                </div>
              </div>

              <div className="text-center p-4 bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg border border-purple-200">
                <div className="flex items-center justify-center mb-2">
                  <Award className="h-5 w-5 text-purple-600" />
                </div>
                <div className="text-2xl font-bold text-purple-600">
                  {unlockedBadges.length}
                </div>
                <div className="text-sm text-purple-700 font-medium">
                  Huy hiệu
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Profile Details */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Personal Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Thông Tin Cá Nhân
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm">
                <Mail className="h-4 w-4 text-muted-foreground" />
                <span className="text-muted-foreground">Email:</span>
                <span className="font-medium">{user.email}</span>
              </div>

              <div className="flex items-center gap-2 text-sm">
                <School className="h-4 w-4 text-muted-foreground" />
                <span className="text-muted-foreground">Vai trò:</span>
                <span className="font-medium">Sinh viên</span>
              </div>

              <div className="flex items-center gap-2 text-sm">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span className="text-muted-foreground">Tham gia:</span>
                <span className="font-medium">N/A</span>
              </div>
            </div>

            <div className="pt-4 border-t">
              <Button variant="outline" size="sm" className="w-full">
                <Settings className="h-4 w-4 mr-2" />
                Cài đặt tài khoản
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Achievement Summary */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Trophy className="h-5 w-5" />
              Thống Kê Học Tập
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {isLoading ? (
              <div className="space-y-3">
                {[...Array(4)].map((_, i) => (
                  <div
                    key={i}
                    className="h-4 bg-gray-200 rounded animate-pulse"
                  />
                ))}
              </div>
            ) : userGamification ? (
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">
                    Tổng điểm:
                  </span>
                  <span className="font-semibold">
                    {userGamification.total_points?.toLocaleString()}
                  </span>
                </div>

                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Cấp độ:</span>
                  <span className="font-semibold">
                    {userGamification.current_level}
                  </span>
                </div>

                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">
                    Quiz hoàn thành:
                  </span>
                  <span className="font-semibold">
                    {userGamification.stats?.total_quizzes_completed || 0}
                  </span>
                </div>

                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">
                    Độ chính xác:
                  </span>
                  <span className="font-semibold text-green-600">
                    {userGamification.stats?.total_questions_answered
                      ? Math.round(
                          (userGamification.stats.total_correct_answers /
                            userGamification.stats.total_questions_answered) *
                            100
                        )
                      : 0}
                    %
                  </span>
                </div>

                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">
                    Streak tốt nhất:
                  </span>
                  <span className="font-semibold text-orange-600">
                    {userGamification.stats?.best_streak || 0}
                  </span>
                </div>
              </div>
            ) : (
              <p className="text-sm text-muted-foreground">
                Chưa có dữ liệu thành tích
              </p>
            )}
          </CardContent>
        </Card>

        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Thành Tích Gần Đây
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {/* Recent badges/titles */}
              {unlockedBadges.slice(0, 2).map((badge) => (
                <div
                  key={badge.user_badge_id}
                  className="flex items-center gap-3 p-3 bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg border border-purple-200"
                >
                  <div className="w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center">
                    <Award className="h-4 w-4 text-white" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium truncate text-purple-900">
                      {badge.Badge.badge_name}
                    </p>
                    <p className="text-xs text-purple-600">
                      Huy hiệu •{" "}
                      {new Date(badge.unlocked_at).toLocaleDateString("vi-VN")}
                    </p>
                  </div>
                </div>
              ))}

              {unlockedTitles.slice(0, 2).map((title) => (
                <div
                  key={title.user_title_id}
                  className="flex items-center gap-3 p-3 bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg border border-blue-200"
                >
                  <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                    <Crown className="h-4 w-4 text-white" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium truncate text-blue-900">
                      {title.Title.title_display}
                    </p>
                    <p className="text-xs text-blue-600">
                      Danh hiệu •{" "}
                      {new Date(title.unlocked_at).toLocaleDateString("vi-VN")}
                    </p>
                  </div>
                </div>
              ))}

              {unlockedBadges.length === 0 && unlockedTitles.length === 0 && (
                <div className="text-center py-6">
                  <Trophy className="h-12 w-12 text-muted-foreground mx-auto mb-3" />
                  <p className="text-sm text-muted-foreground">
                    Chưa có thành tích nào
                  </p>
                  <p className="text-xs text-muted-foreground mt-1">
                    Hoàn thành quiz để nhận huy hiệu và danh hiệu!
                  </p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Collections Tabs */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Trophy className="h-5 w-5" />
            Bộ Sưu Tập Thành Tích
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="titles" className="w-full">
            <TabsList className="grid w-full grid-cols-2 mb-6">
              <TabsTrigger value="titles" className="flex items-center gap-2">
                <Crown className="h-4 w-4" />
                Danh Hiệu ({unlockedTitles.length})
              </TabsTrigger>
              <TabsTrigger value="badges" className="flex items-center gap-2">
                <Award className="h-4 w-4" />
                Huy Hiệu ({unlockedBadges.length})
              </TabsTrigger>
            </TabsList>

            <TabsContent value="titles" className="mt-0">
              <TitleGallery showStats={true} />
            </TabsContent>

            <TabsContent value="badges" className="mt-0">
              <BadgeCollection showStats={true} />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
