# Frontend Integration Guide - Emoji & Social System

## 📋 API Endpoints Summary

### 🎭 EMOJI ENDPOINTS

| Method | Endpoint | Description | Related Files |
|--------|----------|-------------|---------------|
| POST | `/api/emojis/initialize` | Initialize user emoji system | `emojiController.js:initializeUserEmojis()` |
| GET | `/api/emojis/collection` | Get user emoji collection | `emojiController.js:getUserEmojiCollection()` |
| GET | `/api/emojis/available` | Get available emojis for tier | `emojiController.js:getAvailableEmojis()` |
| GET | `/api/emojis/category/:category` | Get emojis by category | `emojiController.js:getEmojisByCategory()` |
| GET | `/api/emojis/shop` | Get emoji shop | `emojiController.js:getEmojiShop()` |
| POST | `/api/emojis/purchase` | Purchase emoji with <PERSON><PERSON> | `emojiController.js:purchaseEmoji()` |
| POST | `/api/emojis/use` | Use emoji in context | `emojiController.js:useEmoji()` |
| GET | `/api/emojis/usage/history` | Get usage history | `emojiController.js:getEmojiUsageHistory()` |
| GET | `/api/emojis/usage/stats` | Get usage statistics | `emojiController.js:getEmojiUsageStats()` |
| POST | `/api/emojis/favorite` | Set favorite emoji | `emojiController.js:setFavoriteEmoji()` |
| GET | `/api/emojis/:emoji_id` | Get emoji details | `emojiController.js:getEmojiDetails()` |

### 🤝 SOCIAL ENDPOINTS

| Method | Endpoint | Description | Related Files |
|--------|----------|-------------|---------------|
| POST | `/api/social/emoji-reaction` | Send emoji reaction | `socialController.js:sendEmojiReaction()` |
| POST | `/api/social/encouragement` | Send encouragement | `socialController.js:sendEncouragement()` |
| POST | `/api/social/celebrate` | Celebrate achievement | `socialController.js:celebrateAchievement()` |
| GET | `/api/social/stats` | Get user social stats | `socialController.js:getUserSocialStats()` |
| GET | `/api/social/interactions/history` | Get interaction history | `socialController.js:getSocialInteractionHistory()` |
| GET | `/api/social/top-users` | Get top social users | `socialController.js:getTopSocialUsers()` |
| GET | `/api/social/leaderboard` | Get social leaderboard | `socialController.js:getSocialLeaderboard()` |
| GET | `/api/social/rank` | Get user social rank | `socialController.js:getUserSocialRank()` |
| POST | `/api/social/favorite-emoji` | Set profile favorite emoji | `socialController.js:setFavoriteEmoji()` |
| GET | `/api/social/profile/:user_id?` | Get social profile | `socialController.js:getUserSocialProfile()` |

## 📁 Backend File Structure

```
backend/src/
├── controllers/
│   ├── emojiController.js          # Emoji API endpoints
│   └── socialController.js         # Social interaction endpoints
├── models/
│   ├── emojiType.js               # Emoji type definitions
│   ├── userEmoji.js               # User emoji collection
│   ├── emojiUsageHistory.js       # Usage tracking
│   ├── socialInteraction.js       # Social interactions
│   └── userSocialStats.js         # Social statistics
├── services/
│   └── emojiSocialService.js      # Business logic layer
├── routes/
│   ├── emojiRoutes.js             # Emoji route definitions
│   └── socialRoutes.js            # Social route definitions
└── app.js                         # Main app with route registration
```

## 📊 Database Schema Files

```
doc_for_devgame/task3/
├── gamification_emoji_social_system.sql    # Complete database schema
├── emoji_social_api_documentation.md       # Detailed API docs
├── postman_testing_guide.md               # Testing instructions
└── frontend_integration_guide.md          # This file
```

## 🎯 Key Data Structures for Frontend

### Emoji Object
```typescript
interface EmojiType {
  emoji_type_id: number;
  emoji_name: string;
  emoji_code: string;
  emoji_image_path: string;
  category: 'BASIC' | 'REACTION' | 'EMOTION' | 'SPECIAL' | 'PREMIUM';
  tier_requirement: 'WOOD' | 'BRONZE' | 'SILVER' | 'GOLD' | 'PLATINUM' | 'ONYX' | 'SAPPHIRE' | 'RUBY' | 'AMETHYST' | 'MASTER';
  rarity: 'COMMON' | 'RARE' | 'EPIC' | 'LEGENDARY';
  kristal_price: number;
  is_purchasable: boolean;
  unlock_description: string;
  rarity_color: string;
}
```

### User Emoji Collection
```typescript
interface UserEmoji {
  user_emoji_id: number;
  emoji_type_id: number;
  is_favorite: boolean;
  usage_count: number;
  unlock_source: string;
  EmojiType: EmojiType;
}
```

### Social Stats
```typescript
interface UserSocialStats {
  user_id: number;
  total_emojis_unlocked: number;
  total_emoji_usage: number;
  positive_interactions_sent: number;
  positive_interactions_received: number;
  social_reputation_score: number;
  reputation_level: 'NEWCOMER' | 'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED' | 'EXPERT' | 'LEGENDARY';
  FavoriteEmoji?: EmojiType;
}
```

### Social Interaction
```typescript
interface SocialInteraction {
  interaction_id: number;
  from_user_id: number;
  to_user_id: number;
  interaction_type: 'EMOJI_REACTION' | 'ENCOURAGEMENT' | 'CELEBRATION' | 'APPRECIATION' | 'SUPPORT';
  emoji_type_id?: number;
  context?: string;
  created_at: string;
  FromUser: User;
  ToUser: User;
  EmojiType?: EmojiType;
}
```

## 🔧 Frontend Implementation Suggestions

### 1. Emoji Picker Component
**Files to reference**: `emojiController.js:getUserEmojiCollection()`
```javascript
// Get user's emoji collection with categories
GET /api/emojis/collection?category=REACTION
```

### 2. Emoji Shop Component
**Files to reference**: `emojiController.js:getEmojiShop()`, `emojiController.js:purchaseEmoji()`
```javascript
// Display purchasable emojis
GET /api/emojis/shop

// Purchase emoji
POST /api/emojis/purchase
Body: { emoji_type_id: 25 }
```

### 3. Social Interaction Components
**Files to reference**: `socialController.js:sendEmojiReaction()`
```javascript
// Send emoji reaction to user
POST /api/social/emoji-reaction
Body: {
  to_user_id: 123,
  emoji_type_id: 5,
  context: "quiz_completion"
}
```

### 4. Leaderboard Component
**Files to reference**: `socialController.js:getSocialLeaderboard()`
```javascript
// Get social leaderboard
GET /api/social/leaderboard?criteria=reputation&limit=10
```

### 5. User Profile Social Stats
**Files to reference**: `socialController.js:getUserSocialStats()`
```javascript
// Get user's social statistics
GET /api/social/stats?timeframe=7d
```

## 🎨 UI/UX Integration Points

### Emoji Usage Contexts
- **PRE_QUIZ**: Motivation buttons before quiz start
- **DURING_QUIZ**: Quick reactions during quiz
- **POST_QUIZ**: Celebration/disappointment after quiz
- **PROFILE**: Profile decoration and expression
- **SOCIAL_REACTION**: Reactions to other users' activities
- **ACHIEVEMENT_CELEBRATION**: Achievement unlock celebrations

### Social Features Integration
- **Quiz Results Page**: Add emoji reactions and encouragement buttons
- **User Profiles**: Display social stats and favorite emoji
- **Leaderboards**: Show social rankings alongside academic rankings
- **Achievement Notifications**: Allow celebration reactions
- **Community Feed**: Social interaction history display

## 🔗 Integration with Existing Systems

### Authentication
All endpoints require JWT token in Authorization header:
```javascript
headers: {
  'Authorization': `Bearer ${userToken}`,
  'Content-Type': 'application/json'
}
```

### Error Handling
Standard error response format:
```typescript
interface ApiError {
  success: false;
  message: string;
  error?: string;
}
```

### Success Response Format
```typescript
interface ApiSuccess<T> {
  success: true;
  message: string;
  data: T;
}
```

## 📱 Mobile Considerations

### Emoji Display
- Use SVG paths from `/vector-emojis-pack/` directory
- Implement emoji size scaling for different screen sizes
- Consider emoji animation for social interactions

### Touch Interactions
- Quick emoji selection for mobile quiz interface
- Swipe gestures for emoji categories
- Long press for emoji details/purchase

## 🚀 Performance Optimization

### Caching Strategy
- Cache user emoji collection locally
- Implement pagination for emoji shop and leaderboards
- Use lazy loading for emoji images

### Real-time Updates
- WebSocket integration for live social interactions
- Real-time leaderboard updates
- Live emoji usage notifications

## 📋 Testing Checklist for Frontend

- [ ] Emoji picker displays user's collection correctly
- [ ] Emoji shop shows purchasable items with Kristal prices
- [ ] Social interactions send and display properly
- [ ] Leaderboards update with correct rankings
- [ ] Error handling works for all edge cases
- [ ] Mobile responsive design functions correctly
- [ ] Performance is acceptable with large emoji collections

## 🔍 Debug Information

### Common Issues
1. **Emoji images not loading**: Check `/vector-emojis-pack/` path
2. **Social interactions not working**: Verify user authentication
3. **Leaderboard empty**: Ensure users have social activity
4. **Purchase failures**: Check Kristal balance and emoji availability

### Useful Debug Endpoints
- `GET /api/emojis/collection` - Check user's emoji status
- `GET /api/social/stats` - Verify social statistics
- `GET /api/currency/balance` - Check Kristal balance for purchases
