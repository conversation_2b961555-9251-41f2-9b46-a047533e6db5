# 🎮 SYNLEARNIA GAME MECHANICS - TÀI LIỆU CHI TIẾT

## 📁 C<PERSON>u trúc <PERSON>h<PERSON> mục

```
game-mechanics/
├── README.md                           # Tổng quan hệ thống
├── 01-progression-system.md            # Hệ thống Cấp độ & <PERSON><PERSON> hiệu
├── 02-customization-system.md          # Tùy chỉnh & <PERSON><PERSON><PERSON> hiện
├── 03-collection-system.md             # Trứng Thưởng & <PERSON><PERSON><PERSON> Tầm
├── 04-social-system.md                 # Tương tác Xã hội
├── 05-economy-system.md                # Hệ thống Tiền tệ & Ki<PERSON> tế
├── 06-quiz-racing-system.md            # Cuộc Đua Quiz Tốc Độ
├── interactions/
│   ├── cross-system-interactions.md    # Tương tác giữa các hệ thống
│   ├── reward-distribution.md          # Phân phối phần thưởng
│   ├── progression-flow.md             # Luồng tiến trình
│   └── balance-mechanics.md            # <PERSON><PERSON> chế cân bằng
└── technical/
    ├── database-schema.md              # Cấu trúc cơ sở dữ liệu
    ├── api-endpoints.md                # Định nghĩa API
    ├── real-time-events.md             # Sự kiện thời gian thực
    └── implementation-phases.md        # Giai đoạn triển khai
```

## 🔗 Mối Quan Hệ Giữa Các Hệ Thống

### Luồng Chính (Core Flow)

```
Người chơi → Quiz Racing → Nhận Thưởng → Tiến Bộ → Tùy Chỉnh → Tương Tác
     ↑                                     ↓
Kinh Tế ← ← ← ← ← ← ← ← ← ← ← ← ← ← Sưu Tầm
```

### Các Kết Nối Quan Trọng

1. **Quiz Racing** ↔ **Economy**: Nhận SynCoin từ quiz, dùng SynCoin mua skills
2. **Collection** ↔ **Economy**: Phân giải vật phẩm trùng lặp thành Kristal
3. **Progression** ↔ **Customization**: Cấp độ mở khóa avatar và khung
4. **Social** ↔ **Collection**: Emoji từ trứng thưởng
5. **Economy** ↔ **All Systems**: Hub trung tâm kết nối tất cả

## 📊 Các Chỉ Số Quan Trọng (KPIs)

### Engagement Metrics

- **Quiz Participation Rate**: % người chơi tham gia quiz mỗi ngày
- **Skill Usage Rate**: Tần suất sử dụng skills trong quiz racing
- **Collection Completion**: % hoàn thành bộ sưu tập avatar/emoji

### Economic Metrics

- **SynCoin Circulation**: Lượng SynCoin được kiếm và tiêu mỗi ngày
- **Kristal Scarcity**: Tỷ lệ Kristal/SynCoin trong economy
- **Shop Conversion**: % người chơi mua items từ cửa hàng

### Progression Metrics

- **Level Distribution**: Phân bố cấp độ của người chơi
- **Tier Achievement**: % đạt các tầng danh hiệu
- **Retention by Tier**: Tỷ lệ giữ chân theo từng tầng

## 🎯 Mục Tiêu Thiết Kế

### Ngắn Hạn (1-2 tuần)

- Người chơi làm quen với Quiz Racing
- Tích lũy SynCoin đầu tiên
- Mở khóa avatar và skills cơ bản

### Trung Hạn (1-3 tháng)

- Đạt tầng Silver/Gold
- Sưu tầm đa dạng skills và avatar
- Tham gia cạnh tranh bảng xếp hạng

### Dài Hạn (3+ tháng)

- Master tier với hiệu ứng tên đặc biệt
- Hoàn thành bộ sưu tập
- Trở thành cao thủ Quiz Racing

## 🔄 Vòng Lặp Game Chính

```mermaid
graph TD
    A[Đăng nhập hàng ngày] --> B[Tham gia Quiz Racing]
    B --> C[Sử dụng Skills chiến lược]
    C --> D[Nhận thưởng XP + SynCoin]
    D --> E[Lên cấp / Nhận trứng]
    E --> F[Mở trứng / Mua skills]
    F --> G[Trang bị loadout mới]
    G --> B

    D --> H[Tích lũy Kristal]
    H --> I[Mua items hiếm]
    I --> J[Thể hiện thành tích]
    J --> K[Tương tác xã hội]
    K --> A
```

## 📈 Chiến Lược Monetization (Tương lai)

### Premium Currency (Kristal)

- Không bán trực tiếp
- Chỉ kiếm được qua gameplay
- Tạo giá trị cho effort và skill

### Time Savers (Tùy chọn)

- Boost XP trong thời gian ngắn
- Extra trứng chances
- Không ảnh hưởng competitive balance

### Cosmetics Premium

- Khung avatar độc quyền
- Hiệu ứng tên đặc biệt
- Limited edition items

---

**📝 Ghi chú**: Các file chi tiết sẽ đi sâu vào từng hệ thống cụ thể, bao gồm logic, công thức tính toán, và implementation guidelines.

**🔗 Liên kết**: Xem [Cross-System Interactions](interactions/cross-system-interactions.md) để hiểu rõ cách các hệ thống tương tác với nhau.
