"use client";

import React, { useState, useMemo } from "react";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/layout";
import { Button } from "@/components/ui/forms";
import { Badge } from "@/components/ui/feedback";
import { Input } from "@/components/ui/forms";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/forms";
import { Checkbox } from "@/components/ui/forms";
import { Label } from "@/components/ui/forms";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/layout";
import { cn } from "@/lib/utils";
import { getVietnameseTierName, TIER_NAMES } from "@/lib/utils/tier-assets";
import {
  UserBadgeData,
  UserTitleData,
  BadgeRarity,
} from "@/lib/types/gamification";
import {
  Search,
  Filter,
  X,
  ChevronDown,
  Calendar,
  Star,
  Trophy,
  Award,
} from "lucide-react";

// Utility functions for sorting
const getSortValue = (
  item: UserBadgeData | UserTitleData,
  sortBy: string,
  type: "badge" | "title"
): any => {
  switch (sortBy) {
    case "name":
      return type === "badge"
        ? (item as UserBadgeData).Badge.badge_name
        : (item as UserTitleData).Title.title_display;
    case "tier":
      return type === "badge"
        ? (item as UserBadgeData).Badge.tier_name
        : (item as UserTitleData).Title.tier_name;
    case "unlock_date":
      return item.unlocked_at ? new Date(item.unlocked_at) : new Date(0);
    case "level":
      return type === "badge"
        ? (item as UserBadgeData).Badge.unlock_level
        : (item as UserTitleData).Title.unlock_level;
    default:
      return "";
  }
};

interface FilterState {
  search: string;
  tiers: string[];
  rarities: string[];
  status: string[];
  dateRange: {
    from?: Date;
    to?: Date;
  };
  sortBy: string;
  sortOrder: "asc" | "desc";
}

interface AdvancedFilterProps {
  items: (UserBadgeData | UserTitleData)[];
  type: "badge" | "title";
  onFilterChange: (filteredItems: (UserBadgeData | UserTitleData)[]) => void;
  className?: string;
}

export const AdvancedFilter: React.FC<AdvancedFilterProps> = ({
  items,
  type,
  onFilterChange,
  className,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [filters, setFilters] = useState<FilterState>({
    search: "",
    tiers: [],
    rarities: [],
    status: [],
    dateRange: {},
    sortBy: "name",
    sortOrder: "asc",
  });

  // Get available rarities for badges
  const availableRarities = useMemo(() => {
    if (type !== "badge" || !items || items.length === 0) return [];
    const rarities = new Set<string>();
    items.forEach((item) => {
      const badge = item as UserBadgeData;
      rarities.add(badge.Badge.rarity);
    });
    return Array.from(rarities);
  }, [items, type]);

  // Get available tiers
  const availableTiers = useMemo(() => {
    if (!items || items.length === 0) return [];
    const tiers = new Set<string>();
    items.forEach((item) => {
      if (type === "badge") {
        const badge = item as UserBadgeData;
        tiers.add(badge.Badge.tier_name);
      } else {
        const title = item as UserTitleData;
        tiers.add(title.Title.tier_name);
      }
    });
    return Array.from(tiers);
  }, [items, type]);

  // Filter and sort items
  const filteredItems = useMemo(() => {
    if (!items || items.length === 0) return [];
    let filtered = [...items];

    // Search filter
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      filtered = filtered.filter((item) => {
        if (type === "badge") {
          const badge = item as UserBadgeData;
          return (
            badge.Badge.badge_name.toLowerCase().includes(searchLower) ||
            badge.Badge.description.toLowerCase().includes(searchLower) ||
            getVietnameseTierName(badge.Badge.tier_name)
              .toLowerCase()
              .includes(searchLower)
          );
        } else {
          const title = item as UserTitleData;
          return (
            title.Title.title_name.toLowerCase().includes(searchLower) ||
            title.Title.title_display.toLowerCase().includes(searchLower) ||
            getVietnameseTierName(title.Title.tier_name)
              .toLowerCase()
              .includes(searchLower)
          );
        }
      });
    }

    // Tier filter
    if (filters.tiers.length > 0) {
      filtered = filtered.filter((item) => {
        const tierName =
          type === "badge"
            ? (item as UserBadgeData).Badge.tier_name
            : (item as UserTitleData).Title.tier_name;
        return filters.tiers.includes(tierName);
      });
    }

    // Rarity filter (badges only)
    if (type === "badge" && filters.rarities.length > 0) {
      filtered = filtered.filter((item) => {
        const badge = item as UserBadgeData;
        return filters.rarities.includes(badge.Badge.rarity);
      });
    }

    // Status filter
    if (filters.status.length > 0) {
      filtered = filtered.filter((item) => {
        const isUnlocked = !!item.unlocked_at;
        const isActive =
          type === "title" ? (item as UserTitleData).is_active : false;

        return filters.status.some((status) => {
          switch (status) {
            case "unlocked":
              return isUnlocked;
            case "locked":
              return !isUnlocked;
            case "active":
              return isActive;
            default:
              return true;
          }
        });
      });
    }

    // Date range filter
    if (filters.dateRange.from || filters.dateRange.to) {
      filtered = filtered.filter((item) => {
        if (!item.unlocked_at) return false;
        const unlockDate = new Date(item.unlocked_at);

        if (filters.dateRange.from && unlockDate < filters.dateRange.from) {
          return false;
        }
        if (filters.dateRange.to && unlockDate > filters.dateRange.to) {
          return false;
        }
        return true;
      });
    }

    // Sort using utility function
    filtered.sort((a, b) => {
      const aValue = getSortValue(a, filters.sortBy, type);
      const bValue = getSortValue(b, filters.sortBy, type);

      if (aValue < bValue) return filters.sortOrder === "asc" ? -1 : 1;
      if (aValue > bValue) return filters.sortOrder === "asc" ? 1 : -1;
      return 0;
    });

    return filtered;
  }, [items, filters, type]);

  // Update filters
  const updateFilter = (key: keyof FilterState, value: any) => {
    setFilters((prev) => ({ ...prev, [key]: value }));
  };

  const toggleArrayFilter = (
    key: "tiers" | "rarities" | "status",
    value: string
  ) => {
    setFilters((prev) => ({
      ...prev,
      [key]: prev[key].includes(value)
        ? prev[key].filter((item) => item !== value)
        : [...prev[key], value],
    }));
  };

  const clearFilters = () => {
    setFilters({
      search: "",
      tiers: [],
      rarities: [],
      status: [],
      dateRange: {},
      sortBy: "name",
      sortOrder: "asc",
    });
  };

  const hasActiveFilters = useMemo(() => {
    return (
      filters.search ||
      filters.tiers.length > 0 ||
      filters.rarities.length > 0 ||
      filters.status.length > 0 ||
      filters.dateRange.from ||
      filters.dateRange.to
    );
  }, [filters]);

  // Apply filters when they change
  React.useEffect(() => {
    onFilterChange(filteredItems);
  }, [filteredItems, onFilterChange]);

  return (
    <div className={cn("space-y-4", className)}>
      {/* Quick Search */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder={`Tìm kiếm ${
            type === "badge" ? "huy hiệu" : "danh hiệu"
          }...`}
          value={filters.search}
          onChange={(e) => updateFilter("search", e.target.value)}
          className="pl-10"
        />
      </div>

      {/* Advanced Filters */}
      <Collapsible open={isOpen} onOpenChange={setIsOpen}>
        <CollapsibleTrigger asChild>
          <Button variant="outline" className="w-full justify-between">
            <div className="flex items-center gap-2">
              <Filter className="h-4 w-4" />
              Bộ lọc nâng cao
              {hasActiveFilters && (
                <Badge variant="secondary" className="ml-2">
                  {filters.tiers.length +
                    filters.rarities.length +
                    filters.status.length}
                </Badge>
              )}
            </div>
            <ChevronDown className="h-4 w-4" />
          </Button>
        </CollapsibleTrigger>

        <CollapsibleContent className="space-y-4 mt-4">
          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm">Bộ lọc</CardTitle>
                {hasActiveFilters && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={clearFilters}
                    className="h-6 px-2 text-xs"
                  >
                    <X className="h-3 w-3 mr-1" />
                    Xóa tất cả
                  </Button>
                )}
              </div>
            </CardHeader>

            <CardContent className="space-y-4">
              {/* Tier Filter */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">Hạng</Label>
                <div className="flex flex-wrap gap-2">
                  {availableTiers.map((tier) => (
                    <div key={tier} className="flex items-center space-x-2">
                      <Checkbox
                        id={`tier-${tier}`}
                        checked={filters.tiers.includes(tier)}
                        onCheckedChange={() => toggleArrayFilter("tiers", tier)}
                      />
                      <Label htmlFor={`tier-${tier}`} className="text-sm">
                        {getVietnameseTierName(tier)}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Rarity Filter (Badges only) */}
              {type === "badge" && (
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Độ hiếm</Label>
                  <div className="flex flex-wrap gap-2">
                    {availableRarities.map((rarity) => (
                      <div key={rarity} className="flex items-center space-x-2">
                        <Checkbox
                          id={`rarity-${rarity}`}
                          checked={filters.rarities.includes(rarity)}
                          onCheckedChange={() =>
                            toggleArrayFilter("rarities", rarity)
                          }
                        />
                        <Label htmlFor={`rarity-${rarity}`} className="text-sm">
                          {rarity === "common"
                            ? "Thường"
                            : rarity === "rare"
                            ? "Hiếm"
                            : rarity === "epic"
                            ? "Sử Thi"
                            : rarity === "legendary"
                            ? "Huyền Thoại"
                            : rarity}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Status Filter */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">Trạng thái</Label>
                <div className="flex flex-wrap gap-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="status-unlocked"
                      checked={filters.status.includes("unlocked")}
                      onCheckedChange={() =>
                        toggleArrayFilter("status", "unlocked")
                      }
                    />
                    <Label htmlFor="status-unlocked" className="text-sm">
                      Đã mở khóa
                    </Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="status-locked"
                      checked={filters.status.includes("locked")}
                      onCheckedChange={() =>
                        toggleArrayFilter("status", "locked")
                      }
                    />
                    <Label htmlFor="status-locked" className="text-sm">
                      Chưa mở khóa
                    </Label>
                  </div>

                  {type === "title" && (
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="status-active"
                        checked={filters.status.includes("active")}
                        onCheckedChange={() =>
                          toggleArrayFilter("status", "active")
                        }
                      />
                      <Label htmlFor="status-active" className="text-sm">
                        Đang sử dụng
                      </Label>
                    </div>
                  )}
                </div>
              </div>

              {/* Sort Options */}
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Sắp xếp theo</Label>
                  <Select
                    value={filters.sortBy}
                    onValueChange={(value) => updateFilter("sortBy", value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="name">Tên</SelectItem>
                      <SelectItem value="tier">Hạng</SelectItem>
                      <SelectItem value="level">Level yêu cầu</SelectItem>
                      <SelectItem value="unlock_date">Ngày mở khóa</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label className="text-sm font-medium">Thứ tự</Label>
                  <Select
                    value={filters.sortOrder}
                    onValueChange={(value: "asc" | "desc") =>
                      updateFilter("sortOrder", value)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="asc">Tăng dần</SelectItem>
                      <SelectItem value="desc">Giảm dần</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>
        </CollapsibleContent>
      </Collapsible>

      {/* Results Summary */}
      <div className="flex items-center justify-between text-sm text-muted-foreground">
        <span>
          Hiển thị {filteredItems.length} / {items.length}{" "}
          {type === "badge" ? "huy hiệu" : "danh hiệu"}
        </span>

        {hasActiveFilters && (
          <div className="flex items-center gap-2">
            <span>Đã lọc</span>
            <div className="flex gap-1">
              {filters.tiers.map((tier) => (
                <Badge key={tier} variant="secondary" className="text-xs">
                  {getVietnameseTierName(tier)}
                </Badge>
              ))}
              {filters.rarities.map((rarity) => (
                <Badge key={rarity} variant="secondary" className="text-xs">
                  {rarity}
                </Badge>
              ))}
              {filters.status.map((status) => (
                <Badge key={status} variant="secondary" className="text-xs">
                  {status}
                </Badge>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AdvancedFilter;
