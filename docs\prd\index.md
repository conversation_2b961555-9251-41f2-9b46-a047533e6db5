# QL_CTDT Frontend Code Restructuring PRD

## Table of Contents

- [QL_CTDT Frontend Code Restructuring PRD](#table-of-contents)
  - [Intro Project Analysis and Context](./intro-project-analysis-and-context.md)
    - [Analysis Source](./intro-project-analysis-and-context.md#analysis-source)
    - [Current Project State](./intro-project-analysis-and-context.md#current-project-state)
    - [Available Documentation Analysis](./intro-project-analysis-and-context.md#available-documentation-analysis)
    - [Enhancement Scope Definition](./intro-project-analysis-and-context.md#enhancement-scope-definition)
    - [Goals and Background Context](./intro-project-analysis-and-context.md#goals-and-background-context)
    - [Change Log](./intro-project-analysis-and-context.md#change-log)
  - [Requirements](./requirements.md)
    - [Functional Requirements](./requirements.md#functional-requirements)
    - [Non-Functional Requirements](./requirements.md#non-functional-requirements)
    - [Compatibility Requirements](./requirements.md#compatibility-requirements)
    - [Testing Policy](./requirements.md#testing-policy)
  - [User Interface Enhancement Goals](./user-interface-enhancement-goals.md)
    - [Integration with Existing UI](./user-interface-enhancement-goals.md#integration-with-existing-ui)
    - [Modified/New Screens and Views](./user-interface-enhancement-goals.md#modifiednew-screens-and-views)
    - [UI Consistency Requirements](./user-interface-enhancement-goals.md#ui-consistency-requirements)
  - [Technical Constraints and Integration Requirements](./technical-constraints-and-integration-requirements.md)
    - [Existing Technology Stack](./technical-constraints-and-integration-requirements.md#existing-technology-stack)
    - [Integration Approach](./technical-constraints-and-integration-requirements.md#integration-approach)
    - [Code Organization and Standards](./technical-constraints-and-integration-requirements.md#code-organization-and-standards)
    - [Risk Assessment and Mitigation](./technical-constraints-and-integration-requirements.md#risk-assessment-and-mitigation)
  - [Epic and Story Structure](./epic-and-story-structure.md)
  - [Epic 1: Frontend Code Restructuring](./epic-1-frontend-code-restructuring.md)
    - [Story 1.1: Thiết lập Cấu trúc Thư mục Mới và Migration Plan](./epic-1-frontend-code-restructuring.md#story-11-thit-lp-cu-trc-th-mc-mi-v-migration-plan)
      - [Acceptance Criteria](./epic-1-frontend-code-restructuring.md#acceptance-criteria)
      - [Integration Verification](./epic-1-frontend-code-restructuring.md#integration-verification)
    - [Story 1.2: Migration UI Components sang Components/UI Structure](./epic-1-frontend-code-restructuring.md#story-12-migration-ui-components-sang-componentsui-structure)
      - [Acceptance Criteria](./epic-1-frontend-code-restructuring.md#acceptance-criteria)
      - [Integration Verification](./epic-1-frontend-code-restructuring.md#integration-verification)
    - [Story 1.3: Tách Business Logic Components sang Features Structure](./epic-1-frontend-code-restructuring.md#story-13-tch-business-logic-components-sang-features-structure)
      - [Acceptance Criteria](./epic-1-frontend-code-restructuring.md#acceptance-criteria)
      - [Integration Verification](./epic-1-frontend-code-restructuring.md#integration-verification)
    - [Story 1.4: Consolidate Utilities và Shared Logic vào Lib Structure](./epic-1-frontend-code-restructuring.md#story-14-consolidate-utilities-v-shared-logic-vo-lib-structure)
      - [Acceptance Criteria](./epic-1-frontend-code-restructuring.md#acceptance-criteria)
      - [Integration Verification](./epic-1-frontend-code-restructuring.md#integration-verification)
    - [Story 1.4 (Final): Consolidate Utilities và Complete Migration](./epic-1-frontend-code-restructuring.md#story-14-final-consolidate-utilities-v-complete-migration)
      - [Acceptance Criteria](./epic-1-frontend-code-restructuring.md#acceptance-criteria)
      - [Integration Verification](./epic-1-frontend-code-restructuring.md#integration-verification)
  - [Epic 2: Nâng Cấp Front-end Hỗ Trợ API Phân Tích Học Tập Theo Chương](./epic-2-nng-cp-front-end-h-tr-api-phn-tch-hc-tp-theo-chng.md)
    - [Story 2.1: Tích Hợp API Chapter Analytics Mới](./epic-2-nng-cp-front-end-h-tr-api-phn-tch-hc-tp-theo-chng.md#story-21-tch-hp-api-chapter-analytics-mi)
      - [Acceptance Criteria](./epic-2-nng-cp-front-end-h-tr-api-phn-tch-hc-tp-theo-chng.md#acceptance-criteria)
      - [Integration Verification](./epic-2-nng-cp-front-end-h-tr-api-phn-tch-hc-tp-theo-chng.md#integration-verification)
    - [Story 2.2: Cập Nhật Student Quiz Results Page](./epic-2-nng-cp-front-end-h-tr-api-phn-tch-hc-tp-theo-chng.md#story-22-cp-nht-student-quiz-results-page)
      - [Acceptance Criteria](./epic-2-nng-cp-front-end-h-tr-api-phn-tch-hc-tp-theo-chng.md#acceptance-criteria)
      - [Integration Verification](./epic-2-nng-cp-front-end-h-tr-api-phn-tch-hc-tp-theo-chng.md#integration-verification)
    - [Story 2.3: Xây Dựng Student Learning Results Dashboard](./epic-2-nng-cp-front-end-h-tr-api-phn-tch-hc-tp-theo-chng.md#story-23-xy-dng-student-learning-results-dashboard)
      - [Acceptance Criteria](./epic-2-nng-cp-front-end-h-tr-api-phn-tch-hc-tp-theo-chng.md#acceptance-criteria)
      - [Integration Verification](./epic-2-nng-cp-front-end-h-tr-api-phn-tch-hc-tp-theo-chng.md#integration-verification)
    - [Story 2.4: Nâng Cấp Teacher Analytics Dashboard](./epic-2-nng-cp-front-end-h-tr-api-phn-tch-hc-tp-theo-chng.md#story-24-nng-cp-teacher-analytics-dashboard)
      - [Acceptance Criteria](./epic-2-nng-cp-front-end-h-tr-api-phn-tch-hc-tp-theo-chng.md#acceptance-criteria)
      - [Integration Verification](./epic-2-nng-cp-front-end-h-tr-api-phn-tch-hc-tp-theo-chng.md#integration-verification)
    - [Story 2.5: Cập Nhật Real-time Quiz Monitor](./epic-2-nng-cp-front-end-h-tr-api-phn-tch-hc-tp-theo-chng.md#story-25-cp-nht-real-time-quiz-monitor)
      - [Acceptance Criteria](./epic-2-nng-cp-front-end-h-tr-api-phn-tch-hc-tp-theo-chng.md#acceptance-criteria)
      - [Integration Verification](./epic-2-nng-cp-front-end-h-tr-api-phn-tch-hc-tp-theo-chng.md#integration-verification)
    - [Story 2.6: Responsive Design và Mobile Optimization](./epic-2-nng-cp-front-end-h-tr-api-phn-tch-hc-tp-theo-chng.md#story-26-responsive-design-v-mobile-optimization)
      - [Acceptance Criteria](./epic-2-nng-cp-front-end-h-tr-api-phn-tch-hc-tp-theo-chng.md#acceptance-criteria)
      - [Integration Verification](./epic-2-nng-cp-front-end-h-tr-api-phn-tch-hc-tp-theo-chng.md#integration-verification)
  - [Epic 3: Tích Hợp Hệ Thống Level/XP Mới với Visual Assets - Brownfield Enhancement](./epic-3-tch-hp-h-thng-levelxp-mi-vi-visual-assets-brownfield-enhancement.md)
    - [Epic Description](./epic-3-tch-hp-h-thng-levelxp-mi-vi-visual-assets-brownfield-enhancement.md#epic-description)
    - [Story 3.1: Cập Nhật API Integration và Image Mapping System](./epic-3-tch-hp-h-thng-levelxp-mi-vi-visual-assets-brownfield-enhancement.md#story-31-cp-nht-api-integration-v-image-mapping-system)
      - [Acceptance Criteria](./epic-3-tch-hp-h-thng-levelxp-mi-vi-visual-assets-brownfield-enhancement.md#acceptance-criteria)
      - [Integration Verification](./epic-3-tch-hp-h-thng-levelxp-mi-vi-visual-assets-brownfield-enhancement.md#integration-verification)
    - [Story 3.2: Nâng Cấp UserLevelBadge với Rich Tier Visuals](./epic-3-tch-hp-h-thng-levelxp-mi-vi-visual-assets-brownfield-enhancement.md#story-32-nng-cp-userlevelbadge-vi-rich-tier-visuals)
      - [Acceptance Criteria](./epic-3-tch-hp-h-thng-levelxp-mi-vi-visual-assets-brownfield-enhancement.md#acceptance-criteria)
      - [Integration Verification](./epic-3-tch-hp-h-thng-levelxp-mi-vi-visual-assets-brownfield-enhancement.md#integration-verification)
    - [Story 3.3: Xây Dựng Title & Badge Gallery với Interactive Management](./epic-3-tch-hp-h-thng-levelxp-mi-vi-visual-assets-brownfield-enhancement.md#story-33-xy-dng-title-badge-gallery-vi-interactive-management)
      - [Acceptance Criteria](./epic-3-tch-hp-h-thng-levelxp-mi-vi-visual-assets-brownfield-enhancement.md#acceptance-criteria)
      - [Integration Verification](./epic-3-tch-hp-h-thng-levelxp-mi-vi-visual-assets-brownfield-enhancement.md#integration-verification)
    - [Compatibility Requirements](./epic-3-tch-hp-h-thng-levelxp-mi-vi-visual-assets-brownfield-enhancement.md#compatibility-requirements)
    - [Risk Mitigation](./epic-3-tch-hp-h-thng-levelxp-mi-vi-visual-assets-brownfield-enhancement.md#risk-mitigation)
    - [Definition of Done](./epic-3-tch-hp-h-thng-levelxp-mi-vi-visual-assets-brownfield-enhancement.md#definition-of-done)
  - [Epic 4: Tích Hợp Hệ Thống Avatar & Customization vào Frontend - Brownfield Enhancement](./epic-4-tch-hp-h-thng-avatar-customization-vo-frontend-brownfield-enhancement.md)
    - [Epic Description](./epic-4-tch-hp-h-thng-avatar-customization-vo-frontend-brownfield-enhancement.md#epic-description)
    - [Story 4.1: Tạo Avatar Service và API Integration](./epic-4-tch-hp-h-thng-avatar-customization-vo-frontend-brownfield-enhancement.md#story-41-to-avatar-service-v-api-integration)
      - [Acceptance Criteria](./epic-4-tch-hp-h-thng-avatar-customization-vo-frontend-brownfield-enhancement.md#acceptance-criteria)
      - [Integration Verification](./epic-4-tch-hp-h-thng-avatar-customization-vo-frontend-brownfield-enhancement.md#integration-verification)
    - [Story 4.2: Xây Dựng Avatar Display Component](./epic-4-tch-hp-h-thng-avatar-customization-vo-frontend-brownfield-enhancement.md#story-42-xy-dng-avatar-display-component)
      - [Acceptance Criteria](./epic-4-tch-hp-h-thng-avatar-customization-vo-frontend-brownfield-enhancement.md#acceptance-criteria)
      - [Integration Verification](./epic-4-tch-hp-h-thng-avatar-customization-vo-frontend-brownfield-enhancement.md#integration-verification)
    - [Story 4.3: Tạo Avatar Customization Interface](./epic-4-tch-hp-h-thng-avatar-customization-vo-frontend-brownfield-enhancement.md#story-43-to-avatar-customization-interface)
      - [Acceptance Criteria](./epic-4-tch-hp-h-thng-avatar-customization-vo-frontend-brownfield-enhancement.md#acceptance-criteria)
      - [Integration Verification](./epic-4-tch-hp-h-thng-avatar-customization-vo-frontend-brownfield-enhancement.md#integration-verification)
    - [Story 4.4: Collection Progress và Achievement Tracking](./epic-4-tch-hp-h-thng-avatar-customization-vo-frontend-brownfield-enhancement.md#story-44-collection-progress-v-achievement-tracking)
      - [Acceptance Criteria](./epic-4-tch-hp-h-thng-avatar-customization-vo-frontend-brownfield-enhancement.md#acceptance-criteria)
      - [Integration Verification](./epic-4-tch-hp-h-thng-avatar-customization-vo-frontend-brownfield-enhancement.md#integration-verification)
    - [Story 4.5: Emoji System Integration](./epic-4-tch-hp-h-thng-avatar-customization-vo-frontend-brownfield-enhancement.md#story-45-emoji-system-integration)
      - [Acceptance Criteria](./epic-4-tch-hp-h-thng-avatar-customization-vo-frontend-brownfield-enhancement.md#acceptance-criteria)
      - [Integration Verification](./epic-4-tch-hp-h-thng-avatar-customization-vo-frontend-brownfield-enhancement.md#integration-verification)
    - [Story 4.6: Avatar Integration vào Existing Components](./epic-4-tch-hp-h-thng-avatar-customization-vo-frontend-brownfield-enhancement.md#story-46-avatar-integration-vo-existing-components)
      - [Acceptance Criteria](./epic-4-tch-hp-h-thng-avatar-customization-vo-frontend-brownfield-enhancement.md#acceptance-criteria)
      - [Integration Verification](./epic-4-tch-hp-h-thng-avatar-customization-vo-frontend-brownfield-enhancement.md#integration-verification)
    - [Compatibility Requirements](./epic-4-tch-hp-h-thng-avatar-customization-vo-frontend-brownfield-enhancement.md#compatibility-requirements)
    - [Risk Mitigation](./epic-4-tch-hp-h-thng-avatar-customization-vo-frontend-brownfield-enhancement.md#risk-mitigation)
    - [Definition of Done](./epic-4-tch-hp-h-thng-avatar-customization-vo-frontend-brownfield-enhancement.md#definition-of-done)
