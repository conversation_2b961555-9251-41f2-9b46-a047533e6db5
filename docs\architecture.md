# QL_CTDT System Architecture Document

## Document Information

**Project**: QL_CTDT (Quản lý Chương trình Đào tạo)  
**Version**: 2.0  
**Date**: 2025-07-26  
**Author**: <PERSON> (System Architect)  
**Status**: Current Production Architecture

### Change Log

| Date       | Version | Description                                                         | Author              |
| ---------- | ------- | ------------------------------------------------------------------- | ------------------- |
| 2025-01-23 | 1.0     | Initial architecture documentation                                  | Business Analyst    |
| 2025-07-26 | 2.0     | Updated after frontend restructuring - complete system architecture | <PERSON> (Architect) |

## Executive Summary

QL_CTDT là hệ thống quản lý chương trình đào tạo với tính năng quiz real-time, đư<PERSON><PERSON> xây dựng trên kiến trúc monorepo với Next.js 15 frontend và Node.js/Express backend. Hệ thống hỗ trợ authentication, role-based access control, real-time quiz interactions, analytics, và gamification features.

**Key Achievements**:

- ✅ Frontend restructured theo feature-based architecture
- ✅ Real-time quiz system với Socket.IO
- ✅ Comprehensive analytics và reporting
- ✅ Gamification system với leaderboards
- ✅ Role-based access control (Admin, Teacher, Student)

## Technology Stack

### Core Technologies

| Category           | Technology   | Version  | Purpose                   | Rationale                            |
| ------------------ | ------------ | -------- | ------------------------- | ------------------------------------ |
| Package Manager    | pnpm         | 8.15.0   | Workspace management      | Efficient monorepo support           |
| Frontend Framework | Next.js      | 15.3.0   | React application         | App Router, SSR, modern React 19     |
| Frontend Runtime   | React        | 19.0.0   | UI library                | Latest stable version                |
| Backend Runtime    | Node.js      | >=18.0.0 | Server runtime            | LTS version, modern features         |
| Backend Framework  | Express      | 5.1.0    | Web application framework | Mature, extensive ecosystem          |
| Language           | TypeScript   | ^5       | Frontend type safety      | Enhanced developer experience        |
| Database           | PostgreSQL   | 15       | Primary data store        | ACID compliance, complex queries     |
| Cache              | Redis        | 7        | Session & real-time cache | High performance, pub/sub support    |
| ORM                | Sequelize    | 6.37.7   | Database abstraction      | Mature ORM with migration support    |
| Real-time          | Socket.IO    | 4.8.1    | WebSocket communication   | Reliable real-time features          |
| UI Library         | Radix UI     | Latest   | Component primitives      | Accessible, unstyled components      |
| Styling            | Tailwind CSS | ^4       | Utility-first CSS         | Rapid development, consistent design |
| Authentication     | JWT          | 9.0.2    | Token-based auth          | Stateless, scalable authentication   |
| Password Hashing   | bcrypt       | 5.1.1    | Secure password storage   | Industry standard hashing            |
| File Processing    | xlsx         | 0.18.5   | Excel import/export       | Bulk data operations                 |
| Reverse Proxy      | Nginx        | latest   | Load balancing & SSL      | Production deployment                |

### Development Tools

| Category         | Technology     | Version | Purpose                  |
| ---------------- | -------------- | ------- | ------------------------ |
| Containerization | Docker         | latest  | Development & deployment |
| Orchestration    | Docker Compose | latest  | Multi-service management |
| Version Control  | Git            | latest  | Source code management   |

## System Architecture

### High-Level Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     Client      │    │     Nginx       │    │   Load Balancer │
│   (Browser)     │◄──►│  Reverse Proxy  │◄──►│   (Optional)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                        Application Layer                        │
├─────────────────┬─────────────────┬─────────────────────────────┤
│   Frontend      │    Backend      │       Real-time             │
│   Next.js 15    │   Express 5     │      Socket.IO              │
│   React 19      │   Node.js 18+   │                             │
│   TypeScript    │   JavaScript    │                             │
└─────────────────┴─────────────────┴─────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                         Data Layer                              │
├─────────────────┬─────────────────┬─────────────────────────────┤
│   PostgreSQL    │     Redis       │      File Storage           │
│   Primary DB    │   Cache/Session │    Local uploads/           │
│   Port 5433     │   Port 6379     │                             │
└─────────────────┴─────────────────┴─────────────────────────────┘
```

### Deployment Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                      Docker Compose                             │
├─────────────────┬─────────────────┬─────────────────────────────┤
│   nginx:latest  │  frontend:3000  │     backend:8888            │
│   SSL/Proxy     │   Next.js App   │    Express API              │
└─────────────────┴─────────────────┴─────────────────────────────┘
├─────────────────┬─────────────────────────────────────────────────┤
│ postgres:15     │              redis:7                           │
│ Database        │              Cache                             │
│ Port 5433       │              Port 6379                         │
└─────────────────┴─────────────────────────────────────────────────┘
```

## Project Structure

### Monorepo Organization

```
QL_CTDT_FRONT/
├── frontend/                    # Next.js 15 Application
│   ├── src/app/                # App Router (Route Handlers)
│   │   ├── (auth)/             # Authentication routes
│   │   ├── dashboard/          # Main dashboard
│   │   ├── quiz-live/          # Real-time quiz interface
│   │   ├── quiz-monitor/       # Teacher monitoring
│   │   └── quiz-waiting-room/  # Pre-quiz lobby
│   ├── src/components/         # React Components (Restructured)
│   │   ├── ui/                 # Categorized UI Components
│   │   │   ├── forms/          # Form components
│   │   │   ├── navigation/     # Navigation components
│   │   │   ├── layout/         # Layout components
│   │   │   ├── feedback/       # Feedback components
│   │   │   └── display/        # Display components
│   │   └── features/           # Feature-based Components
│   │       ├── auth/           # Authentication features
│   │       ├── quiz/           # Quiz management
│   │       ├── charts/         # Analytics & visualization
│   │       ├── gamification/   # Gamification features
│   │       ├── navigation/     # App navigation
│   │       ├── learning/       # Learning features
│   │       ├── subject/        # Subject management
│   │       └── shared/         # Shared components
│   └── src/lib/                # Centralized Utilities
│       ├── auth/               # Authentication utilities
│       ├── hooks/              # Custom React hooks
│       ├── services/           # API & Socket services
│       ├── types/              # TypeScript definitions
│       ├── utils/              # Utility functions
│       ├── constants/          # Application constants
│       └── validations/        # Validation schemas
├── backend/                    # Node.js/Express API
│   ├── src/controllers/        # Business logic (35+ controllers)
│   ├── src/models/             # Sequelize models (25+ models)
│   ├── src/routes/             # API route definitions
│   ├── src/services/           # Business services
│   ├── src/middleware/         # Express middleware
│   ├── src/migrations/         # Database migrations
│   ├── src/redis/              # Redis utilities
│   └── src/uploads/            # File upload storage
├── docker-compose.yml          # Multi-service deployment
├── nginx.conf                  # Reverse proxy configuration
└── pnpm-workspace.yaml         # Workspace configuration
```

## Data Architecture

### Database Design

**Primary Database**: PostgreSQL 15

- **Models**: 25+ Sequelize models với complex relationships
- **Key Entities**: Users, Programs, Courses, Subjects, Chapters, Quizzes, Questions, Answers
- **Relationships**: Many-to-many relationships cho program-course-subject hierarchy
- **Constraints**: Foreign key constraints, unique constraints, check constraints

### Caching Strategy

**Redis 7 Implementation**:

- **Session Storage**: User sessions với TTL
- **Quiz State**: Real-time quiz data caching
- **Leaderboards**: Sorted sets cho performance
- **Pub/Sub**: Real-time event broadcasting

### File Storage

**Local File System**:

- **Location**: `backend/src/uploads/`
- **Types**: Excel files, images, documents
- **Processing**: xlsx library cho Excel import/export
- **Issue**: No cleanup mechanism (technical debt)

## Security Architecture

### Authentication & Authorization

**JWT-based Authentication**:

- **Token Storage**: localStorage (frontend)
- **Token Validation**: Middleware-based (backend)
- **Role-based Access**: Admin, Teacher, Student roles
- **Password Security**: bcrypt hashing với salt

### API Security

**Security Measures**:

- **CORS**: Configured cho multiple localhost ports
- **Rate Limiting**: Not implemented (technical debt)
- **Input Validation**: Sequelize ORM protection
- **SQL Injection**: ORM-based protection

## Real-time Architecture

### Socket.IO Implementation

**Server-side** (`backend/src/socket.js`):

- **Room Management**: Quiz-specific rooms
- **Event Handling**: Question distribution, answer collection
- **State Synchronization**: Redis-backed state management

**Client-side** (`frontend/src/lib/services/socket/`):

- **Singleton Pattern**: Single connection instance
- **Event Listeners**: Real-time quiz updates
- **Reconnection**: Manual reconnection logic

### Real-time Data Flow

```
Student → Frontend → Socket.IO → Backend → Redis → Database
   ↑                                ↓
   ←─────── Real-time Updates ←─────┘
```

## Performance Considerations

### Frontend Performance

**Optimizations Achieved**:

- ✅ **Bundle Optimization**: Barrel exports enable tree-shaking
- ✅ **Code Splitting**: Feature-based organization
- ✅ **Import Efficiency**: Categorized components
- ✅ **Developer Experience**: Organized structure

### Backend Performance

**Current Issues**:

- ❌ **Massive Controller**: quizController.js (3400+ lines)
- ❌ **N+1 Queries**: Some controller patterns
- ❌ **File Cleanup**: No automatic cleanup mechanism
- ❌ **Connection Pool**: Limited to 5 connections

## Testing Strategy

### Project Testing Policy

**CRITICAL ARCHITECTURAL DECISION**: NO TESTING POLICY

- **Unit Tests**: KHÔNG ĐƯỢC TRIỂN KHAI
- **Integration Tests**: KHÔNG ĐƯỢC TRIỂN KHAI
- **E2E Tests**: KHÔNG ĐƯỢC TRIỂN KHAI
- **Manual Testing**: KHÔNG ĐƯỢC TRIỂN KHAI
- **Test Frameworks**: KHÔNG ĐƯỢC SỬ DỤNG

**Quality Assurance Approach**:

- TypeScript compilation success
- Application startup without errors
- Basic functionality verification
- Code review processes
- Runtime error monitoring

## Deployment Strategy

### Development Environment

```bash
# Prerequisites
Node.js >=18.0.0
pnpm >=8.0.0
Docker & Docker Compose

# Setup
pnpm install
cp .env.example .env
docker-compose up -d postgres redis
pnpm dev
```

### Production Deployment

**Docker Compose Services**:

1. **nginx**: Reverse proxy với SSL termination
2. **frontend**: Next.js production build
3. **backend**: Express server
4. **postgres**: Database service
5. **redis**: Cache service

**Environment Configuration**:

- **Ports**: Frontend:3000, Backend:8888, DB:5433, Redis:6379
- **SSL**: Let's Encrypt certificates
- **Domain**: stardust.id.vn (hardcoded)

## Technical Debt & Known Issues

### Critical Issues

**Backend Technical Debt**:

1. **Massive Quiz Controller**: 3400+ lines cần refactor
2. **File Upload Management**: No cleanup mechanism
3. **Database Migrations**: Naming inconsistencies
4. **Error Handling**: Inconsistent response formats
5. **Socket.IO Rooms**: Complex logic scattered

**Frontend Technical Debt** (RESOLVED):

- ✅ Component organization resolved
- ✅ Import path consistency achieved
- ✅ Code duplication eliminated
- ✅ Utilities centralized
- ✅ Constants consolidated

### Performance Issues

**Backend**:

- Single massive quiz controller
- No file size limits
- N+1 query patterns
- No rate limiting

**Frontend** (IMPROVED):

- ✅ Better tree-shaking
- ✅ Optimized imports
- ✅ Code splitting support

## Future Considerations

### Scalability Improvements

1. **Microservices**: Break down massive controllers
2. **Database Optimization**: Query optimization, indexing
3. **Caching Strategy**: Enhanced Redis usage
4. **File Management**: Cloud storage migration
5. **Rate Limiting**: API protection implementation

### Architecture Evolution

1. **API Gateway**: Centralized API management
2. **Event-Driven Architecture**: Improved real-time features
3. **Monitoring**: Application performance monitoring
4. **CI/CD Pipeline**: Automated deployment
5. **Security Enhancements**: Advanced security measures

## API Architecture

### REST API Design

**Base URL**: `http://localhost:8888/api`

**Authentication**: Bearer JWT token trong Authorization header

#### Core API Endpoints

**Authentication & User Management**:

```
POST /api/users/login                    # User authentication
GET /api/users                           # List users (Admin)
POST /api/users/createStudent            # Create student (Admin/Teacher)
POST /api/users/createTeacher            # Create teacher (Admin)
PUT /api/users/:id                       # Update user
DELETE /api/users/:id                    # Delete user (Admin)
```

**Program & Course Management**:

```
GET /api/programs                        # List programs
POST /api/programs                       # Create program (Admin)
PUT /api/programs/:id                    # Update program (Admin)
DELETE /api/programs/:id                 # Delete program (Admin)
GET /api/courses                         # List courses
POST /api/courses                        # Create course (Admin)
GET /api/subjects                        # List subjects
GET /api/chapters/subject/:subject_id    # Chapters by subject
```

**Quiz Management** (Core System):

```
GET /api/quizzes                         # List quizzes
POST /api/quizzes                        # Create quiz
PUT /api/quizzes/:id                     # Update quiz
DELETE /api/quizzes/:id                  # Delete quiz
POST /api/quizzes/:id/start              # Start quiz (Teacher)
POST /api/quizzes/:id/join               # Join quiz (Student)
POST /api/quizzes/realtime/answer        # Submit answer (Real-time)
GET /api/quizzes/:id/leaderboard         # Live leaderboard
GET /api/quizzes/pin/:pin                # Find quiz by PIN
POST /api/quizzes/:id/end                # End quiz (Teacher)
```

**Question & Answer Management**:

```
GET /api/questions                       # List questions
POST /api/questions                      # Create question
PUT /api/questions/:id                   # Update question
DELETE /api/questions/:id                # Delete question
POST /api/questions/bulk                 # Bulk import questions
GET /api/answers/quiz/:quiz_id           # Quiz answers
```

**Analytics & Reporting**:

```
GET /api/analytics/quiz/:id              # Quiz analytics
GET /api/analytics/student/:id           # Student performance
GET /api/analytics/class/:id             # Class performance
GET /api/reports/export/:type            # Export reports
```

**Gamification**:

```
GET /api/gamification/leaderboard        # Global leaderboard
GET /api/gamification/achievements       # User achievements
GET /api/gamification/streaks            # Learning streaks
```

### API Response Format

**Success Response**:

```json
{
  "success": true,
  "data": { ... },
  "message": "Operation successful"
}
```

**Error Response**:

```json
{
  "success": false,
  "error": "Error message",
  "code": "ERROR_CODE"
}
```

### Real-time Events (Socket.IO)

**Quiz Events**:

```javascript
// Server to Client
'quiz:started'           # Quiz has started
'quiz:question'          # New question broadcast
'quiz:answer_received'   # Answer acknowledgment
'quiz:leaderboard'       # Updated leaderboard
'quiz:ended'             # Quiz completed

// Client to Server
'join_quiz'              # Join quiz room
'submit_answer'          # Submit answer
'leave_quiz'             # Leave quiz room
```

**Room Structure**:

- `quiz:{quiz_id}` - All participants
- `quiz:{quiz_id}:teachers` - Teachers only
- `quiz:{quiz_id}:students` - Students only

## Integration Architecture

### External Integrations

**Firebase Integration**:

- **Purpose**: Real-time database backup
- **Implementation**: Admin SDK
- **Location**: `backend/src/config/firebase.js`
- **Usage**: Participant data synchronization

**Email Service** (Future):

- **Purpose**: Notifications, reports
- **Status**: Not implemented
- **Recommendation**: SendGrid or AWS SES

### Internal Service Communication

**Frontend ↔ Backend**:

- **Protocol**: HTTP/HTTPS REST API
- **Authentication**: JWT Bearer tokens
- **Real-time**: Socket.IO WebSocket connection
- **Error Handling**: Axios interceptors

**Backend ↔ Database**:

- **ORM**: Sequelize with PostgreSQL
- **Connection Pool**: Max 5 connections (needs scaling)
- **Migrations**: Sequelize CLI managed
- **Backup**: Manual PostgreSQL dumps

**Backend ↔ Cache**:

- **Client**: Redis client
- **Usage**: Sessions, quiz state, leaderboards
- **Persistence**: RDB + AOF
- **Clustering**: Single instance (needs scaling)

## Monitoring & Observability

### Current Monitoring

**Application Logs**:

- **Frontend**: Browser console, Next.js logs
- **Backend**: Express console logs
- **Database**: PostgreSQL logs
- **Cache**: Redis logs

**Health Checks**:

- **Basic**: Application startup verification
- **Database**: Connection status
- **Cache**: Redis ping
- **Real-time**: Socket.IO connection status

### Recommended Monitoring

**Application Performance Monitoring (APM)**:

- **Recommendation**: New Relic, DataDog, or Sentry
- **Metrics**: Response times, error rates, throughput
- **Alerts**: Performance degradation, errors

**Infrastructure Monitoring**:

- **Recommendation**: Prometheus + Grafana
- **Metrics**: CPU, memory, disk, network
- **Dashboards**: Real-time system health

**Log Aggregation**:

- **Recommendation**: ELK Stack (Elasticsearch, Logstash, Kibana)
- **Centralized Logging**: All services in one place
- **Search & Analysis**: Log pattern analysis

---

**Document Status**: Current production architecture reflecting post-restructuring state
**Next Review**: Quarterly architecture review recommended
**Maintenance**: Update when significant changes occur
