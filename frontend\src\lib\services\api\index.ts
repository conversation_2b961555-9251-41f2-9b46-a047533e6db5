import api from "./client";
import authService from "./auth.service";
import userService from "./user.service";
import roleService from "./role.service";
import quizService from "./quiz.service";
import subjectService from "./subject.service";
import loService from "./lo.service";
import gamificationService from "./gamification.service";
import advancedAnalyticsService from "./advanced-analytics.service";
import chapterAnalyticsService from "./chapter-analytics.service";
import avatarService from "./avatar.service";

export {
  api,
  authService,
  userService,
  roleService,
  quizService,
  subjectService,
  loService,
  gamificationService,
  advancedAnalyticsService,
  chapterAnalyticsService,
  avatarService,
};

export default api;

// Re-export types from services
export type {
  TimeSeriesParams,
  ScoreDistributionParams,
  LearningOutcomesParams,
  DifficultyHeatmapParams,
  ActivityTimelineParams,
  StudentScoreAnalysisParams,
} from "./advanced-analytics.service";

export type {
  UserGamificationInfo,
  LeaderboardEntry,
  GamificationStats,
  AddPointsRequest,
} from "@/lib/types/gamification";

// Chapter Analytics types
export type {
  ChapterAnalysisData,
  ComprehensiveAnalysisData,
  TeacherAnalyticsData,
  ChapterAnalyticsResponse,
  DetailedAnalysisParams,
  ComprehensiveAnalysisParams,
  TeacherAnalyticsParams,
  SectionRecommendation,
} from "@/lib/types/chapter-analytics";
