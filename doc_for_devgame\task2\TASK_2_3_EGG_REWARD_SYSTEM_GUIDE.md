# Task 2.3: <PERSON><PERSON> thống Trứng thưởng & <PERSON><PERSON><PERSON> tầm - Implementation Guide

## Tổng quan
Task 2.3 triển khai hệ thống trứng thưởng hoàn chỉnh với 24 loại trứng kh<PERSON>c nhau, hệ thống phần thưởng dựa trên x<PERSON><PERSON>, và tích hợp với tất cả các hệ thống gamification hiện có.

## Kiến trúc hệ thống

### 1. Database Schema
- **EggTypes**: 24 loại trứng với độ hiếm khác nhau
- **EggRewards**: Phần thưởng có thể có trong từng loại trứng
- **UserEggs**: Trứng người dùng sở hữu
- **EggDropRules**: Quy tắc rơi trứng dựa trên trigger
- **EggOpeningHistory**: Lịch sử mở trứng và phần thưởng

### 2. Models
- `EggType`: <PERSON><PERSON><PERSON><PERSON> lý các loại trứng
- `EggReward`: <PERSON><PERSON><PERSON><PERSON> lý phần thưởng trong trứng
- `UserEgg`: <PERSON><PERSON><PERSON><PERSON> lý trứng của người dùng
- `EggDropRule`: Quản lý quy tắc rơi trứng
- `EggOpeningHistory`: Lịch sử mở trứng

### 3. Service Layer
- `EggRewardService`: Logic nghiệp vụ chính cho hệ thống trứng

### 4. API Endpoints
- `/api/eggs/*`: Tất cả endpoints liên quan đến trứng

## Tính năng chính

### 1. Hệ thống 24 loại trứng
```
COMMON (3 loại):
- Basic Egg, Cracked Egg, Spotted Egg

UNCOMMON (4 loại):
- Coconut Egg, Cat Egg, Dog Egg, Party Egg

RARE (5 loại):
- Ice Egg, Royal Egg, Sandcastle Egg, Mine Egg, Yeti Egg

EPIC (5 loại):
- Dragon Egg, Rainbow Egg, Angel Egg, Demon Egg, Kraken Egg

LEGENDARY (4 loại):
- Legendary Egg, Angel Demon Egg, Dominus Egg, Black Hole Egg

MYTHICAL (3 loại):
- Mythical Egg, Secret Egg, Glitched Egg
```

### 2. Hệ thống phần thưởng
- **Currency**: SynCoin, Kristal, XP
- **Items**: Avatar, Frame, Emoji, Name Effect
- **Duplicate Conversion**: Tự động chuyển đổi item trùng thành Kristal

### 3. Cơ chế rơi trứng
- **Quiz Completion**: Dựa trên điểm số và độ chính xác
- **Perfect Score**: Đảm bảo nhận trứng khi đạt điểm tuyệt đối
- **Streak Achievement**: Thưởng cho chuỗi thắng
- **Level Up**: Trứng thưởng khi lên cấp
- **Daily Login**: Trứng đăng nhập hàng ngày

### 4. Cửa hàng trứng
- Mua trứng bằng SynCoin hoặc Kristal
- Giá cả dựa trên độ hiếm
- Giới hạn số lượng mua

## API Documentation

### Inventory Management
```javascript
// Get user egg inventory
GET /api/eggs/inventory

// Response
{
  "success": true,
  "data": {
    "inventory": [
      {
        "egg_type": { /* EggType info */ },
        "quantity": 3,
        "eggs": [ /* UserEgg objects */ ],
        "latest_obtained": "2024-01-15T10:30:00Z"
      }
    ],
    "total_egg_types": 5,
    "total_eggs": 12
  }
}
```

### Egg Opening
```javascript
// Open an egg
POST /api/eggs/open
{
  "user_egg_id": 123
}

// Response
{
  "success": true,
  "data": {
    "egg_type": { /* EggType info */ },
    "rewards": [
      {
        "reward_type": "SYNCOIN",
        "reward_amount": 100,
        "was_duplicate": false,
        "kristal_compensation": 0
      }
    ],
    "summary": {
      "total_rewards": 2,
      "syncoin_earned": 100,
      "kristal_earned": 15,
      "items_received": 1,
      "duplicates_converted": 5
    }
  }
}
```

### Shop System
```javascript
// Get egg shop
GET /api/eggs/shop

// Purchase egg
POST /api/eggs/purchase
{
  "egg_type_id": 1,
  "quantity": 2
}
```

### Egg Awarding (Internal)
```javascript
// Award eggs based on triggers
POST /api/eggs/award
{
  "trigger_type": "QUIZ_COMPLETION",
  "trigger_data": {
    "score": 85,
    "min_correct": 8,
    "total_questions": 10
  }
}
```

## Integration với các hệ thống khác

### 1. Quiz System
```javascript
// Trong QuizResultController, sau khi lưu kết quả:
const eggResult = await EggRewardService.awardEggByTrigger(
  userId, 
  'QUIZ_COMPLETION', 
  {
    score: finalScore,
    min_correct: correctAnswers,
    total_questions: totalQuestions
  }
);
```

### 2. Level System
```javascript
// Trong User model, khi lên cấp:
if (leveledUp) {
  await EggRewardService.awardEggByTrigger(
    this.user_id,
    'LEVEL_UP',
    { level_milestone: newLevel }
  );
}
```

### 3. Currency System
- Tự động tích hợp với CurrencyService
- Duplicate items tự động chuyển thành Kristal
- Mua trứng tự động trừ tiền

### 4. Avatar System
- Items từ trứng tự động thêm vào UserInventory
- Tích hợp với AvatarCustomizationService

## Cài đặt và triển khai

### 1. Chạy SQL Schema
```sql
-- Chạy file SQL để tạo tables và data
\i doc_for_devgame/task2/gamification_egg_system.sql
```

### 2. Restart Server
```bash
# Restart Node.js server để load models mới
npm restart
```

### 3. Test API
```bash
# Test với Postman hoặc curl
curl -H "Authorization: Bearer YOUR_TOKEN" \
     -X GET http://localhost:3000/api/eggs/inventory
```

## Tính năng nâng cao

### 1. Probability System
- Drop rates được tính toán chính xác
- Weighted random selection
- Guaranteed rewards cho một số trường hợp

### 2. Daily Limits
- Giới hạn số trứng nhận được mỗi ngày
- Reset tự động vào 00:00

### 3. Analytics
- Tracking lịch sử mở trứng
- Thống kê phần thưởng
- Views cho báo cáo

### 4. Anti-Duplicate System
- Tự động detect items trùng
- Chuyển đổi thành Kristal với tỷ lệ hợp lý
- Thông báo cho user về conversion

## 🥚 **Cơ chế Mở Trứng - Chi tiết**

### **1. 🎯 Probability-Based Reward System**

Mỗi loại trứng có nhiều phần thưởng với **tỉ lệ rơi khác nhau**:

```sql
-- Basic Egg Rewards (ID: 1)
(1, 'SYNCOIN', NULL, 50, 0.4000, 1),   -- 40% chance for 50 SynCoin
(1, 'SYNCOIN', NULL, 100, 0.2000, 1),  -- 20% chance for 100 SynCoin
(1, 'XP', NULL, 25, 0.3000, 1),        -- 30% chance for 25 XP
(1, 'AVATAR', 1, 1, 0.0500, 2),        -- 5% chance for Dog avatar
(1, 'EMOJI', 1, 1, 0.0500, 2),         -- 5% chance for basic emoji
```

### **2. 🎲 Weighted Random Selection**

Hệ thống sử dụng **trọng số độ hiếm** (`rarity_weight`) để tính toán:

```javascript
// Trong EggRewardService.js
const totalWeight = rewards.reduce((sum, reward) => sum + reward.rarity_weight, 0);
const randomValue = Math.random() * totalWeight;

let currentWeight = 0;
for (const reward of rewards) {
    currentWeight += reward.rarity_weight;
    if (randomValue <= currentWeight) {
        selectedReward = reward;
        break;
    }
}
```

### **3. 🔄 Duplicate Detection & Conversion**

Khi mở trứng, hệ thống kiểm tra duplicate:

```javascript
// Kiểm tra duplicate trong UserInventory
const existingItem = await UserInventory.findOne({
    where: {
        user_id: userId,
        item_type: reward.reward_type,
        item_id: reward.reward_item_id,
        is_equipped: false
    }
});

if (existingItem) {
    // Convert duplicate thành Kristal
    const kristalValue = this.calculateKristalValue(reward);
    await CurrencyService.awardCurrency(userId, 'KRIS', kristalValue, 'ITEM_DECOMPOSE');

    duplicateConversions.push({
        item_type: reward.reward_type,
        item_name: reward.item_name,
        kristal_earned: kristalValue
    });
}
```

### **4. 💎 Kristal Conversion Rates**

Duplicate items được convert theo tỉ lệ:

```javascript
calculateKristalValue(reward) {
    const conversionRates = {
        'AVATAR': 10,      // Avatar duplicate = 10 Kristal
        'FRAME': 8,        // Frame duplicate = 8 Kristal
        'EMOJI': 5,        // Emoji duplicate = 5 Kristal
        'NAME_EFFECT': 15  // Name Effect duplicate = 15 Kristal
    };
    return conversionRates[reward.reward_type] || 1;
}
```

### **5. 📊 Multiple Rewards System**

Một trứng có thể cho **nhiều phần thưởng**:

```javascript
// Lấy tất cả rewards có thể có
const availableRewards = eggType.Rewards.filter(reward => reward.is_active);

// Random số lượng rewards (1-3 items)
const numRewards = Math.min(
    Math.floor(Math.random() * 3) + 1,
    availableRewards.length
);

// Select multiple rewards
for (let i = 0; i < numRewards; i++) {
    const selectedReward = this.selectRewardByWeight(availableRewards);
    rewardsReceived.push(selectedReward);
}
```

### **6. 🎁 Reward Types & Processing**

Hệ thống xử lý 7 loại phần thưởng:

```javascript
switch (reward.reward_type) {
    case 'AVATAR':
        // Thêm avatar vào UserInventory
        await UserInventory.create({
            user_id: userId,
            item_type: 'AVATAR',
            item_id: reward.reward_item_id,
            quantity: 1
        });
        break;

    case 'SYNCOIN':
    case 'KRISTAL':
        // Thêm currency
        await CurrencyService.awardCurrency(
            userId,
            reward.reward_type === 'SYNCOIN' ? 'SYNC' : 'KRIS',
            reward.reward_amount,
            'EGG_REWARD'
        );
        break;

    case 'XP':
        // Thêm XP vào gamification system
        await GamificationService.addExperience(userId, reward.reward_amount);
        break;
}
```

### **7. 📝 Opening History Tracking**

Mỗi lần mở trứng được ghi lại:

```javascript
await EggOpeningHistory.create({
    user_id: userId,
    egg_type_id: eggType.egg_type_id,
    rewards_received: rewardsReceived,
    total_value_syncoin: totalSyncoinValue,
    total_value_kristal: totalKristalValue,
    was_duplicate: duplicateConversions.length > 0,
    kristal_from_duplicates: totalKristalFromDuplicates
});
```

### **8. 🎯 Rarity-Based Probability**

Trứng hiếm hơn có tỉ lệ rewards tốt hơn:

- **COMMON**: 40% currency, 30% XP, 5% items
- **RARE**: 25% currency, 20% XP, 15% items
- **LEGENDARY**: 10% currency, 10% XP, 40% items
- **MYTHICAL**: 5% currency, 5% XP, 60% items

### **9. 🎯 Tóm tắt Cơ chế:**

1. **Weighted Random** - Chọn rewards theo trọng số
2. **Multiple Rewards** - Có thể nhận 1-3 phần thưởng
3. **Duplicate Protection** - Tự động convert duplicate thành Kristal
4. **Rarity Scaling** - Trứng hiếm = rewards tốt hơn
5. **History Tracking** - Ghi lại mọi lần mở trứng
6. **Guaranteed System** - Một số rewards đảm bảo 100%

## Testing Scenarios

### 1. Basic Flow
1. User hoàn thành quiz → Nhận trứng
2. Mở trứng → Nhận rewards
3. Duplicate item → Chuyển thành Kristal

### 2. Shop Flow
1. Check balance → Mua trứng
2. Trừ tiền → Thêm trứng vào inventory
3. Mở trứng → Nhận rewards

### 3. Integration Flow
1. Level up → Trigger egg drop
2. Perfect score → Guaranteed egg
3. Daily login → Daily egg

## Lưu ý quan trọng

1. **Performance**: Sử dụng indexes cho queries thường xuyên
2. **Security**: Validate tất cả inputs
3. **Consistency**: Transaction cho operations quan trọng
4. **Scalability**: Pagination cho large datasets
5. **Monitoring**: Log tất cả egg operations

## Next Steps
- Tích hợp với frontend UI
- Thêm animations cho egg opening
- Implement push notifications
- Add more egg types theo feedback

---
**Status**: ✅ Completed - Ready for testing
**Dependencies**: Currency System, Avatar System, User System
**API Docs**: Available at `/api/eggs/docs`
