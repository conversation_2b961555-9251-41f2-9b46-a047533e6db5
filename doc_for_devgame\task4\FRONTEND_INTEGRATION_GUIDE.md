# 🎮 Quiz Racing & Skills System - Frontend Integration Guide

## 📋 Tổng quan hệ thống

Hệ thống Quiz Racing với Skills đã được hoàn thiện bao gồm:
- **17 Skills** chia thành 5 categories (Attack, Defense, Burst, Special, Ultimate)
- **Real-time Quiz Racing** với WebSocket
- **Energy System** với mechanics phức tạp
- **Skill Loadout Management** (4 skills per session)
- **Racing Analytics** và leaderboard real-time

---

## 🗂️ Cấu trúc file Backend

### Core Services
```
backend/src/services/
├── skillService.js              # Skill management logic
├── quizRacingService.js         # Real-time racing logic
└── quizRealtimeService.js       # Existing quiz service
```

### Controllers
```
backend/src/controllers/
├── skillController.js           # Skill REST API endpoints
├── quizRacingController.js      # WebSocket event handlers
└── quizController.js            # Existing quiz controller
```

### Routes
```
backend/src/routes/
├── skillRoutes.js               # /api/skills/*
├── quizRacingRoutes.js          # /api/quiz-racing/*
└── quizRoutes.js                # Existing quiz routes
```

### Models
```
backend/src/models/
├── skill.js                     # Skills table
├── userSkill.js                 # User skill inventory
├── quizSkillLoadout.js          # 4-skill loadouts per session
├── skillUsageHistory.js         # Skill usage tracking
└── activeSkillEffect.js         # Active effects during racing
```

### Database Schema
```
doc_for_devgame/task4/
├── skills_system.sql            # Complete skills schema + data
└── quiz_racing_schema_updates.sql # Racing-specific tables
```

---

## 🔌 API Endpoints Overview

### 1. Skills Management APIs

#### Base URL: `/api/skills`

| Method | Endpoint                   | Description                     | Auth Required |
| ------ | -------------------------- | ------------------------------- | ------------- |
| GET    | `/shop`                    | Browse available skills in shop | ✅ Student+    |
| GET    | `/shop/category/:category` | Get skills by category          | ✅ Student+    |
| POST   | `/purchase`                | Purchase a skill                | ✅ Student+    |
| GET    | `/inventory`               | Get user's owned skills         | ✅ Student+    |
| GET    | `/inventory/equipped`      | Get currently equipped skills   | ✅ Student+    |
| POST   | `/loadout`                 | Set skill loadout (4 skills)    | ✅ Student+    |
| GET    | `/loadout`                 | Get current loadout             | ✅ Student+    |
| POST   | `/execute`                 | Execute a skill (in quiz)       | ✅ Student+    |
| GET    | `/usage-history`           | Get skill usage history         | ✅ Student+    |
| GET    | `/stats`                   | Get skill statistics            | ✅ Student+    |
| GET    | `/effects/active`          | Get active skill effects        | ✅ Student+    |
| POST   | `/effects/clear`           | Clear expired effects           | ✅ Student+    |
| GET    | `/categories`              | Get all skill categories        | ✅ Student+    |

### 2. Quiz Racing APIs

#### Base URL: `/api/quiz-racing`

| Method | Endpoint              | Description               | Auth Required |
| ------ | --------------------- | ------------------------- | ------------- |
| POST   | `/initialize`         | Initialize racing session | ✅ Student+    |
| GET    | `/session/:sessionId` | Get session data          | ✅ Student+    |
| POST   | `/loadout`            | Set racing loadout        | ✅ Student+    |
| GET    | `/loadout/:sessionId` | Get session loadout       | ✅ Student+    |
| GET    | `/stats/:sessionId`   | Get racing statistics     | ✅ Student+    |

---

## 📡 WebSocket Events

### Connection Setup
```javascript
const socket = io('http://localhost:8888', {
  transports: ['websocket', 'polling']
});
```

### Client → Server Events

| Event Name             | Description                 | Payload                                                             |
| ---------------------- | --------------------------- | ------------------------------------------------------------------- |
| `join-quiz-racing`     | Join racing session         | `{quiz_session_id, user_id, username}`                              |
| `submit-racing-answer` | Submit answer in racing     | `{quiz_session_id, user_id, question_id, answer_id, response_time}` |
| `use-skill`            | Execute skill               | `{quiz_session_id, user_id, skill_id, target_user_id?}`             |
| `skip-question`        | Skip current question       | `{quiz_session_id, user_id, question_id}`                           |
| `get-random-skill`     | Request random skill        | `{quiz_session_id, user_id}`                                        |
| `player-ready`         | Signal ready for next round | `{quiz_session_id, user_id}`                                        |

### Server → Client Events

| Event Name                | Description           | Payload                                                                                 |
| ------------------------- | --------------------- | --------------------------------------------------------------------------------------- |
| `quiz-racing-initialized` | Session created       | `{session_id, participants, total_questions}`                                           |
| `energy-update`           | Energy changed        | `{user_id, energy_percent, energy_gain, skill_available}`                               |
| `skill-available`         | Random skill selected | `{user_id, skill, energy_percent}`                                                      |
| `skill-executed`          | Skill was used        | `{executor_id, skill, target_id, effect_data, message}`                                 |
| `racing-answer-result`    | Answer result         | `{question_id, is_correct, points_earned, total_score, current_streak, energy_percent}` |
| `leaderboard-update`      | Real-time rankings    | `{session_id, leaderboard}`                                                             |
| `effect-applied`          | Effect activated      | `{target_id, effect_type, duration, effect_data}`                                       |
| `effect-expired`          | Effect ended          | `{user_id, effect_type}`                                                                |

---

## 🎯 Skills System Details

### Skill Categories

#### 1. Attack Skills (4)
```json
[
  {
    "skill_id": 1,
    "skill_name": "Blackhole",
    "category": "ATTACK",
    "cost_syncoin": 100,
    "effect": "Target leader gets 0 points for 3 questions",
    "target_type": "LEADER"
  },
  {
    "skill_id": 2,
    "skill_name": "Steal",
    "category": "ATTACK", 
    "cost_syncoin": 150,
    "effect": "Steal 50% points from player above (10% risk)",
    "target_type": "PLAYER_ABOVE",
    "risk_factor": 0.10
  }
]
```

#### 2. Defense Skills (3)
```json
[
  {
    "skill_id": 5,
    "skill_name": "Shield",
    "category": "DEFENSE",
    "cost_syncoin": 120,
    "effect": "Immunity to attacks for 45 seconds",
    "duration_seconds": 45
  }
]
```

#### 3. Burst Skills (5)
```json
[
  {
    "skill_id": 8,
    "skill_name": "Double",
    "category": "BURST",
    "cost_syncoin": 100,
    "effect": "×2 points next question (25% risk)",
    "risk_factor": 0.25
  }
]
```

#### 4. Special Skills (3)
```json
[
  {
    "skill_id": 13,
    "skill_name": "Swap",
    "category": "SPECIAL",
    "cost_kristal": 100,
    "effect": "Exchange total points with random player"
  }
]
```

#### 5. Ultimate Skills (2)
```json
[
  {
    "skill_id": 16,
    "skill_name": "King",
    "category": "ULTIMATE",
    "cost_kristal": 200,
    "effect": "×2 points + immunity + guaranteed correct for 3 questions"
  }
]
```

### Energy System Rules

```javascript
// Energy calculation
const energyGain = {
  base: 20,           // +20% for correct answers
  speedBonus: 10,     // +10% if response_time < 5000ms
  streakBonus: 5      // +5% if current_streak >= 3
};

// Total energy = base + speedBonus + streakBonus
// 100% energy triggers skill selection
// Energy resets to 0% after skill usage
```

---

## 🧪 Testing với Postman

### 1. Authentication Setup
```javascript
// Pre-request Script
pm.globals.set("baseUrl", "http://localhost:8888/api");

// Login first
pm.sendRequest({
    url: pm.globals.get("baseUrl") + "/auth/login",
    method: 'POST',
    header: {'Content-Type': 'application/json'},
    body: {
        mode: 'raw',
        raw: JSON.stringify({
            username: "student1",
            password: "password123"
        })
    }
}, function (err, res) {
    if (res.json().success) {
        pm.globals.set("authToken", res.json().data.token);
    }
});
```

### 2. Skills API Tests

#### Get Skills Shop
```http
GET {{baseUrl}}/skills/shop
Authorization: Bearer {{authToken}}
```

#### Purchase Skill
```http
POST {{baseUrl}}/skills/purchase
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "skill_id": 1
}
```

#### Set Skill Loadout
```http
POST {{baseUrl}}/skills/loadout
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "skill_ids": [1, 5, 8, 13]
}
```

### 3. Quiz Racing API Tests

#### Initialize Racing Session
```http
POST {{baseUrl}}/quiz-racing/initialize
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "quiz_id": 1,
  "participants": [
    {"user_id": 1, "username": "student1"},
    {"user_id": 2, "username": "student2"}
  ]
}
```

#### Set Racing Loadout
```http
POST {{baseUrl}}/quiz-racing/loadout
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "quiz_session_id": "racing_1_1640995200000",
  "skill_ids": [1, 5, 8, 13]
}
```

### 4. WebSocket Testing với Postman

#### Connect to WebSocket
```javascript
// Use Postman WebSocket feature
// URL: ws://localhost:8888/socket.io/?EIO=4&transport=websocket

// Send join event
{
  "type": "join-quiz-racing",
  "data": {
    "quiz_session_id": "racing_1_1640995200000",
    "user_id": 1,
    "username": "student1"
  }
}

// Send skill usage
{
  "type": "use-skill",
  "data": {
    "quiz_session_id": "racing_1_1640995200000",
    "user_id": 1,
    "skill_id": 1,
    "target_user_id": 2
  }
}
```

---

## 🔧 Frontend Integration Examples

### 1. Skills Shop Component
```typescript
// services/skillsApi.ts
export const skillsApi = {
  getShop: () => api.get('/skills/shop'),
  purchaseSkill: (skillId: number) => 
    api.post('/skills/purchase', { skill_id: skillId }),
  getInventory: () => api.get('/skills/inventory'),
  setLoadout: (skillIds: number[]) => 
    api.post('/skills/loadout', { skill_ids: skillIds })
};

// components/SkillsShop.tsx
const SkillsShop = () => {
  const [skills, setSkills] = useState([]);
  
  useEffect(() => {
    skillsApi.getShop().then(res => setSkills(res.data.data));
  }, []);
  
  const handlePurchase = async (skillId: number) => {
    try {
      await skillsApi.purchaseSkill(skillId);
      // Refresh inventory
    } catch (error) {
      console.error('Purchase failed:', error);
    }
  };
};
```

### 2. Quiz Racing Component
```typescript
// services/quizRacingApi.ts
export const quizRacingApi = {
  initializeSession: (quizId: number, participants: any[]) =>
    api.post('/quiz-racing/initialize', { quiz_id: quizId, participants }),
  getSessionData: (sessionId: string) =>
    api.get(`/quiz-racing/session/${sessionId}`),
  setRacingLoadout: (sessionId: string, skillIds: number[]) =>
    api.post('/quiz-racing/loadout', { 
      quiz_session_id: sessionId, 
      skill_ids: skillIds 
    })
};

// hooks/useQuizRacing.ts
export const useQuizRacing = (sessionId: string) => {
  const [socket, setSocket] = useState<Socket | null>(null);
  const [energy, setEnergy] = useState(0);
  const [availableSkill, setAvailableSkill] = useState(null);
  
  useEffect(() => {
    const newSocket = io('http://localhost:8888');
    setSocket(newSocket);
    
    // Join racing session
    newSocket.emit('join-quiz-racing', {
      quiz_session_id: sessionId,
      user_id: userId,
      username: username
    });
    
    // Listen for energy updates
    newSocket.on('energy-update', (data) => {
      setEnergy(data.energy_percent);
      if (data.skill_available) {
        // Show skill selection UI
      }
    });
    
    // Listen for skill availability
    newSocket.on('skill-available', (data) => {
      setAvailableSkill(data.skill);
    });
    
    return () => newSocket.close();
  }, [sessionId]);
  
  const useSkill = (skillId: number, targetUserId?: number) => {
    socket?.emit('use-skill', {
      quiz_session_id: sessionId,
      user_id: userId,
      skill_id: skillId,
      target_user_id: targetUserId
    });
  };
  
  return { energy, availableSkill, useSkill };
};
```

### 3. Energy & Skills UI
```typescript
// components/EnergyBar.tsx
const EnergyBar = ({ energy, onSkillSelect }: { 
  energy: number, 
  onSkillSelect: () => void 
}) => {
  return (
    <div className="energy-container">
      <div className="energy-bar">
        <div 
          className="energy-fill" 
          style={{ width: `${energy}%` }}
        />
      </div>
      <span>{energy}%</span>
      {energy >= 100 && (
        <button 
          className="skill-trigger-btn"
          onClick={onSkillSelect}
        >
          🎯 Select Skill
        </button>
      )}
    </div>
  );
};

// components/SkillLoadout.tsx
const SkillLoadout = ({ skills, onSkillUse }: {
  skills: Skill[],
  onSkillUse: (skillId: number, targetId?: number) => void
}) => {
  return (
    <div className="skill-loadout">
      {skills.map((skill, index) => (
        <div key={skill.skill_id} className="skill-slot">
          <img src={`/skills/${skill.skill_code}.png`} alt={skill.skill_name} />
          <span>{skill.skill_name}</span>
          <button 
            onClick={() => onSkillUse(skill.skill_id)}
            disabled={skill.cooldown > 0}
          >
            Use
          </button>
        </div>
      ))}
    </div>
  );
};
```

---

## 📁 File Test Postman Collection

Tạo file `Quiz_Racing_Skills.postman_collection.json`:

```json
{
  "info": {
    "name": "Quiz Racing & Skills API",
    "description": "Complete API collection for Skills and Quiz Racing system"
  },
  "variable": [
    {
      "key": "baseUrl",
      "value": "http://localhost:8888/api"
    },
    {
      "key": "authToken",
      "value": ""
    }
  ],
  "item": [
    {
      "name": "Authentication",
      "item": [
        {
          "name": "Login",
          "request": {
            "method": "POST",
            "url": "{{baseUrl}}/auth/login",
            "body": {
              "mode": "raw",
              "raw": "{\n  \"username\": \"student1\",\n  \"password\": \"password123\"\n}"
            }
          }
        }
      ]
    },
    {
      "name": "Skills Management",
      "item": [
        {
          "name": "Get Skills Shop",
          "request": {
            "method": "GET",
            "url": "{{baseUrl}}/skills/shop",
            "header": [
              {
                "key": "Authorization",
                "value": "Bearer {{authToken}}"
              }
            ]
          }
        },
        {
          "name": "Purchase Skill",
          "request": {
            "method": "POST",
            "url": "{{baseUrl}}/skills/purchase",
            "header": [
              {
                "key": "Authorization",
                "value": "Bearer {{authToken}}"
              }
            ],
            "body": {
              "mode": "raw",
              "raw": "{\n  \"skill_id\": 1\n}"
            }
          }
        }
      ]
    }
  ]
}
```

---

## 🚀 Quick Start Guide

### 1. Database Setup
```sql
-- Run skills system schema
\i doc_for_devgame/task4/skills_system.sql

-- Run racing schema updates  
\i doc_for_devgame/task4/quiz_racing_schema_updates.sql
```

### 2. Test API
```bash
# Run test script
cd doc_for_devgame/task4
node test_quiz_racing_api.js
```

### 3. Import Postman Collection
1. Import `Quiz_Racing_Skills.postman_collection.json`
2. Set environment variables
3. Run authentication first
4. Test all endpoints

---

## ⚠️ Important Notes

### Environment Configuration
- **Local Development**: `REDIS_HOST=localhost`, `DB_HOST=localhost`
- **Docker**: `REDIS_HOST=redis`, `DB_HOST=postgres`

### Error Handling
- All APIs return consistent format: `{success: boolean, message: string, data?: any}`
- WebSocket events include error handling with `error` event
- Skills validation includes ownership, energy, and cooldown checks

### Performance Considerations
- Redis caching for session data (2-hour TTL)
- Batch WebSocket emissions for multiple participants
- Efficient skill lookup with category indexing

---

## 📦 Files Package cho Frontend Team

### 1. Documentation Files
```
doc_for_devgame/task4/
├── FRONTEND_INTEGRATION_GUIDE.md           # File này - Hướng dẫn tổng hợp
├── quiz_racing_api_documentation.md        # Chi tiết API documentation
├── skills_system.sql                       # Database schema + data
├── quiz_racing_schema_updates.sql          # Racing tables schema
└── test_quiz_racing_api.js                 # Node.js test script
```

### 2. Postman Testing Files
```
doc_for_devgame/task4/
├── Quiz_Racing_Skills.postman_collection.json    # Complete API collection
└── Quiz_Racing_Environment.postman_environment.json # Environment variables
```

### 3. Backend Implementation Files
```
backend/src/
├── services/
│   ├── skillService.js                     # Skills business logic
│   └── quizRacingService.js               # Racing business logic
├── controllers/
│   ├── skillController.js                 # Skills REST endpoints
│   └── quizRacingController.js            # WebSocket event handlers
├── routes/
│   ├── skillRoutes.js                     # /api/skills/* routes
│   └── quizRacingRoutes.js               # /api/quiz-racing/* routes
└── models/
    ├── skill.js                           # Skills table model
    ├── userSkill.js                       # User inventory model
    ├── quizSkillLoadout.js               # Loadout model
    ├── skillUsageHistory.js              # Usage tracking model
    └── activeSkillEffect.js              # Active effects model
```

---

## 🚀 Quick Setup cho Frontend

### 1. Import Postman Collection
1. Mở Postman
2. Import `Quiz_Racing_Skills.postman_collection.json`
3. Import `Quiz_Racing_Environment.postman_environment.json`
4. Chọn environment "Quiz Racing & Skills Environment"
5. Chạy "Login Student1" để lấy auth token
6. Test các endpoints khác

### 2. Database Setup (Backend team)
```bash
# Chạy skills schema
psql -d HETHONGTRACNGHIEM -f doc_for_devgame/task4/skills_system.sql

# Chạy racing schema
psql -d HETHONGTRACNGHIEM -f doc_for_devgame/task4/quiz_racing_schema_updates.sql
```

### 3. Test API Connection
```bash
# Test với Node.js script
cd doc_for_devgame/task4
node test_quiz_racing_api.js
```

---

## 📞 Support & Contact

### Backend Implementation Status
- ✅ **Skills System**: Hoàn thành 100% (17 skills, 5 categories)
- ✅ **Quiz Racing**: Hoàn thành 100% (WebSocket, energy, real-time)
- ✅ **Database Schema**: Hoàn thành 100% (6 tables + procedures)
- ✅ **API Endpoints**: Hoàn thành 100% (13 skills + 5 racing endpoints)
- ✅ **Testing**: Hoàn thành 100% (Postman + Node.js scripts)

### Next Steps cho Frontend
1. **Skills Shop UI**: Hiển thị 17 skills theo categories
2. **Loadout Management**: UI chọn 4 skills cho racing
3. **Racing Interface**: Real-time energy bar, skill buttons
4. **WebSocket Integration**: Kết nối Socket.IO cho real-time events
5. **Effects Display**: Hiển thị active effects và timers

### Technical Support
- **API Issues**: Check Postman collection tests
- **WebSocket Issues**: Verify Socket.IO connection
- **Database Issues**: Run schema files
- **Authentication**: Use provided login endpoints

Hệ thống đã sẵn sàng cho frontend integration! 🎉
