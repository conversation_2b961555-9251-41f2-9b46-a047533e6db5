{"compilerOptions": {"target": "ES2017", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./src/*"], "@/components/*": ["./src/components/*"], "@/components/ui/*": ["./src/components/ui/*"], "@/components/features/*": ["./src/components/features/*"], "@/lib/*": ["./src/lib/*"], "@/lib/hooks/*": ["./src/lib/hooks/*"], "@/lib/services/*": ["./src/lib/services/*"], "@/lib/types/*": ["./src/lib/types/*"], "@/lib/utils/*": ["./src/lib/utils/*"], "@/lib/auth/*": ["./src/lib/auth/*"], "@/lib/validations/*": ["./src/lib/validations/*"], "@/lib/constants/*": ["./src/lib/constants/*"], "@/app/*": ["./src/app/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}