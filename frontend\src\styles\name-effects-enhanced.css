/* Name Effects - Enhanced Visibility & Clarity */
/* Maximum contrast, bold colors, smooth animations */

/* ===== CORE PRINCIPLES ===== */
/* 1. MAXIMUM CONTRAST - Bold, vibrant colors */
/* 2. C<PERSON>AR TIER DIFFERENTIATION - Each tier stands out */
/* 3. SMOOTH PREMIUM ANIMATIONS - Elegant movement */
/* 4. PERFECT READABILITY - Always crystal clear */

/* ===== SILVER TIER - Bold Silver ===== */

.name-effect-silver-basic {
  color: #374151;
  font-weight: 700;
  transition: all 0.3s ease;
}

.name-effect-silver-basic:hover {
  color: #111827;
  transform: translateY(-1px) scale(1.02);
}

.dark .name-effect-silver-basic {
  color: #f3f4f6;
  font-weight: 700;
}

.dark .name-effect-silver-basic:hover {
  color: #ffffff;
}

.name-effect-silver-bright {
  color: #4b5563;
  font-weight: 800;
  animation: silver-shine 3s ease-in-out infinite;
}

.dark .name-effect-silver-bright {
  color: #f9fafb;
  font-weight: 800;
  animation: silver-shine-dark 3s ease-in-out infinite;
}

@keyframes silver-shine {
  0%,
  100% {
    color: #4b5563;
    transform: translateY(0px);
  }
  50% {
    color: #1f2937;
    transform: translateY(-1.5px) scale(1.03);
  }
}

@keyframes silver-shine-dark {
  0%,
  100% {
    color: #f9fafb;
    transform: translateY(0px);
  }
  50% {
    color: #ffffff;
    transform: translateY(-1.5px) scale(1.03);
  }
}

/* ===== GOLD TIER - Rich Gold ===== */

.name-effect-gold-basic {
  color: #b45309;
  font-weight: 700;
  transition: all 0.3s ease;
}

.name-effect-gold-basic:hover {
  color: #92400e;
  transform: translateY(-1px) scale(1.03);
}

.dark .name-effect-gold-basic {
  color: #f59e0b;
  font-weight: 700;
}

.dark .name-effect-gold-basic:hover {
  color: #fbbf24;
}

.name-effect-gold-bold {
  color: #92400e;
  font-weight: 800;
  letter-spacing: 0.5px;
  animation: gold-radiance 2.5s ease-in-out infinite;
}

.dark .name-effect-gold-bold {
  color: #fbbf24;
  font-weight: 800;
  animation: gold-radiance-dark 2.5s ease-in-out infinite;
}

@keyframes gold-radiance {
  0%,
  100% {
    color: #92400e;
    transform: scale(1);
  }
  50% {
    color: #d97706;
    transform: scale(1.04) translateY(-1px);
  }
}

@keyframes gold-radiance-dark {
  0%,
  100% {
    color: #fbbf24;
    transform: scale(1);
  }
  50% {
    color: #fcd34d;
    transform: scale(1.04) translateY(-1px);
  }
}

/* ===== PLATINUM TIER - Pure Platinum ===== */

.name-effect-platinum-basic {
  color: #374151;
  font-weight: 800;
  transition: all 0.3s ease;
}

.name-effect-platinum-basic:hover {
  color: #1f2937;
  transform: translateY(-1px) scale(1.02);
}

.dark .name-effect-platinum-basic {
  color: #f3f4f6;
  font-weight: 800;
}

.dark .name-effect-platinum-basic:hover {
  color: #ffffff;
}

.name-effect-platinum-glow {
  color: #1f2937;
  font-weight: 800;
  animation: platinum-elegance 4s ease-in-out infinite;
}

.dark .name-effect-platinum-glow {
  color: #ffffff;
  font-weight: 800;
  animation: platinum-elegance-dark 4s ease-in-out infinite;
}

@keyframes platinum-elegance {
  0%,
  100% {
    color: #1f2937;
    transform: translateX(0px);
  }
  25% {
    color: #374151;
    transform: translateX(1px) translateY(-0.5px);
  }
  75% {
    color: #374151;
    transform: translateX(-1px) translateY(-0.5px);
  }
}

@keyframes platinum-elegance-dark {
  0%,
  100% {
    color: #ffffff;
    transform: translateX(0px);
  }
  25% {
    color: #f9fafb;
    transform: translateX(1px) translateY(-0.5px);
  }
  75% {
    color: #f9fafb;
    transform: translateX(-1px) translateY(-0.5px);
  }
}

/* ===== ONYX TIER - Strong Contrast ===== */

.name-effect-onyx-basic {
  color: #000000;
  font-weight: 800;
  -webkit-text-stroke: 1px rgba(255, 255, 255, 0.6);
  transition: all 0.3s ease;
}

.name-effect-onyx-basic:hover {
  transform: translateY(-1px) scale(1.02);
  -webkit-text-stroke: 1px rgba(255, 255, 255, 0.8);
}

.dark .name-effect-onyx-basic {
  color: #ffffff;
  font-weight: 800;
  -webkit-text-stroke: 1px rgba(0, 0, 0, 0.4);
}

.dark .name-effect-onyx-basic:hover {
  -webkit-text-stroke: 1px rgba(0, 0, 0, 0.6);
}

.name-effect-onyx-glow {
  color: #000000;
  font-weight: 800;
  -webkit-text-stroke: 1px rgba(255, 255, 255, 0.7);
  animation: onyx-power 5s ease-in-out infinite;
}

.dark .name-effect-onyx-glow {
  color: #ffffff;
  font-weight: 800;
  -webkit-text-stroke: 1px rgba(0, 0, 0, 0.5);
  animation: onyx-power-dark 5s ease-in-out infinite;
}

@keyframes onyx-power {
  0%,
  100% {
    transform: translate(0px, 0px);
    -webkit-text-stroke: 1px rgba(255, 255, 255, 0.7);
  }
  25% {
    transform: translate(1px, -0.5px);
    -webkit-text-stroke: 1px rgba(255, 255, 255, 0.9);
  }
  50% {
    transform: translate(0px, -1px);
    -webkit-text-stroke: 1px rgba(255, 255, 255, 1);
  }
  75% {
    transform: translate(-1px, -0.5px);
    -webkit-text-stroke: 1px rgba(255, 255, 255, 0.9);
  }
}

@keyframes onyx-power-dark {
  0%,
  100% {
    transform: translate(0px, 0px);
    -webkit-text-stroke: 1px rgba(0, 0, 0, 0.5);
  }
  25% {
    transform: translate(1px, -0.5px);
    -webkit-text-stroke: 1px rgba(0, 0, 0, 0.7);
  }
  50% {
    transform: translate(0px, -1px);
    -webkit-text-stroke: 1px rgba(0, 0, 0, 0.8);
  }
  75% {
    transform: translate(-1px, -0.5px);
    -webkit-text-stroke: 1px rgba(0, 0, 0, 0.7);
  }
}

/* ===== SAPPHIRE TIER - Deep Blue ===== */

.name-effect-sapphire-basic {
  color: #1e3a8a;
  font-weight: 800;
  transition: all 0.3s ease;
}

.name-effect-sapphire-basic:hover {
  color: #1e40af;
  transform: translateY(-1px) scale(1.03);
}

.dark .name-effect-sapphire-basic {
  color: #3b82f6;
  font-weight: 800;
}

.dark .name-effect-sapphire-basic:hover {
  color: #60a5fa;
}

.name-effect-sapphire-wave {
  color: #1e40af;
  font-weight: 800;
  animation: sapphire-ocean 3.5s ease-in-out infinite;
}

.dark .name-effect-sapphire-wave {
  color: #60a5fa;
  font-weight: 800;
  animation: sapphire-ocean-dark 3.5s ease-in-out infinite;
}

@keyframes sapphire-ocean {
  0%,
  100% {
    color: #1e40af;
    transform: translateX(0px) translateY(0px);
  }
  25% {
    color: #2563eb;
    transform: translateX(1.5px) translateY(-1px);
  }
  50% {
    color: #3b82f6;
    transform: translateX(0px) translateY(-2px);
  }
  75% {
    color: #2563eb;
    transform: translateX(-1.5px) translateY(-1px);
  }
}

@keyframes sapphire-ocean-dark {
  0%,
  100% {
    color: #60a5fa;
    transform: translateX(0px) translateY(0px);
  }
  25% {
    color: #3b82f6;
    transform: translateX(1.5px) translateY(-1px);
  }
  50% {
    color: #93c5fd;
    transform: translateX(0px) translateY(-2px);
  }
  75% {
    color: #3b82f6;
    transform: translateX(-1.5px) translateY(-1px);
  }
}

/* ===== RUBY TIER - Intense Red ===== */

.name-effect-ruby-basic {
  color: #b91c1c;
  font-weight: 800;
  transition: all 0.3s ease;
}

.name-effect-ruby-basic:hover {
  color: #991b1b;
  transform: translateY(-1px) scale(1.03);
}

.dark .name-effect-ruby-basic {
  color: #ef4444;
  font-weight: 800;
}

.dark .name-effect-ruby-basic:hover {
  color: #f87171;
}

.name-effect-ruby-fire {
  color: #991b1b;
  font-weight: 800;
  animation: ruby-blaze 2.8s ease-in-out infinite;
}

.dark .name-effect-ruby-fire {
  color: #f87171;
  font-weight: 800;
  animation: ruby-blaze-dark 2.8s ease-in-out infinite;
}

@keyframes ruby-blaze {
  0%,
  100% {
    color: #991b1b;
    transform: translateY(0px) rotate(0deg);
  }
  25% {
    color: #dc2626;
    transform: translateY(-1px) rotate(1deg);
  }
  50% {
    color: #ef4444;
    transform: translateY(-2px) rotate(0deg) scale(1.02);
  }
  75% {
    color: #dc2626;
    transform: translateY(-1px) rotate(-1deg);
  }
}

@keyframes ruby-blaze-dark {
  0%,
  100% {
    color: #f87171;
    transform: translateY(0px) rotate(0deg);
  }
  25% {
    color: #ef4444;
    transform: translateY(-1px) rotate(1deg);
  }
  50% {
    color: #fca5a5;
    transform: translateY(-2px) rotate(0deg) scale(1.02);
  }
  75% {
    color: #ef4444;
    transform: translateY(-1px) rotate(-1deg);
  }
}

/* ===== AMETHYST TIER - Vibrant Purple ===== */

.name-effect-amethyst-basic {
  color: #6b21a8;
  font-weight: 800;
  transition: all 0.3s ease;
}

.name-effect-amethyst-basic:hover {
  color: #581c87;
  transform: translateY(-1px) scale(1.03);
}

.dark .name-effect-amethyst-basic {
  color: #a855f7;
  font-weight: 800;
}

.dark .name-effect-amethyst-basic:hover {
  color: #c084fc;
}

.name-effect-amethyst-magic {
  color: #581c87;
  font-weight: 800;
  animation: amethyst-mystical 4.5s ease-in-out infinite;
}

.dark .name-effect-amethyst-magic {
  color: #c084fc;
  font-weight: 800;
  animation: amethyst-mystical-dark 4.5s ease-in-out infinite;
}

@keyframes amethyst-mystical {
  0%,
  100% {
    color: #581c87;
    transform: translate(0px, 0px) scale(1);
  }
  20% {
    color: #6b21a8;
    transform: translate(1px, -0.5px) scale(1.01);
  }
  40% {
    color: #7c3aed;
    transform: translate(0px, -1.5px) scale(1.03);
  }
  60% {
    color: #8b5cf6;
    transform: translate(-1px, -1px) scale(1.02);
  }
  80% {
    color: #6b21a8;
    transform: translate(0.5px, -0.3px) scale(1.01);
  }
}

@keyframes amethyst-mystical-dark {
  0%,
  100% {
    color: #c084fc;
    transform: translate(0px, 0px) scale(1);
  }
  20% {
    color: #a855f7;
    transform: translate(1px, -0.5px) scale(1.01);
  }
  40% {
    color: #d8b4fe;
    transform: translate(0px, -1.5px) scale(1.03);
  }
  60% {
    color: #e9d5ff;
    transform: translate(-1px, -1px) scale(1.02);
  }
  80% {
    color: #a855f7;
    transform: translate(0.5px, -0.3px) scale(1.01);
  }
}
