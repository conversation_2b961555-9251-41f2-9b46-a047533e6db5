#!/bin/bash

# Docker testing script cho gamification system
echo "🐳 DOCKER GAMIFICATION TESTING SCRIPT"
echo "======================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️ $1${NC}"
}

# Check if Docker is running
check_docker() {
    print_info "Checking Docker status..."
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker first."
        exit 1
    fi
    print_status "Docker is running"
}

# Check if containers are running
check_containers() {
    print_info "Checking containers status..."
    
    # Check backend container
    if docker ps | grep -q "backend\|node\|ql_ctdt"; then
        print_status "Backend container is running"
        BACKEND_CONTAINER=$(docker ps --format "table {{.Names}}" | grep -E "backend|node|ql_ctdt" | head -1)
        print_info "Backend container: $BACKEND_CONTAINER"
    else
        print_error "Backend container is not running"
        print_info "Please start your containers with: docker-compose up -d"
        exit 1
    fi
    
    # Check database container
    if docker ps | grep -q "postgres\|db\|database"; then
        print_status "Database container is running"
        DB_CONTAINER=$(docker ps --format "table {{.Names}}" | grep -E "postgres|db|database" | head -1)
        print_info "Database container: $DB_CONTAINER"
    else
        print_warning "Database container might not be running"
    fi
}

# Test database connection
test_database() {
    print_info "Testing database connection..."
    
    # Try to connect to database through backend container
    docker exec $BACKEND_CONTAINER node -e "
        const { sequelize } = require('./backend/src/models');
        sequelize.authenticate()
            .then(() => console.log('✅ Database connection successful'))
            .catch(err => {
                console.log('❌ Database connection failed:', err.message);
                process.exit(1);
            })
            .finally(() => sequelize.close());
    " 2>/dev/null
    
    if [ $? -eq 0 ]; then
        print_status "Database connection successful"
    else
        print_error "Database connection failed"
        return 1
    fi
}

# Copy test files to container
copy_test_files() {
    print_info "Copying test files to container..."
    
    docker cp debug_avatar_system.js $BACKEND_CONTAINER:/app/
    docker cp debug_title_badge_system.js $BACKEND_CONTAINER:/app/
    docker cp fix_gamification_sync.js $BACKEND_CONTAINER:/app/
    docker cp test_gamification_docker.js $BACKEND_CONTAINER:/app/
    
    print_status "Test files copied to container"
}

# Run database tests
run_database_tests() {
    print_info "Running database tests..."
    
    echo ""
    echo "🔍 DEBUGGING GAMIFICATION SYSTEM..."
    echo "=================================="
    
    docker exec -it $BACKEND_CONTAINER node test_gamification_docker.js
    
    echo ""
    read -p "Do you want to fix the issues? (y/n): " -n 1 -r
    echo ""
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_info "Running fixes..."
        docker exec -it $BACKEND_CONTAINER node test_gamification_docker.js --fix
    fi
}

# Test specific user
test_specific_user() {
    echo ""
    read -p "Enter User ID to test (or press Enter to skip): " USER_ID
    
    if [ ! -z "$USER_ID" ]; then
        print_info "Testing User ID: $USER_ID"
        docker exec -it $BACKEND_CONTAINER node test_gamification_docker.js --user $USER_ID
        
        echo ""
        read -p "Do you want to fix issues for this user? (y/n): " -n 1 -r
        echo ""
        
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            docker exec -it $BACKEND_CONTAINER node test_gamification_docker.js --user $USER_ID --fix
        fi
    fi
}

# Test API endpoints
test_api_endpoints() {
    print_info "Testing API endpoints..."
    
    # Check if axios is available
    docker exec $BACKEND_CONTAINER npm list axios > /dev/null 2>&1
    if [ $? -ne 0 ]; then
        print_warning "Installing axios for API testing..."
        docker exec $BACKEND_CONTAINER npm install axios
    fi
    
    # Copy API test file
    docker cp test_api_endpoints.js $BACKEND_CONTAINER:/app/
    
    echo ""
    echo "🧪 TESTING API ENDPOINTS..."
    echo "=========================="
    
    # Get server port
    SERVER_PORT=$(docker exec $BACKEND_CONTAINER node -e "console.log(process.env.PORT || 8888)")
    
    print_info "Server should be running on port: $SERVER_PORT"
    print_info "Testing with default credentials..."
    
    docker exec -it $BACKEND_CONTAINER env API_BASE_URL=http://localhost:$SERVER_PORT node test_api_endpoints.js
}

# Show logs
show_logs() {
    echo ""
    read -p "Do you want to see backend logs? (y/n): " -n 1 -r
    echo ""
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_info "Showing backend logs (last 50 lines)..."
        docker logs --tail 50 $BACKEND_CONTAINER
    fi
}

# Main execution
main() {
    echo "Starting gamification system testing..."
    echo ""
    
    # Run checks
    check_docker
    check_containers
    test_database
    
    if [ $? -ne 0 ]; then
        print_error "Database connection failed. Cannot proceed with tests."
        exit 1
    fi
    
    # Copy test files
    copy_test_files
    
    # Run tests
    run_database_tests
    test_specific_user
    
    echo ""
    read -p "Do you want to test API endpoints? (y/n): " -n 1 -r
    echo ""
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        test_api_endpoints
    fi
    
    show_logs
    
    echo ""
    print_status "Testing completed!"
    print_info "Test files are now available in your container at /app/"
    print_info "You can run individual tests with:"
    print_info "  docker exec -it $BACKEND_CONTAINER node test_gamification_docker.js"
    print_info "  docker exec -it $BACKEND_CONTAINER node test_api_endpoints.js"
}

# Command line options
case "${1:-}" in
    --help|-h)
        echo "🐳 Docker Gamification Testing Script"
        echo ""
        echo "Usage:"
        echo "  ./docker-test.sh              # Run full test suite"
        echo "  ./docker-test.sh --db-only    # Test database only"
        echo "  ./docker-test.sh --api-only   # Test API only"
        echo "  ./docker-test.sh --help       # Show this help"
        echo ""
        echo "Prerequisites:"
        echo "  - Docker and Docker Compose running"
        echo "  - Backend container running"
        echo "  - Database container running"
        exit 0
        ;;
    --db-only)
        check_docker
        check_containers
        test_database
        copy_test_files
        run_database_tests
        ;;
    --api-only)
        check_docker
        check_containers
        test_api_endpoints
        ;;
    *)
        main
        ;;
esac
