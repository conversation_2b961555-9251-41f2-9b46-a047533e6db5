// Quick test script to verify emoji API endpoints
const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api';
const TEST_TOKEN = 'your_jwt_token_here'; // Replace with actual token

const headers = {
    'Authorization': `Bearer ${TEST_TOKEN}`,
    'Content-Type': 'application/json'
};

async function testEmojiInitialize() {
    try {
        console.log('Testing emoji initialize...');
        const response = await axios.post(`${BASE_URL}/emojis/initialize`, {}, { headers });
        console.log('✅ Initialize response:', response.data);
        return true;
    } catch (error) {
        console.error('❌ Initialize error:', error.response?.data || error.message);
        return false;
    }
}

async function testEmojiCollection() {
    try {
        console.log('Testing emoji collection...');
        const response = await axios.get(`${BASE_URL}/emojis/collection`, { headers });
        console.log('✅ Collection response:', response.data);
        return true;
    } catch (error) {
        console.error('❌ Collection error:', error.response?.data || error.message);
        return false;
    }
}

async function testEmojiShop() {
    try {
        console.log('Testing emoji shop...');
        const response = await axios.get(`${BASE_URL}/emojis/shop`, { headers });
        console.log('✅ Shop response:', response.data);
        return true;
    } catch (error) {
        console.error('❌ Shop error:', error.response?.data || error.message);
        return false;
    }
}

async function runTests() {
    console.log('🧪 Starting Emoji API Tests...\n');
    
    const results = [];
    
    results.push(await testEmojiInitialize());
    results.push(await testEmojiCollection());
    results.push(await testEmojiShop());
    
    const passed = results.filter(r => r).length;
    const total = results.length;
    
    console.log(`\n📊 Test Results: ${passed}/${total} passed`);
    
    if (passed === total) {
        console.log('🎉 All tests passed!');
    } else {
        console.log('⚠️ Some tests failed. Check the errors above.');
    }
}

// Run tests if this file is executed directly
if (require.main === module) {
    runTests();
}

module.exports = { runTests };
