// doc_for_devgame/task4/test_skills_api.js
// Simple test script for Skills API

const axios = require('axios');

const BASE_URL = 'http://localhost:8888/api';
let authToken = '';

// Test configuration
const testUser = {
    username: 'testuser',
    password: 'password'
};

// Helper function to make authenticated requests
const apiRequest = async (method, endpoint, data = null, useAuth = true) => {
    try {
        const config = {
            method,
            url: `${BASE_URL}${endpoint}`,
            headers: useAuth && authToken ? { 'Authorization': `Bearer ${authToken}` } : {}
        };
        
        if (data) {
            config.data = data;
            config.headers['Content-Type'] = 'application/json';
        }

        const response = await axios(config);
        return { success: true, data: response.data };
    } catch (error) {
        return { 
            success: false, 
            error: error.response?.data || error.message,
            status: error.response?.status
        };
    }
};

// Test functions
async function testLogin() {
    console.log('\n🔐 Testing User Login...');
    
    const result = await apiRequest('POST', '/users/login', testUser, false);
    
    if (result.success && result.data.token) {
        authToken = result.data.token;
        console.log('✅ Login successful');
        console.log('📝 Token received:', authToken.substring(0, 20) + '...');
        return true;
    } else {
        console.log('❌ Login failed:', result.error);
        return false;
    }
}

async function testGetAllSkills() {
    console.log('\n🛒 Testing Get All Skills...');
    
    const result = await apiRequest('GET', '/skills', null, false);
    
    if (result.success) {
        console.log('✅ Skills fetched successfully');
        console.log('📊 Total skills:', result.data.data?.skills?.length || 0);
        
        if (result.data.data?.skills?.length > 0) {
            const skill = result.data.data.skills[0];
            console.log('📋 Sample skill:', {
                id: skill.skill_id,
                name: skill.skill_name,
                category: skill.category,
                cost: `${skill.cost_amount} ${skill.cost_type}`
            });
        }
        return true;
    } else {
        console.log('❌ Failed to fetch skills:', result.error);
        return false;
    }
}

async function testGetSkillsByCategory() {
    console.log('\n⚔️ Testing Get Skills by Category (ATTACK)...');
    
    const result = await apiRequest('GET', '/skills/category/attack', null, false);
    
    if (result.success) {
        console.log('✅ Attack skills fetched successfully');
        console.log('📊 Attack skills count:', result.data.data?.skills?.length || 0);
        return true;
    } else {
        console.log('❌ Failed to fetch attack skills:', result.error);
        return false;
    }
}

async function testGetUserSkills() {
    console.log('\n👤 Testing Get User Skills...');
    
    const result = await apiRequest('GET', '/skills/my-skills');
    
    if (result.success) {
        console.log('✅ User skills fetched successfully');
        console.log('📊 Owned skills count:', result.data.data?.total_owned || 0);
        return true;
    } else {
        console.log('❌ Failed to fetch user skills:', result.error);
        return false;
    }
}

async function testGetAffordableSkills() {
    console.log('\n💰 Testing Get Affordable Skills...');
    
    const result = await apiRequest('GET', '/skills/affordable');
    
    if (result.success) {
        console.log('✅ Affordable skills fetched successfully');
        console.log('📊 Affordable skills count:', result.data.data?.count || 0);
        console.log('💵 User balances:', result.data.data?.user_balances);
        return true;
    } else {
        console.log('❌ Failed to fetch affordable skills:', result.error);
        return false;
    }
}

async function testPurchaseSkill() {
    console.log('\n🛍️ Testing Purchase Skill...');
    
    // First get affordable skills to find one to purchase
    const affordableResult = await apiRequest('GET', '/skills/affordable');
    
    if (!affordableResult.success || !affordableResult.data.data?.affordable_skills?.length) {
        console.log('⚠️ No affordable skills found to purchase');
        return false;
    }
    
    const skillToPurchase = affordableResult.data.data.affordable_skills[0];
    console.log('🎯 Attempting to purchase:', skillToPurchase.skill_name);
    
    const result = await apiRequest('POST', '/skills/purchase', {
        skill_id: skillToPurchase.skill_id
    });
    
    if (result.success) {
        console.log('✅ Skill purchased successfully');
        console.log('💰 Balance after purchase:', result.data.data?.balance_after);
        return true;
    } else {
        console.log('❌ Failed to purchase skill:', result.error);
        return false;
    }
}

async function testCreateQuizLoadout() {
    console.log('\n⚔️ Testing Create Quiz Loadout...');
    
    // First get user's owned skills
    const userSkillsResult = await apiRequest('GET', '/skills/my-skills');
    
    if (!userSkillsResult.success || !userSkillsResult.data.data?.skills?.length) {
        console.log('⚠️ User has no skills to create loadout');
        return false;
    }
    
    const ownedSkills = userSkillsResult.data.data.skills;
    
    if (ownedSkills.length < 4) {
        console.log('⚠️ User needs at least 4 skills to create loadout');
        console.log('📊 Current owned skills:', ownedSkills.length);
        return false;
    }
    
    const skillIds = ownedSkills.slice(0, 4).map(skill => skill.skill_id);
    
    const result = await apiRequest('POST', '/skills/loadout', {
        quiz_session_id: 'test_quiz_001',
        skill_ids: skillIds
    });
    
    if (result.success) {
        console.log('✅ Quiz loadout created successfully');
        console.log('🎮 Loadout skills:', skillIds);
        return true;
    } else {
        console.log('❌ Failed to create quiz loadout:', result.error);
        return false;
    }
}

async function testGetQuizLoadout() {
    console.log('\n📋 Testing Get Quiz Loadout...');
    
    const result = await apiRequest('GET', '/skills/loadout/test_quiz_001');
    
    if (result.success) {
        console.log('✅ Quiz loadout fetched successfully');
        console.log('🎮 Loadout data:', result.data.data?.loadout);
        return true;
    } else {
        console.log('❌ Failed to fetch quiz loadout:', result.error);
        return false;
    }
}

async function testGetRandomSkill() {
    console.log('\n🎲 Testing Get Random Skill...');
    
    const result = await apiRequest('GET', '/skills/random/test_quiz_001');
    
    if (result.success) {
        console.log('✅ Random skill selected successfully');
        console.log('🎯 Selected skill:', result.data.data?.skill?.skill_name);
        return true;
    } else {
        console.log('❌ Failed to get random skill:', result.error);
        return false;
    }
}

// Main test runner
async function runTests() {
    console.log('🚀 Starting Skills API Tests...');
    console.log('=' .repeat(50));
    
    const tests = [
        { name: 'Login', fn: testLogin },
        { name: 'Get All Skills', fn: testGetAllSkills },
        { name: 'Get Skills by Category', fn: testGetSkillsByCategory },
        { name: 'Get User Skills', fn: testGetUserSkills },
        { name: 'Get Affordable Skills', fn: testGetAffordableSkills },
        { name: 'Purchase Skill', fn: testPurchaseSkill },
        { name: 'Create Quiz Loadout', fn: testCreateQuizLoadout },
        { name: 'Get Quiz Loadout', fn: testGetQuizLoadout },
        { name: 'Get Random Skill', fn: testGetRandomSkill }
    ];
    
    let passed = 0;
    let failed = 0;
    
    for (const test of tests) {
        try {
            const result = await test.fn();
            if (result) {
                passed++;
            } else {
                failed++;
            }
        } catch (error) {
            console.log(`❌ Test "${test.name}" threw error:`, error.message);
            failed++;
        }
        
        // Small delay between tests
        await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    console.log('\n' + '=' .repeat(50));
    console.log('📊 TEST RESULTS:');
    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`📈 Success Rate: ${((passed / (passed + failed)) * 100).toFixed(1)}%`);
    
    if (failed === 0) {
        console.log('🎉 All tests passed! Skills API is working correctly.');
    } else {
        console.log('⚠️ Some tests failed. Check the logs above for details.');
    }
}

// Run tests if this file is executed directly
if (require.main === module) {
    runTests().catch(console.error);
}

module.exports = { runTests };
