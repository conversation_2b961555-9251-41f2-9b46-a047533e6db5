"use client";

import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON>T<PERSON>le,
  DialogTrigger,
} from "@/components/ui/feedback";
import { Badge } from "@/components/ui/feedback";
import { Button } from "@/components/ui/forms";
import { Progress } from "@/components/ui/feedback";
import { cn } from "@/lib/utils";
import {
  getVietnameseTierName,
} from "@/lib/utils/tier-assets";
import { TierIcon } from "@/lib/hooks/use-tier-icon";
import { UserBadgeData, BadgeRarity } from "@/lib/types/gamification";
import { Star, Sparkles, Crown, Gem, Lock, Calendar, Trophy, Target } from "lucide-react";

interface BadgeDetailsModalProps {
  badge: UserBadgeData;
  currentLevel: number;
  trigger: React.ReactNode;
  className?: string;
}

export const BadgeDetailsModal: React.FC<BadgeDetailsModalProps> = ({
  badge,
  currentLevel,
  trigger,
  className,
}) => {
  const isUnlocked = !!badge.unlocked_at;
  const isLocked = currentLevel < badge.Badge.unlock_level;
  const progressToUnlock = isLocked 
    ? (currentLevel / badge.Badge.unlock_level) * 100 
    : 100;

  // Get rarity configuration
  const getRarityConfig = (rarity: BadgeRarity) => {
    const rarityConfigs = {
      common: {
        color: "text-gray-600 bg-gray-50 border-gray-200",
        icon: Star,
        label: "Thường",
        gradient: "from-gray-400 to-gray-600",
        bgGradient: "from-gray-50 to-gray-100",
      },
      rare: {
        color: "text-blue-600 bg-blue-50 border-blue-200",
        icon: Sparkles,
        label: "Hiếm",
        gradient: "from-blue-400 to-blue-600",
        bgGradient: "from-blue-50 to-blue-100",
      },
      epic: {
        color: "text-purple-600 bg-purple-50 border-purple-200",
        icon: Crown,
        label: "Sử Thi",
        gradient: "from-purple-400 to-purple-600",
        bgGradient: "from-purple-50 to-purple-100",
      },
      legendary: {
        color: "text-yellow-600 bg-yellow-50 border-yellow-200",
        icon: Gem,
        label: "Huyền Thoại",
        gradient: "from-yellow-400 to-orange-500",
        bgGradient: "from-yellow-50 to-orange-100",
      },
    };
    return rarityConfigs[rarity] || rarityConfigs.common;
  };

  const rarityConfig = getRarityConfig(badge.Badge.rarity);
  const RarityIcon = rarityConfig.icon;

  return (
    <Dialog>
      <DialogTrigger asChild>
        {trigger}
      </DialogTrigger>
      
      <DialogContent className={cn("max-w-md", className)}>
        <DialogHeader>
          <DialogTitle className="sr-only">
            Chi tiết huy hiệu {badge.Badge.badge_name}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Header with badge icon and basic info */}
          <div className={cn(
            "relative p-6 rounded-lg bg-gradient-to-br",
            rarityConfig.bgGradient
          )}>
            {/* Background pattern */}
            <div className="absolute inset-0 opacity-10">
              <div className={cn(
                "w-full h-full bg-gradient-to-br rounded-lg",
                rarityConfig.gradient
              )} />
            </div>

            <div className="relative flex flex-col items-center text-center space-y-3">
              {/* Badge Icon */}
              <div className="relative">
                {badge.Badge.icon_path ? (
                  <div className="w-20 h-20 bg-white/80 rounded-full flex items-center justify-center shadow-lg">
                    <RarityIcon className="h-10 w-10" />
                  </div>
                ) : (
                  <div className="w-20 h-20 bg-white/80 rounded-full flex items-center justify-center shadow-lg">
                    <TierIcon
                      level={badge.Badge.unlock_level}
                      size="lg"
                      tierName={badge.Badge.tier_name}
                      levelInTier={1}
                    />
                  </div>
                )}
                
                {isLocked && (
                  <div className="absolute inset-0 flex items-center justify-center bg-black/50 rounded-full">
                    <Lock className="h-6 w-6 text-white" />
                  </div>
                )}
              </div>

              {/* Badge Name */}
              <div>
                <h3 className="text-xl font-bold text-gray-900">
                  {badge.Badge.badge_name}
                </h3>
                
                {/* Rarity Badge */}
                <Badge
                  variant="outline"
                  className={cn(
                    "mt-2 bg-white/80",
                    rarityConfig.color
                  )}
                >
                  <RarityIcon className="h-3 w-3 mr-1" />
                  {rarityConfig.label}
                </Badge>
              </div>
            </div>
          </div>

          {/* Badge Description */}
          <div className="space-y-3">
            <h4 className="font-semibold text-gray-900 flex items-center gap-2">
              <Trophy className="h-4 w-4" />
              Mô tả
            </h4>
            <p className="text-sm text-gray-600 leading-relaxed">
              {badge.Badge.description || "Không có mô tả cho huy hiệu này."}
            </p>
          </div>

          {/* Badge Details */}
          <div className="space-y-4">
            <h4 className="font-semibold text-gray-900 flex items-center gap-2">
              <Target className="h-4 w-4" />
              Thông tin chi tiết
            </h4>
            
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div className="space-y-1">
                <p className="text-gray-500">Hạng</p>
                <p className="font-medium">
                  {getVietnameseTierName(badge.Badge.tier_name)}
                </p>
              </div>
              
              <div className="space-y-1">
                <p className="text-gray-500">Yêu cầu Level</p>
                <p className="font-medium">
                  {badge.Badge.unlock_level}
                </p>
              </div>
              
              <div className="space-y-1">
                <p className="text-gray-500">Độ hiếm</p>
                <p className="font-medium">
                  {rarityConfig.label}
                </p>
              </div>
              
              <div className="space-y-1">
                <p className="text-gray-500">Trạng thái</p>
                <p className={cn(
                  "font-medium",
                  isUnlocked ? "text-green-600" : "text-yellow-600"
                )}>
                  {isUnlocked ? "Đã mở khóa" : "Chưa mở khóa"}
                </p>
              </div>
            </div>
          </div>

          {/* Unlock Progress */}
          {isLocked && (
            <div className="space-y-3">
              <h4 className="font-semibold text-gray-900 flex items-center gap-2">
                <Target className="h-4 w-4" />
                Tiến độ mở khóa
              </h4>
              
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">
                    Level hiện tại: {currentLevel}
                  </span>
                  <span className="text-gray-600">
                    Cần: {badge.Badge.unlock_level}
                  </span>
                </div>
                
                <Progress 
                  value={progressToUnlock} 
                  className="h-2"
                />
                
                <p className="text-xs text-gray-500 text-center">
                  Còn {badge.Badge.unlock_level - currentLevel} level nữa để mở khóa
                </p>
              </div>
            </div>
          )}

          {/* Unlock Date */}
          {isUnlocked && badge.unlocked_at && (
            <div className="space-y-3">
              <h4 className="font-semibold text-gray-900 flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                Ngày mở khóa
              </h4>
              
              <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                <p className="text-sm text-green-800 font-medium">
                  {new Date(badge.unlocked_at).toLocaleDateString('vi-VN', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                  })}
                </p>
              </div>
            </div>
          )}

          {/* Achievement Tips */}
          {isLocked && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h5 className="font-medium text-blue-900 mb-2">
                💡 Mẹo để mở khóa
              </h5>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• Hoàn thành các bài quiz để tăng level</li>
                <li>• Duy trì streak để nhận bonus XP</li>
                <li>• Tham gia các hoạt động gamification khác</li>
              </ul>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default BadgeDetailsModal;
