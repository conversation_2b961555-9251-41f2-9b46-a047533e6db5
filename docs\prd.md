# QL_CTDT Frontend Code Restructuring PRD

## Intro Project Analysis and Context

### Analysis Source

**Document-project output available**: Brownfield architecture document tại `docs/brownfield-architecture.md` với phân tích chi tiết về frontend structure hiện tại.

### Current Project State

**Frontend hiện tại**: Next.js 15 application với App Router, React 19, TypeScript, Radix UI components, Tailwind CSS. Cấu trúc hiện tại có các vấn đề về organization:

- Components rải rác trong nhiều thư mục
- Cách đặt tên không nhất quán
- Business logic lẫn với UI components
- Không tách biệt rõ ràng các concerns
- Có thể có code trùng lặp trong UI components

### Available Documentation Analysis

✅ **Document-project analysis available** - sử dụng tài liệu kỹ thuật hiện có

**Tà<PERSON> liệu chính từ document-project**:

- ✅ Tài liệu Tech Stack
- ✅ Cấu trúc Source Tree/Architecture
- ✅ Tài liệu API
- ✅ Tài liệu Technical Debt

### Enhancement Scope Definition

**Enhancement Type**:

- ✅ **Cải tiến UI/UX** (tái tổ chức cấu trúc)
- ✅ **Cải thiện Performance/Scalability** (tối ưu code)
- ✅ **Nâng cấp Technology Stack** (patterns tổ chức tốt hơn)

**Enhancement Description**:
Tái cấu trúc toàn bộ frontend codebase để có cấu trúc thư mục logic hơn, loại bỏ code thừa, tối ưu hóa tổ chức component, và cải thiện khả năng maintain mà không thay đổi UI/UX hoặc business logic hiện có.

**Impact Assessment**: ✅ **Tác động Đáng kể** (thay đổi substantial code hiện có)

### Goals and Background Context

**Goals**:

- Tái tổ chức cấu trúc thư mục frontend theo best practices
- Loại bỏ code trùng lặp và code không dùng
- Cải thiện khả năng tái sử dụng component và maintain
- Tối ưu hóa import paths và dependencies
- Đảm bảo cách đặt tên nhất quán
- **CRITICAL**: Loại bỏ HOÀN TOÀN tất cả test files và test-related configurations (NO TESTING POLICY)

**Background Context**:
Frontend hiện tại hoạt động tốt về chức năng nhưng có technical debt về tổ chức code. Với sự phát triển của dự án, cần cấu trúc rõ ràng hơn để dễ maintain và scale.

### Change Log

| Thay đổi    | Ngày       | Phiên bản | Mô tả                                     | Tác giả         |
| ----------- | ---------- | --------- | ----------------------------------------- | --------------- |
| PRD ban đầu | 2025-01-23 | 1.0       | Tạo PRD tái cấu trúc frontend             | Product Manager |
| Epic 2 thêm | 2025-07-26 | 2.0       | Thêm Epic 2 - Chapter Analytics           | John (PM)       |
| Epic 3 thêm | 2025-07-29 | 3.0       | Thêm Epic 3 - Level/XP System Integration | John (PM)       |

## Requirements

### Functional Requirements

**FR1**: Hệ thống phải tái tổ chức toàn bộ cấu trúc thư mục frontend theo pattern feature-based organization thay vì type-based organization hiện tại

**FR2**: Hệ thống phải consolidate tất cả UI components vào một thư mục `components/ui` duy nhất với sub-categories rõ ràng (forms, navigation, layout, feedback, etc.)

**FR3**: Hệ thống phải tách biệt business components khỏi UI components bằng cách tạo thư mục `components/features` cho các components có business logic

**FR4**: Hệ thống phải tạo thư mục `lib` tập trung cho tất cả utilities, helpers, và shared logic thay vì rải rác như hiện tại

**FR5**: Hệ thống phải implement barrel exports (index.js files) cho tất cả major directories để simplify import statements

**FR6**: Hệ thống phải loại bỏ tất cả duplicate code trong components và consolidate thành reusable components

**FR7**: **NO TESTING POLICY** - Hệ thống phải xóa HOÀN TOÀN tất cả test files, test directories, test configurations, và test-related dependencies. Dự án này không implement bất kỳ loại testing nào (unit, integration, e2e, manual testing procedures)

**FR8**: Hệ thống phải maintain tất cả existing functionality và UI appearance sau khi refactor

### Non-Functional Requirements

**NFR1**: Việc refactoring không được làm tăng bundle size quá 5% so với hiện tại

**NFR2**: Tất cả existing import paths phải được update mà không break bất kỳ functionality nào

**NFR3**: Build time không được tăng quá 10% sau khi refactor

**NFR4**: Code organization phải follow consistent naming conventions (kebab-case cho files, PascalCase cho components)

**NFR5**: Mỗi component file không được vượt quá 200 lines of code, nếu vượt phải split thành smaller components

**NFR6**: Tất cả components phải có proper TypeScript types và không có any types

### Compatibility Requirements

**CR1: API Compatibility**: Tất cả API calls và data fetching logic phải giữ nguyên và hoạt động như hiện tại

**CR2: Route Compatibility**: Tất cả Next.js App Router routes phải maintain exact same URLs và navigation behavior

**CR3: UI/UX Consistency**: Toàn bộ user interface phải giữ nguyên appearance, styling, và user interactions

**CR4: Integration Compatibility**: Socket.IO connections, authentication flows, và external integrations phải hoạt động không thay đổi

### Testing Policy

**CRITICAL PROJECT POLICY**: This project follows a **NO TESTING** approach.

**Testing Requirements**:

- **Unit Tests**: KHÔNG ĐƯỢC TRIỂN KHAI - Project policy prohibits unit testing
- **Integration Tests**: KHÔNG ĐƯỢC TRIỂN KHAI - Project policy prohibits integration testing
- **E2E Tests**: KHÔNG ĐƯỢC TRIỂN KHAI - Project policy prohibits end-to-end testing
- **Manual Testing Procedures**: KHÔNG ĐƯỢC TRIỂN KHAI - Project policy prohibits formal manual testing
- **Test Frameworks**: KHÔNG ĐƯỢC SỬ DỤNG - No testing frameworks allowed in this project

**Quality Assurance Approach**:
Quality assurance relies solely on:

- TypeScript compilation success
- Application startup without errors
- Basic functionality verification through normal usage
- Code review processes
- Runtime error monitoring (if implemented)

**Implementation Impact**:

- All existing test files must be completely removed
- All test-related dependencies must be removed from package.json
- All test scripts must be removed from package.json
- No new test files should be created during refactoring
- No testing considerations in acceptance criteria verification

## User Interface Enhancement Goals

### Integration with Existing UI

**Component Library Integration**:
Việc tái cấu trúc sẽ maintain hoàn toàn việc sử dụng Radix UI components hiện tại nhưng organize chúng tốt hơn. Tất cả Radix UI components sẽ được wrap trong `components/ui/` với consistent naming và proper re-exports.

**Design System Consistency**:

- Giữ nguyên Tailwind CSS classes và styling approach hiện tại
- Maintain existing color scheme, typography, và spacing patterns
- Preserve tất cả custom CSS variables và theme configurations

### Modified/New Screens and Views

**Không có screens mới** - Đây là pure refactoring nên không thêm screens/views mới.

**Preserved screens/views**:

- `/dashboard` - Dashboard chính
- `/quiz-live` - Giao diện quiz real-time
- `/quiz-monitor` - Monitoring cho teacher
- `/quiz-waiting-room` - Phòng chờ quiz
- `/(auth)/login` - Trang đăng nhập
- Tất cả các routes khác giữ nguyên

### UI Consistency Requirements

**Visual Consistency**: Giữ nguyên toàn bộ color palette, typography, spacing, borders & shadows

**Interaction Consistency**: Button behaviors, form interactions, navigation patterns, modal/dialog behaviors không đổi

**Component API Consistency**: Tất cả component props interfaces, event handlers, và composition patterns giữ nguyên

## Technical Constraints and Integration Requirements

### Existing Technology Stack

**Languages**: TypeScript ^5, JavaScript (Node.js >=18.0.0)
**Frameworks**: Next.js 15.3.0 (App Router), React 19.0.0
**External Dependencies**: Radix UI (latest), Tailwind CSS ^4, Socket.IO 4.8.1 client

### Integration Approach

**API Integration Strategy**: Maintain tất cả existing API service files trong `src/services/api/`, preserve axios client configuration

**Frontend Integration Strategy**:

- Component Migration: Di chuyển components theo feature-based structure mà không break imports
- Route Preservation: Tất cả App Router routes giữ nguyên structure
- Service Layer: API services, Socket.IO client, auth utilities maintain current interfaces

### Code Organization and Standards

**File Structure Approach**:

```
src/
├── app/                    # Next.js App Router (KHÔNG THAY ĐỔI)
├── components/
│   ├── ui/                 # Pure UI components (Radix wrappers)
│   ├── features/           # Business logic components
│   └── charts/             # Chart.js components (GIỮ NGUYÊN)
├── lib/                    # Utilities và shared logic
├── services/               # API và external services (GIỮ NGUYÊN)
└── styles/                 # Global styles (GIỮ NGUYÊN)
```

**Naming Conventions**:

- Files: kebab-case, Components: PascalCase, Directories: kebab-case

### Risk Assessment and Mitigation

**Technical Risks**:

- Import path breaks (Risk cao) - Mitigation: Systematic find/replace với TypeScript compiler checking
- Component dependency cycles (Risk trung bình) - Mitigation: Careful dependency analysis
- Build time increase (Risk thấp) - Mitigation: Benchmark before/after

**Mitigation Strategies**:

1. Incremental migration theo batches
2. TypeScript compiler để catch import errors
3. Staging validation trước production deployment

## Epic and Story Structure

**Epic Structure Decision**: Single comprehensive epic với rationale là đây là một architectural refactoring cần được thực hiện như một unit để maintain system integrity.

## Epic 1: Frontend Code Restructuring

**Epic Goal**: Tái cấu trúc toàn bộ frontend codebase để có tổ chức thư mục logic, loại bỏ code duplication, cải thiện maintainability và developer experience mà không thay đổi functionality hoặc UI hiện có.

**Integration Requirements**: Preserve tất cả existing API integrations, Socket.IO connections, Next.js App Router structure, và authentication flows.

### Story 1.1: Thiết lập Cấu trúc Thư mục Mới và Migration Plan

Là một **developer**,
Tôi muốn **có cấu trúc thư mục rõ ràng và migration plan chi tiết**,
Để **có thể thực hiện refactoring một cách an toàn và có hệ thống**.

#### Acceptance Criteria

1. Tạo new folder structure trong `src/` theo feature-based organization
2. Document migration plan với step-by-step instructions
3. Setup TypeScript path mapping cho new structure trong tsconfig.json
4. Create barrel export files (index.ts) cho major directories
5. Backup current codebase và create migration branch

#### Integration Verification

- **IV1**: Existing functionality verification - Tất cả current imports vẫn hoạt động bình thường
- **IV2**: Integration point verification - Build process không bị break
- **IV3**: Performance impact verification - Build time không tăng đáng kể

### Story 1.2: Migration UI Components sang Components/UI Structure

Là một **developer**,
Tôi muốn **tất cả UI components được tổ chức trong components/ui với categorization rõ ràng**,
Để **dễ dàng tìm và reuse components**.

#### Acceptance Criteria

1. Di chuyển tất cả Radix UI wrapper components vào `components/ui/`
2. Categorize components: forms/, navigation/, layout/, feedback/
3. Update tất cả import statements across codebase
4. Create comprehensive barrel exports cho UI components
5. Remove duplicate UI components và consolidate functionality

#### Integration Verification

- **IV1**: Existing functionality verification - Tất cả UI components render correctly với same styling
- **IV2**: Integration point verification - Component props và events hoạt động như cũ
- **IV3**: Performance impact verification - Bundle size không tăng quá 5%

### Story 1.3: Tách Business Logic Components sang Features Structure

Là một **developer**,
Tôi muốn **business logic components được tách riêng khỏi pure UI components**,
Để **có separation of concerns rõ ràng và improve maintainability**.

#### Acceptance Criteria

1. Identify và categorize business logic components (auth, quiz, dashboard)
2. Di chuyển components vào `components/features/` với proper grouping
3. Refactor components để tách UI logic khỏi business logic
4. Update import paths và ensure proper dependency flow
5. Create feature-specific barrel exports

#### Integration Verification

- **IV1**: Existing functionality verification - Business logic hoạt động identical như trước
- **IV2**: Integration point verification - API calls và state management unchanged
- **IV3**: Performance impact verification - Component re-renders không increase

### Story 1.4: Consolidate Utilities và Shared Logic vào Lib Structure

Là một **developer**,
Tôi muốn **tất cả utilities, helpers, và shared logic được centralized trong lib/**,
Để **có single source of truth cho shared functionality**.

#### Acceptance Criteria

1. Di chuyển custom hooks từ scattered locations vào `lib/hooks/`
2. Consolidate utility functions vào `lib/utils/`
3. Move auth-related utilities vào `lib/auth/`
4. Centralize constants và types vào `lib/constants/` và `lib/types/`
5. Update tất cả imports để use new lib structure

#### Integration Verification

- **IV1**: Existing functionality verification - Tất cả utilities function correctly
- **IV2**: Integration point verification - Hook dependencies và context providers work
- **IV3**: Performance impact verification - No performance regression trong utility usage

### Story 1.4 (Final): Consolidate Utilities và Complete Migration

Là một **developer**,
Tôi muốn **tất cả utilities, helpers, shared logic được centralized và hoàn tất migration**,
Để **có codebase hoàn chỉnh với structure rõ ràng và maintainable**.

#### Acceptance Criteria

1. Di chuyển custom hooks từ scattered locations vào `lib/hooks/`
2. Consolidate utility functions vào `lib/utils/`
3. Move auth-related utilities vào `lib/auth/`
4. Centralize constants và types vào `lib/constants/` và `lib/types/`
5. Update tất cả imports để use new lib structure
6. **Code Cleanup**: Remove duplicate components/functions và unused code
7. **NO TESTING POLICY**: Delete HOÀN TOÀN tất cả test files và test-related configurations
8. Finalize tất cả import paths và verify TypeScript compilation
9. Run full build và ensure application hoạt động hoàn hảo

#### Integration Verification

- **IV1**: Existing functionality verification - Tất cả utilities function correctly và complete application works identically
- **IV2**: Integration point verification - Hook dependencies, context providers, API calls, Socket.IO, auth flows work perfectly
- **IV3**: Performance impact verification - No performance regression, bundle size optimized

---

---

## Epic 2: Nâng Cấp Front-end Hỗ Trợ API Phân Tích Học Tập Theo Chương

**Epic Goal**: Xây dựng lại giao diện front-end để tích hợp với hệ thống API phân tích học tập mới, chuyển từ hiển thị theo Learning Outcomes (LO) sang **hiển thị theo Chương** để sinh viên dễ hiểu và sử dụng hơn.

**Business Context**: Backend đã phát triển API mới với phân tích theo chương thay vì LO. Frontend hiện tại sử dụng RadarChart và analytics cũ theo LO cần được cập nhật để phù hợp với cách tiếp cận mới.

**Integration Requirements**: Tích hợp với 3 API endpoints mới từ backend: detailed-analysis, comprehensive-analysis, và teacher-analytics APIs.

### Story 2.1: Tích Hợp API Chapter Analytics Mới

Là một **developer**,
Tôi muốn **tích hợp các API phân tích theo chương mới vào hệ thống**,
Để **có thể hiển thị dữ liệu phân tích theo chương thay vì LO**.

#### Acceptance Criteria

1. Tạo service layer mới `chapterAnalyticsService` trong `lib/services/api/`
2. Implement TypeScript types cho Chapter Analytics API responses
3. Tích hợp 3 API endpoints chính:
   - `/quiz-results/detailed-analysis/:quiz_id/:user_id`
   - `/reports/subject/:subject_id/comprehensive-analysis/:user_id`
   - `/teacher-analytics/quiz/:quizId/comprehensive-report`
4. Maintain backward compatibility với existing radar APIs
5. Add error handling và loading states cho new APIs

#### Integration Verification

- **IV1**: API calls return correct chapter-based data structure
- **IV2**: TypeScript compilation successful với new types
- **IV3**: Existing functionality không bị break

### Story 2.2: Cập Nhật Student Quiz Results Page

Là một **sinh viên**,
Tôi muốn **xem kết quả quiz theo chương thay vì LO**,
Để **hiểu rõ chương nào cần ôn tập và sections cụ thể cần xem lại**.

#### Acceptance Criteria

1. Cập nhật `/dashboard/student/quizzes/result/[id]/page.tsx`
2. Thay thế `StudentRadarChart` bằng `ChapterAnalysisChart` mới
3. Hiển thị chapter strengths/weaknesses thay vì LO analysis
4. Show sections cụ thể cần ôn tập với content type (text/video/exercise)
5. Maintain existing UI layout và styling

#### Integration Verification

- **IV1**: Chapter analysis hiển thị chính xác với sections
- **IV2**: Performance không giảm so với radar chart cũ
- **IV3**: Mobile responsiveness maintained

### Story 2.3: Xây Dựng Student Learning Results Dashboard

Là một **sinh viên**,
Tôi muốn **có dashboard tổng quan về tiến độ học tập theo chương**,
Để **theo dõi progress và nhận gợi ý cải thiện cụ thể**.

#### Acceptance Criteria

1. Cập nhật `/dashboard/student/learning-results/page.tsx`
2. Thay thế `StudentLearningOutcomeMasteryChart` bằng `ChapterMasteryChart`
3. Implement `ChapterCompletionChart` với progress tracking
4. Add `SectionRecommendationCard` cho gợi ý cụ thể
5. Integrate với subject comprehensive analysis API

#### Integration Verification

- **IV1**: Chapter mastery data accurate và up-to-date
- **IV2**: Recommendations actionable và relevant
- **IV3**: Chart performance optimized

### Story 2.4: Nâng Cấp Teacher Analytics Dashboard

Là một **giảng viên**,
Tôi muốn **có insights chi tiết về hiệu suất lớp theo chương**,
Để **điều chỉnh phương pháp giảng dạy và hỗ trợ học sinh**.

#### Acceptance Criteria

1. Cập nhật `/dashboard/reports/quiz-results/page.tsx`
2. Implement `TeacherChapterAnalyticsChart` thay thế `TeacherRadarChart`
3. Add `StudentGroupChapterAnalysis` cho phân tích nhóm
4. Integrate teaching insights và recommendations
5. Add quiz comparison và benchmark features

#### Integration Verification

- **IV1**: Teacher insights accurate và actionable
- **IV2**: Student group analysis helpful cho intervention
- **IV3**: Comparison features provide valuable context

### Story 2.5: Cập Nhật Real-time Quiz Monitor

Là một **giảng viên**,
Tôi muốn **monitor quiz real-time với chapter-based insights**,
Để **có thể can thiệp kịp thời khi học sinh gặp khó khăn**.

#### Acceptance Criteria

1. Cập nhật `/quiz-monitor/[id]/page.tsx`
2. Add real-time chapter performance tracking
3. Implement alerts cho chapters có performance thấp
4. Show live chapter completion rates
5. Maintain existing real-time functionality

#### Integration Verification

- **IV1**: Real-time updates work correctly
- **IV2**: Chapter insights update in real-time
- **IV3**: Performance monitoring không impact quiz experience

### Story 2.6: Responsive Design và Mobile Optimization

Là một **user (sinh viên/giảng viên)**,
Tôi muốn **sử dụng chapter analytics trên mobile devices**,
Để **có thể truy cập insights mọi lúc mọi nơi**.

#### Acceptance Criteria

1. Optimize tất cả chapter charts cho mobile screens
2. Implement swipeable chapter navigation
3. Collapsible section recommendations
4. Touch-friendly interactive elements
5. Maintain performance trên low-end devices

#### Integration Verification

- **IV1**: Mobile experience smooth và intuitive
- **IV2**: Charts readable trên small screens
- **IV3**: Touch interactions responsive

---

## Epic 3: Tích Hợp Hệ Thống Level/XP Mới với Visual Assets - Brownfield Enhancement

**Epic Goal**: Tích hợp hệ thống level/XP 10-tier mới từ backend (đã hoàn thành trong TASK_1_1) vào giao diện frontend hiện tại, thay thế hệ thống level đơn giản cũ để sinh viên có trải nghiệm gamification phong phú hơn với titles, badges và rich visual assets.

**Business Context**: Backend đã hoàn thành hệ thống 10-tier level system với 120+ levels, titles, badges và comprehensive APIs. Frontend hiện tại vẫn sử dụng hệ thống level đơn giản (mỗi 100 XP = 1 level) cần được nâng cấp để tận dụng hệ thống mới.

**Integration Requirements**: Tích hợp với APIs mới từ TASK_1_1 và sử dụng visual assets có sẵn trong `/public/vector-ranks-pack/` để tạo trải nghiệm gamification rich và engaging.

### Epic Description

**Existing System Context:**

- Current functionality: Frontend sử dụng hệ thống level đơn giản với UserLevelBadge component cơ bản
- Technology stack: Next.js 15, React 19, TypeScript, Radix UI, Tailwind CSS
- Integration points:
  - `useGamification` hook trong `/lib/hooks/use-gamification`
  - `UserLevelBadge` component trong `/components/features/gamification/`
  - Dashboard pages: `/dashboard/page.tsx`
  - API services: `gamificationService` trong `/lib/services/`
- Visual assets: 120+ tier icons trong `/public/vector-ranks-pack/` (10 tiers × 12 variations each)

**Enhancement Details:**

- What's being added/changed:
  - Cập nhật API integration để sử dụng hệ thống 10-tier level mới
  - Nâng cấp UserLevelBadge để hiển thị tier names, icons và progress chi tiết
  - Thêm Title & Badge management components với rich visuals
  - Cập nhật dashboard để hiển thị achievements và tier progression
  - Image mapping system cho tier icons và badge visuals
- How it integrates:
  - Sử dụng API endpoints mới: `/gamification-level/`, `/titles/`
  - Mapping tier names với visual assets trong `/public/vector-ranks-pack/`
  - Backward compatibility với existing gamification APIs
- Success criteria:
  - Sinh viên thấy tier names và beautiful icons thay vì chỉ level numbers
  - Progress tracking chính xác theo XP requirements của từng tier
  - Title và Badge collection hiển thị với proper visuals
  - Smooth animations và transitions cho level progression
  - No regression trong existing functionality

### Story 3.1: Cập Nhật API Integration và Image Mapping System

Là một **developer**,
Tôi muốn **tích hợp APIs mới và tạo image mapping system cho tier visuals**,
Để **có foundation vững chắc cho việc hiển thị hệ thống level mới với rich graphics**.

#### Acceptance Criteria

1. Tích hợp các API endpoints mới từ TASK_1_1:
   - `/gamification-level/my-progress` - User level progress
   - `/gamification-level/tiers` - Tier information
   - `/titles/my-titles` - User titles
   - `/titles/my-badges` - User badges
2. Cập nhật `gamificationService` để sử dụng APIs mới
3. Tạo image mapping utility `getTierIcon(tierName, levelInTier)`
4. Cập nhật TypeScript types cho tier system và visual assets
5. Implement fallback logic cho missing images và API errors
6. Cập nhật `useGamification` hook để sử dụng level calculation mới

#### Integration Verification

- **IV1**: API calls return correct tier-based data structure với image paths
- **IV2**: Image mapping correctly resolves tier icons từ `/public/vector-ranks-pack/`
- **IV3**: TypeScript compilation successful với new types
- **IV4**: Existing gamification functionality không bị break

### Story 3.2: Nâng Cấp UserLevelBadge với Rich Tier Visuals

Là một **sinh viên**,
Tôi muốn **thấy tier icons đẹp mắt và thông tin chi tiết về level progression**,
Để **có motivation và hiểu rõ tiến độ học tập của mình trong hệ thống tier**.

#### Acceptance Criteria

1. Cập nhật UserLevelBadge component để hiển thị:
   - Tier icons từ `/public/vector-ranks-pack/` based on current level
   - Tier names (Wood, Bronze, Silver, etc.) thay vì chỉ level numbers
   - Progress bar chính xác theo XP requirements của từng tier
   - Tier colors và styling theo design system
2. Dynamic icon selection dựa trên level trong tier (1-12 variations)
3. Enhanced tooltip với tier information và next tier preview
4. Smooth transitions và animations khi level up
5. Responsive design cho mobile và desktop
6. Maintain existing component API để không break existing usage

#### Integration Verification

- **IV1**: Tier icons hiển thị correctly cho tất cả 10 tiers
- **IV2**: Progress calculation accurate theo tier XP requirements
- **IV3**: Component performance không giảm với image loading
- **IV4**: Mobile responsiveness maintained với new visuals

### Story 3.3: Xây Dựng Title & Badge Gallery với Interactive Management

Là một **sinh viên**,
Tôi muốn **xem và quản lý collection titles/badges của mình với giao diện đẹp**,
Để **có sense of achievement và có thể customize profile với titles yêu thích**.

#### Acceptance Criteria

1. Tạo Title Gallery component:
   - Hiển thị tất cả titles đã unlock với tier icons
   - Show locked titles với preview và unlock requirements
   - Active title selection với visual feedback
   - Tier-based organization và filtering
2. Tạo Badge Collection component:
   - Badge grid với rarity-based styling (common, rare, epic, legendary)
   - Badge details modal với unlock criteria và description
   - Achievement unlock animations
   - Progress tracking cho achievement badges
3. Tích hợp vào student dashboard và profile pages
4. Add badge/title unlock notifications với celebratory animations
5. Search và filter functionality cho large collections

#### Integration Verification

- **IV1**: Title selection correctly updates active title via API
- **IV2**: Badge collection accurately reflects user achievements
- **IV3**: Unlock animations smooth và engaging
- **IV4**: Performance optimized cho large badge/title collections

### Compatibility Requirements

- [x] Existing APIs remain unchanged (gamification APIs cũ vẫn hoạt động)
- [x] Database schema changes are backward compatible (TASK_1_1 đã đảm bảo)
- [x] UI changes follow existing patterns (sử dụng Radix UI và Tailwind CSS hiện có)
- [x] Performance impact is minimal (optimized image loading và caching)
- [x] Mobile responsiveness maintained với new visual components

### Risk Mitigation

- **Primary Risk**: Breaking existing gamification display và user experience
- **Mitigation**:
  - Progressive enhancement approach với feature flags
  - Comprehensive fallback logic cho API failures
  - Image preloading và lazy loading strategies
  - Thorough testing với existing user data
- **Rollback Plan**:
  - Revert API calls về gamification service cũ
  - Hide title/badge features với feature toggle
  - Restore original UserLevelBadge component

### Definition of Done

- [x] All stories completed with acceptance criteria met
- [x] Existing functionality verified through testing
- [x] Integration points working correctly với backend APIs mới
- [x] Visual assets properly integrated và optimized
- [x] Documentation updated appropriately
- [x] No regression in existing gamification features
- [x] Sinh viên có rich visual experience với tier progression, title management, và badge collection

---

## Epic 4: Tích Hợp Hệ Thống Avatar & Customization vào Frontend - Brownfield Enhancement

**Epic Goal**: Tích hợp hệ thống Avatar & Customization từ TASK_2_2 vào giao diện frontend hiện tại, cho phép sinh viên tùy chỉnh avatar, khung, hiệu ứng tên và sử dụng emoji để thể hiện cá tính và thành tích học tập.

**Business Context**: Backend đã hoàn thành hệ thống với 30 avatar động vật, khung theo tier, hiệu ứng tên cho level cao và emoji system trong TASK_2_2. Frontend hiện tại chỉ có gamification system cơ bản, cần tích hợp avatar customization để sinh viên có trải nghiệm visual phong phú và motivation mạnh mẽ hơn.

**Integration Requirements**: Tích hợp với Avatar APIs từ TASK_2_2 và sử dụng visual assets trong `/public/avatar-animal-pack/`, `/public/vector-ranks-pack/` để hiển thị avatar system hoàn chỉnh.

### Epic Description

**Existing System Context:**

- Current functionality: Frontend có UserLevelBadge component cơ bản với tier system
- Technology stack: Next.js 15, React 19, TypeScript, Radix UI, Tailwind CSS
- Integration points:
  - `useGamification` hook trong `/lib/hooks/use-gamification`
  - `UserLevelBadge` component trong `/components/features/gamification/`
  - Dashboard pages: `/dashboard/page.tsx`
  - API services: `gamificationService` trong `/lib/services/`
- Visual assets: 30 avatar animals trong `/public/avatar-animal-pack/`, frames trong `/public/vector-ranks-pack/`

**Enhancement Details:**

- What's being added/changed:
  - Hệ thống 30 avatar động vật với unlock mechanisms đa dạng
  - Khung avatar theo tier và achievements
  - Hiệu ứng tên CSS cho tier cao (Onyx, Sapphire, Ruby, Amethyst, Master)
  - Emoji system với 9 categories cho social interactions
  - Inventory management và collection progress tracking
  - Rarity system với decomposition values
- How it integrates:
  - Sử dụng API endpoints mới: `/api/avatar/my-data`, `/api/avatar/equip`, `/api/avatar/available-items`
  - Mapping avatar assets với database records
  - Integration với existing gamification system
- Success criteria:
  - Sinh viên có thể customize avatar, frame, name effects
  - Collection progress motivates continued engagement
  - Avatar displays consistently across app
  - Emoji system enhances social interactions
  - No regression trong existing functionality

### Story 4.1: Tạo Avatar Service và API Integration

Là một **developer**,
Tôi muốn **tích hợp Avatar APIs vào frontend service layer**,
Để **có thể fetch và quản lý dữ liệu avatar customization từ backend**.

#### Acceptance Criteria

1. Tạo `avatarService` trong `/lib/services/api/avatar.service.ts`
2. Implement TypeScript types cho Avatar API responses:
   - `AvatarData`, `AvatarFrame`, `NameEffect`, `EmojiData`
   - `UserInventory`, `UserCustomization`, `CollectionProgress`
3. Tích hợp các API endpoints chính:
   - `GET /api/avatar/my-data` - Dữ liệu avatar hoàn chỉnh
   - `GET /api/avatar/available-items` - Items có thể mở khóa
   - `POST /api/avatar/equip` - Trang bị item
   - `GET /api/avatar/collection-progress` - Tiến độ sưu tập
4. Tạo `useAvatar` hook trong `/lib/hooks/use-avatar.ts`
5. Add error handling và loading states cho avatar APIs
6. Implement caching strategy cho avatar data

#### Integration Verification

- **IV1**: API calls return correct avatar data structure với proper typing
- **IV2**: TypeScript compilation successful với new types
- **IV3**: Error handling works correctly cho network failures
- **IV4**: Caching reduces unnecessary API calls

### Story 4.2: Xây Dựng Avatar Display Component

Là một **sinh viên**,
Tôi muốn **thấy avatar hiện tại của mình với khung và hiệu ứng tên đẹp mắt**,
Để **thể hiện cá tính và thành tích học tập qua visual elements**.

#### Acceptance Criteria

1. Tạo `AvatarDisplay` component trong `/components/features/avatar/`
2. Hiển thị:
   - Avatar image từ `/public/avatar-animal-pack/` với proper fallbacks
   - Frame overlay từ `/public/vector-ranks-pack/` aligned perfectly
   - Name với CSS effects theo tier (gold, blue, red, purple, rainbow)
   - Rarity indicator với color coding (common, rare, epic, legendary)
3. Support multiple sizes: small (32px), medium (64px), large (128px)
4. Hover effects và smooth animations
5. Integrate với existing design system (Radix UI + Tailwind)
6. Lazy loading cho avatar images với placeholders

#### Integration Verification

- **IV1**: Avatar images load correctly với proper fallbacks
- **IV2**: Frame overlays align perfectly với avatar boundaries
- **IV3**: Name effects render correctly cho tất cả tier levels
- **IV4**: Component performance optimized với image loading

### Story 4.3: Tạo Avatar Customization Interface

Là một **sinh viên**,
Tôi muốn **có giao diện để thay đổi avatar, khung và hiệu ứng tên**,
Để **tùy chỉnh appearance theo sở thích và thể hiện achievements**.

#### Acceptance Criteria

1. Tạo `AvatarCustomization` page trong `/app/dashboard/avatar/page.tsx`
2. Tabs cho từng loại item:
   - **Avatars**: Grid hiển thị 30 avatars với unlock status
   - **Frames**: Grid hiển thị frames theo tier và achievements
   - **Name Effects**: Preview effects với unlock requirements
3. Item states với visual indicators:
   - **Owned**: Có thể equip ngay (green border)
   - **Unlockable**: Hiển thị requirements (yellow border)
   - **Locked**: Grayed out với unlock conditions (gray border)
4. Preview functionality trước khi equip
5. Equip/unequip với real-time updates
6. Search và filter functionality cho large collections
7. Responsive design cho mobile devices

#### Integration Verification

- **IV1**: Item grids load efficiently với lazy loading
- **IV2**: Preview accurately reflects final appearance
- **IV3**: Equip actions update immediately across app
- **IV4**: Mobile interface intuitive và touch-friendly

### Story 4.4: Collection Progress và Achievement Tracking

Là một **sinh viên**,
Tôi muốn **theo dõi tiến độ sưu tập và achievements**,
Để **có motivation collect tất cả items và unlock rare items**.

#### Acceptance Criteria

1. Tạo `CollectionProgress` component trong avatar customization
2. Hiển thị:
   - Overall collection percentage với animated progress bar
   - Progress bars cho từng category (Avatars, Frames, Emojis)
   - Rarity breakdown (Common, Rare, Epic, Legendary counts)
   - Next unlock milestones với requirements
3. Achievement badges cho collection milestones:
   - "First Collection" (5 items)
   - "Collector" (15 items)
   - "Master Collector" (25 items)
   - "Completionist" (all items)
4. Statistics dashboard với:
   - Total items owned
   - Rarest item owned
   - Collection rank among users
5. Filter và search functionality
6. Export collection summary feature

#### Integration Verification

- **IV1**: Progress calculations accurate và real-time
- **IV2**: Achievement badges unlock correctly
- **IV3**: Statistics reflect current collection state
- **IV4**: Performance optimized cho large datasets

### Story 4.5: Emoji System Integration

Là một **sinh viên**,
Tôi muốn **sử dụng emoji system để express reactions**,
Để **có thêm cách tương tác xã hội trong quiz và leaderboard**.

#### Acceptance Criteria

1. Tạo `EmojiPicker` component trong `/components/features/avatar/`
2. 9 categories với organized layout:
   - GENERAL, HAPPY, SAD, ANGRY, SURPRISED, LOVE, CELEBRATION, ANIMALS, SPECIAL
3. Unlock status cho từng emoji với visual indicators
4. Integration points:
   - Quiz completion reactions
   - Leaderboard interactions
   - Profile customization
   - Chat/messaging features (future)
5. Emoji preview với hover animations
6. Recent/favorite emoji shortcuts
7. Search functionality trong emoji picker

#### Integration Verification

- **IV1**: Emoji categories load correctly với proper icons
- **IV2**: Unlock system works với level progression
- **IV3**: Emoji interactions work trong quiz và social features
- **IV4**: Picker performance smooth với large emoji sets

### Story 4.6: Avatar Integration vào Existing Components

Là một **user**,
Tôi muốn **thấy avatar customization reflected across toàn bộ app**,
Để **có consistent visual identity và thể hiện progression**.

#### Acceptance Criteria

1. Update existing components để hiển thị avatar:
   - **UserLevelBadge**: Thêm avatar display option
   - **Dashboard**: Avatar section với quick customization access
   - **Leaderboard**: Avatar hiển thị cho tất cả users
   - **Quiz interface**: Avatar trong user info panel
2. Name effects hiển thị consistently:
   - Leaderboard names với tier effects
   - Quiz participant names
   - Profile displays
   - Achievement notifications
3. Frame integration:
   - Profile pictures với frames
   - Achievement displays
   - Social interaction contexts
4. Performance optimization:
   - Image preloading cho common avatars
   - Lazy loading cho avatar grids
   - CDN caching cho static assets
5. Responsive design cho mobile devices
6. Accessibility features cho screen readers

#### Integration Verification

- **IV1**: Avatar displays consistently across all components
- **IV2**: Name effects render correctly trong different contexts
- **IV3**: Performance maintained với avatar loading
- **IV4**: Accessibility standards met

### Compatibility Requirements

- [x] Existing gamification system enhanced với avatar features
- [x] Level progression triggers avatar unlocks automatically
- [x] Mobile responsiveness cho tất cả avatar components
- [x] API backward compatibility với existing user data
- [x] Visual consistency với existing design system
- [x] Performance impact minimal với optimized asset loading

### Risk Mitigation

- **Primary Risk**: Large number of avatar assets impact loading performance
- **Mitigation**:
  - Implement lazy loading cho avatar grids
  - Image optimization và compression (WebP format)
  - Progressive loading với skeleton placeholders
  - CDN caching cho static assets
  - Asset bundling optimization

### Definition of Done

- [x] All stories completed với acceptance criteria met
- [x] Avatar system fully integrated vào frontend
- [x] Customization interface intuitive và responsive
- [x] Collection progress tracking accurate và motivating
- [x] Emoji system functional và engaging
- [x] Avatar displays consistent across entire app
- [x] No regression trong existing gamification features
- [x] Performance optimized với proper asset management
- [x] Sinh viên có rich customization experience với 30 avatars, tier-based frames, name effects, và emoji system

---

_PRD này được tạo để đảm bảo việc tái cấu trúc frontend được thực hiện một cách an toàn, có hệ thống và không ảnh hưởng đến functionality hiện có._

**CRITICAL NOTE**: This PRD implements a **NO TESTING POLICY**. All development work must completely eliminate testing considerations and focus solely on functionality implementation with quality assurance through TypeScript compilation and basic runtime verification.
