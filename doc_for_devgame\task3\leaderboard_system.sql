-- =====================================================
-- LEADERBOARD & RANKING SYSTEM DATABASE SCHEMA
-- Task 3.2: <PERSON><PERSON>ng xếp hạng và leaderboard
-- =====================================================

-- 1. LEADERBOARD ENTRIES TABLE - Lưu trữ điểm số và thứ hạng
CREATE TABLE IF NOT EXISTS "LeaderboardEntries" (
    "entry_id" SERIAL PRIMARY KEY,
    "user_id" INTEGER NOT NULL REFERENCES "Users"("user_id") ON DELETE CASCADE,
    "leaderboard_type" VARCHAR(50) NOT NULL, -- 'GLOBAL', 'TIER_BASED', 'WEEKLY', 'MONTHLY', 'DAILY'
    "ranking_criteria" VARCHAR(50) NOT NULL, -- 'TOTAL_XP', 'LEVEL', 'QUIZ_SCORE', 'WIN_RATE', 'STREAK', 'SOCIAL_SCORE'
    "score_value" BIGINT NOT NULL DEFAULT 0,
    "current_rank" INTEGER,
    "previous_rank" INTEGER,
    "rank_change" INTEGER DEFAULT 0, -- positive = moved up, negative = moved down
    "tier_filter" VARCHAR(20), -- 'WOOD', 'BRONZE', etc. for tier-based leaderboards
    "time_period" DATE, -- for time-based leaderboards (daily/weekly/monthly)
    "last_updated" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "created_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 2. LEADERBOARD SNAPSHOTS TABLE - Lưu trữ snapshot theo thời gian
CREATE TABLE IF NOT EXISTS "LeaderboardSnapshots" (
    "snapshot_id" SERIAL PRIMARY KEY,
    "leaderboard_type" VARCHAR(50) NOT NULL,
    "ranking_criteria" VARCHAR(50) NOT NULL,
    "tier_filter" VARCHAR(20),
    "snapshot_date" DATE NOT NULL,
    "snapshot_data" JSONB NOT NULL, -- Array of {user_id, rank, score, username, avatar, etc.}
    "total_participants" INTEGER DEFAULT 0,
    "created_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 3. USER PERFORMANCE STATS TABLE - Thống kê hiệu suất chi tiết
CREATE TABLE IF NOT EXISTS "UserPerformanceStats" (
    "stats_id" SERIAL PRIMARY KEY,
    "user_id" INTEGER NOT NULL REFERENCES "Users"("user_id") ON DELETE CASCADE,
    "time_period" VARCHAR(20) NOT NULL, -- 'DAILY', 'WEEKLY', 'MONTHLY', 'ALL_TIME'
    "period_date" DATE NOT NULL,
    
    -- Quiz Performance Stats
    "total_quizzes_played" INTEGER DEFAULT 0,
    "total_questions_answered" INTEGER DEFAULT 0,
    "total_correct_answers" INTEGER DEFAULT 0,
    "accuracy_rate" DECIMAL(5,2) DEFAULT 0.00, -- percentage
    "average_score" DECIMAL(10,2) DEFAULT 0.00,
    "highest_score" INTEGER DEFAULT 0,
    "total_score_earned" BIGINT DEFAULT 0,
    
    -- Ranking Performance
    "first_place_finishes" INTEGER DEFAULT 0,
    "top_3_finishes" INTEGER DEFAULT 0,
    "top_5_finishes" INTEGER DEFAULT 0,
    "average_rank" DECIMAL(5,2) DEFAULT 0.00,
    "best_rank" INTEGER DEFAULT 999,
    
    -- Streak & Speed Stats
    "longest_streak" INTEGER DEFAULT 0,
    "total_streaks_achieved" INTEGER DEFAULT 0,
    "average_answer_time" DECIMAL(8,3) DEFAULT 0.000, -- seconds
    "fastest_answer_time" DECIMAL(8,3) DEFAULT 0.000,
    "speed_bonus_earned" INTEGER DEFAULT 0,
    
    -- Currency & Rewards
    "syncoin_earned" INTEGER DEFAULT 0,
    "kristal_earned" INTEGER DEFAULT 0,
    "xp_earned" INTEGER DEFAULT 0,
    "eggs_received" INTEGER DEFAULT 0,
    
    -- Social & Interaction
    "emojis_used" INTEGER DEFAULT 0,
    "social_interactions_sent" INTEGER DEFAULT 0,
    "social_interactions_received" INTEGER DEFAULT 0,
    
    "created_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 4. LEADERBOARD ACHIEVEMENTS TABLE - Thành tích đặc biệt
CREATE TABLE IF NOT EXISTS "LeaderboardAchievements" (
    "achievement_id" SERIAL PRIMARY KEY,
    "user_id" INTEGER NOT NULL REFERENCES "Users"("user_id") ON DELETE CASCADE,
    "achievement_type" VARCHAR(50) NOT NULL, -- 'FIRST_PLACE', 'TOP_3_STREAK', 'TIER_CHAMPION', etc.
    "achievement_name" VARCHAR(100) NOT NULL,
    "achievement_description" TEXT,
    "leaderboard_type" VARCHAR(50) NOT NULL,
    "ranking_criteria" VARCHAR(50) NOT NULL,
    "tier_filter" VARCHAR(20),
    "achievement_date" DATE NOT NULL,
    "achievement_metadata" JSONB DEFAULT '{}', -- Additional data like streak count, score, etc.
    "reward_syncoin" INTEGER DEFAULT 0,
    "reward_kristal" INTEGER DEFAULT 0,
    "reward_xp" INTEGER DEFAULT 0,
    "is_claimed" BOOLEAN DEFAULT FALSE,
    "created_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 5. RANKING HISTORY TABLE - Lịch sử thay đổi thứ hạng
CREATE TABLE IF NOT EXISTS "RankingHistory" (
    "history_id" SERIAL PRIMARY KEY,
    "user_id" INTEGER NOT NULL REFERENCES "Users"("user_id") ON DELETE CASCADE,
    "leaderboard_type" VARCHAR(50) NOT NULL,
    "ranking_criteria" VARCHAR(50) NOT NULL,
    "old_rank" INTEGER,
    "new_rank" INTEGER,
    "rank_change" INTEGER,
    "old_score" BIGINT,
    "new_score" BIGINT,
    "score_change" BIGINT,
    "tier_filter" VARCHAR(20),
    "change_reason" VARCHAR(100), -- 'QUIZ_COMPLETION', 'DAILY_RESET', 'TIER_PROMOTION', etc.
    "created_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- INDEXES FOR PERFORMANCE OPTIMIZATION
-- =====================================================

-- LeaderboardEntries indexes
CREATE INDEX IF NOT EXISTS "idx_leaderboard_entries_user_type_criteria" 
ON "LeaderboardEntries"("user_id", "leaderboard_type", "ranking_criteria");

CREATE INDEX IF NOT EXISTS "idx_leaderboard_entries_type_criteria_rank" 
ON "LeaderboardEntries"("leaderboard_type", "ranking_criteria", "current_rank");

CREATE INDEX IF NOT EXISTS "idx_leaderboard_entries_tier_score" 
ON "LeaderboardEntries"("tier_filter", "score_value" DESC);

CREATE INDEX IF NOT EXISTS "idx_leaderboard_entries_time_period" 
ON "LeaderboardEntries"("time_period", "leaderboard_type");

-- UserPerformanceStats indexes
CREATE INDEX IF NOT EXISTS "idx_user_performance_user_period" 
ON "UserPerformanceStats"("user_id", "time_period", "period_date");

CREATE INDEX IF NOT EXISTS "idx_user_performance_period_date" 
ON "UserPerformanceStats"("time_period", "period_date");

-- LeaderboardSnapshots indexes
CREATE INDEX IF NOT EXISTS "idx_leaderboard_snapshots_type_date" 
ON "LeaderboardSnapshots"("leaderboard_type", "ranking_criteria", "snapshot_date");

-- LeaderboardAchievements indexes
CREATE INDEX IF NOT EXISTS "idx_leaderboard_achievements_user_type" 
ON "LeaderboardAchievements"("user_id", "achievement_type");

CREATE INDEX IF NOT EXISTS "idx_leaderboard_achievements_date" 
ON "LeaderboardAchievements"("achievement_date");

-- RankingHistory indexes
CREATE INDEX IF NOT EXISTS "idx_ranking_history_user_type" 
ON "RankingHistory"("user_id", "leaderboard_type", "ranking_criteria");

CREATE INDEX IF NOT EXISTS "idx_ranking_history_created_at" 
ON "RankingHistory"("created_at");

-- =====================================================
-- INITIAL DATA SETUP
-- =====================================================

-- Add unique constraint for all leaderboard entries
ALTER TABLE "LeaderboardEntries"
ADD CONSTRAINT "unique_leaderboard_entry"
UNIQUE ("user_id", "leaderboard_type", "ranking_criteria", "tier_filter");

-- Insert initial global leaderboard entries
INSERT INTO "LeaderboardEntries" ("user_id", "leaderboard_type", "ranking_criteria", "score_value", "current_rank")
SELECT
    u."user_id",
    'GLOBAL' as leaderboard_type,
    'TOTAL_XP' as ranking_criteria,
    COALESCE(u."total_points", 0) as score_value,
    ROW_NUMBER() OVER (ORDER BY COALESCE(u."total_points", 0) DESC) as current_rank
FROM "Users" u
WHERE u."role_id" = 3 -- Only students
ON CONFLICT ("user_id", "leaderboard_type", "ranking_criteria", "tier_filter") DO NOTHING;

-- Insert level-based leaderboard entries
INSERT INTO "LeaderboardEntries" ("user_id", "leaderboard_type", "ranking_criteria", "score_value", "current_rank")
SELECT
    u."user_id",
    'GLOBAL' as leaderboard_type,
    'LEVEL' as ranking_criteria,
    COALESCE(u."current_level", 1) as score_value,
    ROW_NUMBER() OVER (ORDER BY COALESCE(u."current_level", 1) DESC, COALESCE(u."total_points", 0) DESC) as current_rank
FROM "Users" u
WHERE u."role_id" = 3 -- Only students
ON CONFLICT ("user_id", "leaderboard_type", "ranking_criteria", "tier_filter") DO NOTHING;

-- =====================================================
-- STORED PROCEDURES FOR LEADERBOARD OPERATIONS
-- =====================================================

-- Function to update user leaderboard entry
CREATE OR REPLACE FUNCTION update_leaderboard_entry(
    p_user_id INTEGER,
    p_leaderboard_type VARCHAR(50),
    p_ranking_criteria VARCHAR(50),
    p_new_score BIGINT,
    p_tier_filter VARCHAR(20) DEFAULT NULL
) RETURNS VOID AS $$
DECLARE
    v_old_rank INTEGER;
    v_new_rank INTEGER;
    v_old_score BIGINT;
BEGIN
    -- Get current rank and score
    SELECT current_rank, score_value INTO v_old_rank, v_old_score
    FROM "LeaderboardEntries"
    WHERE user_id = p_user_id 
      AND leaderboard_type = p_leaderboard_type 
      AND ranking_criteria = p_ranking_criteria
      AND (tier_filter = p_tier_filter OR (tier_filter IS NULL AND p_tier_filter IS NULL));
    
    -- Update or insert entry
    INSERT INTO "LeaderboardEntries" (
        user_id, leaderboard_type, ranking_criteria, score_value, tier_filter, last_updated
    ) VALUES (
        p_user_id, p_leaderboard_type, p_ranking_criteria, p_new_score, p_tier_filter, CURRENT_TIMESTAMP
    )
    ON CONFLICT (user_id, leaderboard_type, ranking_criteria, COALESCE(tier_filter, ''))
    DO UPDATE SET 
        score_value = p_new_score,
        previous_rank = current_rank,
        last_updated = CURRENT_TIMESTAMP;
    
    -- Recalculate ranks for this leaderboard
    WITH ranked_users AS (
        SELECT user_id, 
               ROW_NUMBER() OVER (ORDER BY score_value DESC) as new_rank
        FROM "LeaderboardEntries"
        WHERE leaderboard_type = p_leaderboard_type 
          AND ranking_criteria = p_ranking_criteria
          AND (tier_filter = p_tier_filter OR (tier_filter IS NULL AND p_tier_filter IS NULL))
    )
    UPDATE "LeaderboardEntries" le
    SET current_rank = ru.new_rank,
        rank_change = COALESCE(le.previous_rank, 999) - ru.new_rank,
        updated_at = CURRENT_TIMESTAMP
    FROM ranked_users ru
    WHERE le.user_id = ru.user_id
      AND le.leaderboard_type = p_leaderboard_type
      AND le.ranking_criteria = p_ranking_criteria
      AND (le.tier_filter = p_tier_filter OR (le.tier_filter IS NULL AND p_tier_filter IS NULL));
    
    -- Record ranking history if rank changed
    SELECT current_rank INTO v_new_rank
    FROM "LeaderboardEntries"
    WHERE user_id = p_user_id 
      AND leaderboard_type = p_leaderboard_type 
      AND ranking_criteria = p_ranking_criteria
      AND (tier_filter = p_tier_filter OR (tier_filter IS NULL AND p_tier_filter IS NULL));
    
    IF v_old_rank IS DISTINCT FROM v_new_rank THEN
        INSERT INTO "RankingHistory" (
            user_id, leaderboard_type, ranking_criteria, old_rank, new_rank, 
            rank_change, old_score, new_score, score_change, tier_filter, change_reason
        ) VALUES (
            p_user_id, p_leaderboard_type, p_ranking_criteria, v_old_rank, v_new_rank,
            COALESCE(v_old_rank, 999) - v_new_rank, v_old_score, p_new_score, 
            p_new_score - COALESCE(v_old_score, 0), p_tier_filter, 'SCORE_UPDATE'
        );
    END IF;
END;
$$ LANGUAGE plpgsql;
