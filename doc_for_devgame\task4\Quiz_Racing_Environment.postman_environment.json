{"id": "quiz-racing-environment", "name": "Quiz Racing & Skills Environment", "values": [{"key": "baseUrl", "value": "http://localhost:8888/api", "description": "Base API URL for local development", "enabled": true}, {"key": "socketUrl", "value": "http://localhost:8888", "description": "WebSocket server URL", "enabled": true}, {"key": "authToken", "value": "", "description": "JWT authentication token", "enabled": true}, {"key": "userId", "value": "1", "description": "Current user ID", "enabled": true}, {"key": "sessionId", "value": "", "description": "Current quiz racing session ID", "enabled": true}, {"key": "quizId", "value": "1", "description": "Test quiz ID", "enabled": true}, {"key": "student1Username", "value": "student1", "description": "Test student 1 username", "enabled": true}, {"key": "student1Password", "value": "password123", "description": "Test student 1 password", "enabled": true}, {"key": "student2Username", "value": "student2", "description": "Test student 2 username", "enabled": true}, {"key": "student2Password", "value": "password123", "description": "Test student 2 password", "enabled": true}, {"key": "testSkillIds", "value": "[1,5,8,13]", "description": "Test skill IDs for loadout (Attack, Defense, Burst, Special)", "enabled": true}], "_postman_variable_scope": "environment"}