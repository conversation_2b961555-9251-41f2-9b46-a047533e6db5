# QL_CTDT Brownfield Architecture Document

## Introduction

Tài liệu này ghi lại TRẠNG THÁI HIỆN TẠI của hệ thống QL_CTDT (Quản lý Chương trình Đào tạo) sau khi hoàn thành việc tái cấu trúc frontend, bao gồm technical debt, workarounds, và các patterns thực tế. Frontend đã được tái tổ chức hoàn toàn theo feature-based architecture với separation of concerns rõ ràng. Đây là tài liệu tham khảo cho AI agents làm việc trên các enhancements.

### Document Scope

Tài liệu toàn diện cho toàn bộ hệ thống

### Change Log

| Date       | Version | Description                                                       | Author              |
| ---------- | ------- | ----------------------------------------------------------------- | ------------------- |
| 2025-01-23 | 1.0     | Initial brownfield analysis                                       | Business Analyst    |
| 2025-07-26 | 2.0     | Updated after frontend restructuring - feature-based architecture | Winston (Architect) |

## Quick Reference - Key Files and Entry Points

### Critical Files for Understanding the System

- **Backend Entry**: `backend/src/server.js` - Main server entry point
- **Backend App**: `backend/src/app.js` - Express app configuration
- **Frontend Entry**: `frontend/src/app/layout.tsx` - Next.js root layout
- **Socket.IO**: `backend/src/socket.js` - WebSocket server setup
- **Database Models**: `backend/src/models/index.js` - Sequelize ORM setup
- **API Routes**: `backend/src/routes/` - All API endpoint definitions
- **Frontend Services**: `frontend/src/lib/services/api/` - API client services (moved to lib structure)
- **Frontend Components**: `frontend/src/components/features/` - Feature-based business components
- **Frontend UI**: `frontend/src/components/ui/` - Categorized Radix UI components

### Key Business Logic Files

- **Quiz Controller**: `backend/src/controllers/quizController.js` - Core quiz logic (3400+ lines)
- **Quiz Realtime Service**: `backend/src/services/quizRealtimeService.js` - Real-time quiz management
- **Auth Middleware**: `backend/src/middleware/authMiddleware.js` - JWT authentication
- **Redis Utils**: `backend/src/redis/utils.js` - Cache management
- **Socket Service**: `frontend/src/services/socket/index.ts` - Frontend WebSocket client

## High Level Architecture

### Technical Summary

**Monorepo Structure**: pnpm workspace với frontend và backend riêng biệt
**Deployment**: Docker Compose với 5 services (postgres, redis, backend, frontend, nginx)
**Real-time**: Socket.IO cho quiz tương tác
**Authentication**: JWT với role-based access control

### Actual Tech Stack (from package.json)

| Category           | Technology   | Version  | Notes                   |
| ------------------ | ------------ | -------- | ----------------------- |
| Package Manager    | pnpm         | 8.15.0   | Workspace configuration |
| Backend Runtime    | Node.js      | >=18.0.0 | Express.js framework    |
| Backend Framework  | Express      | 5.1.0    | Latest major version    |
| Frontend Framework | Next.js      | 15.3.0   | App Router với React 19 |
| Frontend Runtime   | React        | 19.0.0   | Latest version          |
| Language           | TypeScript   | ^5       | Frontend only           |
| Database           | PostgreSQL   | 15       | Docker container        |
| Cache              | Redis        | 7        | Session và quiz state   |
| ORM                | Sequelize    | 6.37.7   | PostgreSQL integration  |
| Real-time          | Socket.IO    | 4.8.1    | Client và server        |
| UI Library         | Radix UI     | Latest   | Component primitives    |
| Styling            | Tailwind CSS | ^4       | Utility-first CSS       |
| Authentication     | JWT          | 9.0.2    | jsonwebtoken            |
| Password Hashing   | bcrypt       | 5.1.1    | User authentication     |
| File Processing    | xlsx         | 0.18.5   | Excel import/export     |
| Reverse Proxy      | Nginx        | latest   | SSL termination         |

### Repository Structure Reality Check

- **Type**: Monorepo với pnpm workspace
- **Package Manager**: pnpm (enforced via engines)
- **Notable**: Có nhiều file upload trong `backend/src/uploads/` (40+ files)

## Source Tree and Module Organization

### Project Structure (After Frontend Restructuring - Current State)

```text
QL_CTDT_FRONT/
├── frontend/                    # Next.js 15 application
│   ├── src/app/                # App Router structure (UNCHANGED)
│   │   ├── (auth)/             # Auth route group
│   │   ├── dashboard/          # Main dashboard
│   │   ├── quiz-live/          # Real-time quiz interface
│   │   ├── quiz-monitor/       # Teacher monitoring
│   │   └── quiz-waiting-room/  # Pre-quiz lobby
│   ├── src/components/         # React components (RESTRUCTURED)
│   │   ├── ui/                 # Radix UI components (CATEGORIZED)
│   │   │   ├── forms/          # Form components (button, input, select, etc.)
│   │   │   ├── navigation/     # Navigation components (breadcrumb, pagination, tabs)
│   │   │   ├── layout/         # Layout components (card, separator, scroll-area)
│   │   │   ├── feedback/       # Feedback components (badge, dialog, tooltip, etc.)
│   │   │   ├── display/        # Display components (heading, logo, table)
│   │   │   └── index.ts        # Comprehensive barrel exports
│   │   └── features/           # Feature-based business components (NEW STRUCTURE)
│   │       ├── auth/           # Authentication components (login-form, register-form, role-guard)
│   │       ├── charts/         # Analytics & visualization (20+ chart components)
│   │       ├── gamification/   # Gamification features (leaderboard, streak-display, user-level-badge)
│   │       ├── learning/       # Learning features (ChapterRecommendations)
│   │       ├── navigation/     # Navigation components (sidebar, nav-items, top-navbar)
│   │       ├── quiz/           # Quiz-related components (create/, detail/, forms/, list/, live/, waiting-room/)
│   │       ├── subject/        # Subject management (subject-select)
│   │       └── shared/         # Shared components (loading, mode-toggle, theme-provider)
│   ├── src/lib/                # Centralized utilities and shared logic (REORGANIZED)
│   │   ├── auth/               # Authentication utilities (token-utils, role-manager)
│   │   ├── hooks/              # Custom React hooks (13+ hooks + quiz subdirectory)
│   │   ├── services/           # API & Socket services (api/, socket/)
│   │   ├── types/              # TypeScript definitions (7+ type files)
│   │   ├── utils/              # Utility functions (utils, export-utils, toast-utils)
│   │   ├── validations/        # Validation schemas
│   │   └── constants/          # Application constants (api, ui, validation, app)
├── backend/                    # Node.js/Express API
│   ├── src/controllers/        # 35+ controllers (business logic)
│   ├── src/models/             # 25+ Sequelize models
│   ├── src/routes/             # API route definitions
│   ├── src/services/           # Business services
│   ├── src/middleware/         # Auth, upload, session middleware
│   ├── src/migrations/         # 25+ database migrations
│   ├── src/redis/              # Redis connection và utils
│   └── src/uploads/            # File upload storage (40+ files)
├── docker-compose.yml          # 5-service deployment
├── nginx.conf                  # Reverse proxy config
└── pnpm-workspace.yaml         # Workspace configuration
```

### Key Modules and Their Purpose

**Backend Modules (Unchanged):**

- **Quiz Management**: `backend/src/controllers/quizController.js` - MASSIVE file (3400+ lines) handling all quiz operations
- **Real-time Service**: `backend/src/services/quizRealtimeService.js` - Socket.IO quiz management
- **Authentication**: `backend/src/middleware/authMiddleware.js` - JWT validation và role checking
- **Database Models**: `backend/src/models/` - 25+ Sequelize models với complex relationships
- **API Routes**: `backend/src/routes/` - 25+ route files, one per entity

**Frontend Modules (Restructured):**

- **UI Components**: `frontend/src/components/ui/` - Categorized Radix UI components (forms, navigation, layout, feedback, display)
- **Feature Components**: `frontend/src/components/features/` - Business logic components organized by feature (auth, quiz, charts, etc.)
- **Authentication**: `frontend/src/lib/auth/` - Token management và role-based access
- **Socket Client**: `frontend/src/lib/services/socket/` - WebSocket client management (moved to lib structure)
- **Custom Hooks**: `frontend/src/lib/hooks/` - React hooks organized with barrel exports
- **Utilities**: `frontend/src/lib/utils/` - Centralized utility functions
- **Constants**: `frontend/src/lib/constants/` - Application-wide constants (api, ui, validation, app)
- **Types**: `frontend/src/lib/types/` - TypeScript definitions centralized

## Frontend Architecture Details (Post-Restructuring)

### Component Organization Strategy

**Feature-Based Architecture**: Components được tổ chức theo business features thay vì technical types, cải thiện maintainability và developer experience.

#### UI Components Structure (`components/ui/`)

```text
components/ui/
├── forms/              # Interactive form elements
│   ├── button.tsx      # Primary action buttons
│   ├── input.tsx       # Text input fields
│   ├── select.tsx      # Dropdown selections
│   ├── checkbox.tsx    # Boolean selections
│   ├── radio-group.tsx # Single choice selections
│   ├── form.tsx        # Form wrapper components
│   ├── label.tsx       # Form labels
│   └── slider.tsx      # Range inputs
├── navigation/         # Navigation components
│   ├── breadcrumb.tsx  # Breadcrumb navigation
│   ├── pagination.tsx  # Page navigation
│   └── tabs.tsx        # Tab navigation
├── layout/             # Structural components
│   ├── card.tsx        # Content containers
│   ├── separator.tsx   # Visual separators
│   ├── scroll-area.tsx # Scrollable containers
│   └── collapsible.tsx # Expandable sections
├── feedback/           # User feedback components
│   ├── badge.tsx       # Status indicators
│   ├── dialog.tsx      # Modal dialogs
│   ├── tooltip.tsx     # Contextual help
│   ├── progress.tsx    # Progress indicators
│   ├── skeleton.tsx    # Loading placeholders
│   └── empty-state.tsx # Empty state displays
├── display/            # Data presentation
│   ├── heading.tsx     # Typography headings
│   ├── table.tsx       # Data tables
│   └── logo.tsx        # Brand elements
└── index.ts            # Comprehensive barrel exports
```

#### Feature Components Structure (`components/features/`)

```text
components/features/
├── auth/               # Authentication features
│   ├── login-form.tsx  # User login interface
│   ├── register-form.tsx # User registration
│   ├── role-guard.tsx  # Role-based access control
│   └── index.ts        # Auth barrel exports
├── quiz/               # Quiz management features
│   ├── create/         # Quiz creation workflow
│   ├── detail/         # Quiz detail views
│   ├── forms/          # Quiz form components
│   ├── list/           # Quiz listing components
│   ├── live/           # Real-time quiz interface
│   ├── waiting-room/   # Pre-quiz lobby
│   └── index.ts        # Quiz barrel exports
├── charts/             # Analytics & visualization
│   ├── [20+ chart components for analytics]
│   └── index.ts        # Charts barrel exports
├── navigation/         # App navigation
│   ├── sidebar.tsx     # Main navigation sidebar
│   ├── top-navbar/     # Top navigation bar
│   └── index.ts        # Navigation barrel exports
├── gamification/       # Gamification features
│   ├── leaderboard.tsx # Competition rankings
│   ├── streak-display.tsx # Learning streaks
│   ├── user-level-badge.tsx # Achievement badges
│   └── index.ts        # Gamification barrel exports
├── learning/           # Learning features
│   ├── ChapterRecommendations.tsx # AI recommendations
│   └── index.ts        # Learning barrel exports
├── subject/            # Subject management
│   ├── subject-select.tsx # Subject selection
│   └── index.ts        # Subject barrel exports
└── shared/             # Shared feature components
    ├── loading/        # Loading states
    ├── mode-toggle.tsx # Theme switching
    ├── theme-provider.tsx # Theme context
    └── index.ts        # Shared barrel exports
```

### Lib Structure Organization (`lib/`)

```text
lib/
├── auth/               # Authentication utilities
│   ├── token-utils.ts  # JWT token management
│   ├── role-manager.ts # Role-based permissions
│   └── index.ts        # Auth utilities barrel export
├── hooks/              # Custom React hooks
│   ├── quiz/           # Quiz-specific hooks
│   ├── use-auth.ts     # Authentication hooks
│   ├── use-api.ts      # API interaction hooks
│   └── index.ts        # Hooks barrel export
├── services/           # External service integrations
│   ├── api/            # REST API services
│   ├── socket/         # Socket.IO client
│   └── index.ts        # Services barrel export
├── utils/              # Utility functions
│   ├── utils.ts        # General utilities
│   ├── export-utils.ts # Data export utilities
│   ├── toast-utils.ts  # Notification utilities
│   └── index.ts        # Utils barrel export
├── types/              # TypeScript definitions
│   ├── api.ts          # API response types
│   ├── auth.ts         # Authentication types
│   ├── quiz.ts         # Quiz-related types
│   └── index.ts        # Types barrel export
├── constants/          # Application constants
│   ├── api.ts          # API endpoints & config
│   ├── ui.ts           # UI constants (colors, sizes)
│   ├── validation.ts   # Validation rules
│   ├── app.ts          # App-wide constants
│   └── index.ts        # Constants barrel export
└── validations/        # Validation schemas
    ├── auth.ts         # Authentication validation
    ├── quiz.ts         # Quiz validation
    └── index.ts        # Validations barrel export
```

### Import Path Standards

**Standardized Import Patterns**:

- UI Components: `@/components/ui/{category}/{component}`
- Feature Components: `@/components/features/{feature}/{component}`
- Utilities: `@/lib/{category}` (uses barrel exports)
- Types: `@/lib/types` (centralized type exports)
- Constants: `@/lib/constants` (categorized constants)

**Barrel Export Benefits**:

- Simplified import statements
- Better tree-shaking optimization
- Consistent import patterns across codebase
- Easy refactoring and maintenance

### Architecture Principles Achieved

1. **Separation of Concerns**: UI components separated from business logic components
2. **Feature-Based Organization**: Components grouped by business functionality
3. **Single Source of Truth**: Centralized utilities, types, and constants
4. **Developer Experience**: Consistent import patterns and organized structure
5. **Maintainability**: Clear boundaries between different types of components
6. **Scalability**: Structure supports easy addition of new features and components

## Data Models and APIs

### Data Models

Thay vì duplicate, tham khảo các model files thực tế:

- **Core Models**: `backend/src/models/` - 25+ models
- **User Model**: `backend/src/models/user.js` - Includes gamification fields
- **Quiz Model**: `backend/src/models/quiz.js` - Real-time quiz support
- **Question Model**: `backend/src/models/question.js` - Learning outcomes mapping
- **Database Schema**: `data.sql` - Full PostgreSQL dump (3000+ lines)

### API Specifications

- **Route Files**: `backend/src/routes/` - 25+ route definitions
- **Controllers**: `backend/src/controllers/` - 35+ controller files
- **Authentication**: JWT Bearer token required for most endpoints
- **Real-time Events**: Socket.IO events documented in quiz service

## Technical Debt and Known Issues

### Critical Technical Debt

**Backend Technical Debt (Unchanged):**

1. **Massive Quiz Controller**: `quizController.js` có 3400+ lines - cần refactor thành smaller services
2. **File Upload Management**: 40+ files trong `uploads/` directory - không có cleanup mechanism
3. **Database Migrations**: Một số migrations có naming inconsistencies (dates vs timestamps)
4. **Error Handling**: Inconsistent error response formats across controllers
5. **Socket.IO Rooms**: Complex room management logic scattered across multiple files

**Frontend Technical Debt (RESOLVED through restructuring):**

1. ✅ **Component Organization**: Previously scattered components now organized in feature-based structure
2. ✅ **Import Path Consistency**: All imports now use standardized paths with barrel exports
3. ✅ **Code Duplication**: Duplicate UI components consolidated and removed
4. ✅ **Utilities Centralization**: All utilities, hooks, and shared logic centralized in lib/ structure
5. ✅ **Constants Management**: Application constants consolidated into categorized files
6. ✅ **Testing Infrastructure**: All test files and configurations removed per NO TESTING POLICY

### Workarounds and Gotchas

- **CORS Configuration**: Hardcoded nhiều localhost ports (3000-3010) trong `app.js`
- **Redis Connection**: Reconnection strategy có thể fail sau 20 attempts
- **Socket.IO Path**: Must match `/socket.io/` trong nginx config
- **JWT Secret**: Phải set trong environment variables
- **Database Pool**: Hardcoded max 5 connections - có thể bottleneck

### Performance Issues

**Backend Performance Issues (Unchanged):**

- **Quiz Controller**: Single massive file xử lý tất cả quiz operations
- **File Uploads**: Không có file size limits hoặc cleanup
- **Database Queries**: Một số N+1 query patterns trong controllers
- **Socket.IO**: Không có rate limiting cho real-time events

**Frontend Performance Improvements (Achieved through restructuring):**

- ✅ **Bundle Optimization**: Barrel exports enable better tree-shaking
- ✅ **Import Efficiency**: Categorized components reduce import overhead
- ✅ **Code Splitting**: Feature-based organization supports better code splitting
- ✅ **Developer Experience**: Faster development with organized structure and consistent imports

## Integration Points and External Dependencies

### External Services

| Service    | Purpose            | Integration Type | Key Files                        |
| ---------- | ------------------ | ---------------- | -------------------------------- |
| Firebase   | Real-time database | Admin SDK        | `backend/src/config/firebase.js` |
| PostgreSQL | Primary database   | Sequelize ORM    | `backend/src/models/index.js`    |
| Redis      | Cache và sessions  | Direct client    | `backend/src/redis/redis.js`     |

### Internal Integration Points

- **Frontend-Backend**: REST API trên port 8888, Socket.IO cho real-time
- **Authentication**: JWT tokens với role-based middleware (frontend auth utilities in `lib/auth/`)
- **File Storage**: Local uploads directory (needs cleanup strategy)
- **Database**: Sequelize ORM với 25+ models và complex relationships
- **Frontend Services**: Centralized API services in `lib/services/api/` with barrel exports
- **Frontend State**: Custom hooks in `lib/hooks/` for state management and business logic
- **Frontend Types**: Centralized TypeScript definitions in `lib/types/` for type safety

## Data Flow Patterns

### Quiz Real-time Data Flow

```mermaid
sequenceDiagram
    participant Student as 👨‍🎓 Student
    participant Frontend as Frontend
    participant Backend as Backend API
    participant Socket as Socket.IO
    participant Redis as Redis
    participant Firebase as Firebase
    participant DB as PostgreSQL

    Note over Student,DB: Real-time Quiz Flow

    Student->>Frontend: Join Quiz (PIN)
    Frontend->>Backend: POST /api/quizzes/join
    Backend->>DB: Validate quiz & user
    Backend->>Redis: Store session data
    Backend->>Firebase: Add participant
    Backend->>Socket: Join quiz rooms
    Socket-->>Frontend: Participant joined

    Note over Student,DB: Question Flow

    Backend->>Socket: Emit new question
    Socket-->>Frontend: Display question
    Student->>Frontend: Submit answer
    Frontend->>Backend: POST /api/quizzes/realtime/answer
    Backend->>DB: Store answer
    Backend->>Redis: Update scores
    Backend->>Firebase: Update leaderboard
    Backend->>Socket: Emit results
    Socket-->>Frontend: Show answer feedback
```

### Authentication Data Flow

```mermaid
flowchart TD
    A[User Login] --> B[POST /api/users/login]
    B --> C{Credentials Valid?}
    C -->|Yes| D[Generate JWT Token]
    C -->|No| E[Return 401 Error]
    D --> F[Store in localStorage]
    F --> G[Add to Request Headers]
    G --> H[Middleware Validation]
    H --> I{Token Valid?}
    I -->|Yes| J[Extract User Info]
    I -->|No| K[Return 401 Error]
    J --> L[Check Role Permissions]
    L --> M[Allow/Deny Access]
```

### File Upload Data Flow

```mermaid
flowchart LR
    A[Excel File] --> B[Multer Middleware]
    B --> C[Save to uploads/]
    C --> D[Parse with xlsx]
    D --> E[Validate Data]
    E --> F[Bulk Insert to DB]
    F --> G[Return Success]

    C --> H[⚠️ No Cleanup]
    H --> I[Disk Space Growth]
```

## Development and Deployment

### Local Development Setup

1. **Prerequisites**: Node.js >=18.0.0, pnpm >=8.0.0
2. **Install**: `pnpm install` (workspace-aware)
3. **Environment**: Copy `.env.example` và configure database/redis
4. **Database**: Run migrations với Sequelize CLI
5. **Development**: `pnpm dev` (runs both frontend và backend)

### Build and Deployment Process

- **Build Command**: `pnpm build` (builds both workspaces)
- **Deployment**: Docker Compose với 5 services
- **Environments**: Development, Production (Docker-based)
- **SSL**: Let's Encrypt certificates trong nginx
- **Ports**: Frontend:3000, Backend:8888, DB:5433, Redis:6379

### Known Setup Issues

- **Docker Volumes**: Backend volume mount có thể conflict với node_modules
- **Database Connection**: Phải wait for PostgreSQL health check
- **Redis Password**: Required trong production environment
- **Nginx Config**: Hardcoded domain `stardust.id.vn`

## Testing Policy

### Project Testing Strategy

**CRITICAL ARCHITECTURAL DECISION**: This project follows a **NO TESTING** policy.

- **Unit Tests**: KHÔNG ĐƯỢC TRIỂN KHAI - Project policy prohibits unit testing
- **Integration Tests**: KHÔNG ĐƯỢC TRIỂN KHAI - Project policy prohibits integration testing
- **E2E Tests**: KHÔNG ĐƯỢC TRIỂN KHAI - Project policy prohibits end-to-end testing
- **Manual Testing**: KHÔNG ĐƯỢC TRIỂN KHAI - Project policy prohibits manual testing procedures
- **Test Frameworks**: KHÔNG ĐƯỢC SỬ DỤNG - No testing frameworks allowed in this project

### Testing Commands

```bash
# NO TESTING COMMANDS AVAILABLE
# This project does not implement any form of testing
# All test-related scripts are disabled or removed
```

### Quality Assurance Approach

**Quality assurance relies solely on:**

- TypeScript compilation success
- Application startup without errors
- Basic functionality verification through normal usage
- Code review processes
- Runtime error monitoring (if implemented)

## Real-time Quiz System Details

### Socket.IO Implementation

- **Server**: `backend/src/socket.js` - Basic room management
- **Client**: `frontend/src/lib/services/socket/index.ts` - Singleton pattern (moved to lib structure)
- **Quiz Service**: `backend/src/services/quizRealtimeService.js` - Complex quiz logic
- **Rooms**: Multiple room types (quiz:ID, quiz:ID:teachers, quiz:ID:students)
- **Frontend Integration**: Socket client accessed through barrel exports from `lib/services/`

### Session Management

- **Redis Storage**: Quiz sessions stored với TTL
- **Firebase Integration**: Real-time participant data
- **State Tracking**: Complex state machine trong quiz service

### Known Real-time Issues

- **Connection Handling**: Manual reconnection logic
- **Room Cleanup**: Không có automatic cleanup
- **Error Recovery**: Limited error handling cho network issues

## Appendix - Useful Commands and Scripts

### Frequently Used Commands

```bash
pnpm dev           # Start both frontend và backend
pnpm build         # Build production bundles
pnpm --filter backend dev    # Backend only
pnpm --filter frontend dev   # Frontend only

# NO TEST COMMANDS AVAILABLE
# This project does not implement testing per architectural decision
```

### Database Commands

```bash
# Sequelize migrations (from backend directory)
npx sequelize-cli db:migrate
npx sequelize-cli db:seed:all
```

### Docker Commands

```bash
docker-compose up -d         # Start all services
docker-compose logs backend  # View backend logs
docker-compose down          # Stop all services
```

### Debugging and Troubleshooting

- **Backend Logs**: Check Docker logs hoặc console output
- **Database**: Connect directly to PostgreSQL on port 5433
- **Redis**: Use redis-cli để inspect cache data
- **Socket.IO**: Browser dev tools Network tab cho WebSocket traffic

## API Endpoints Summary

### Authentication & User Management

```http
POST /api/users/login                    # User authentication
GET /api/users                           # List users (Admin)
POST /api/users/createStudent            # Create student (Admin/Teacher)
POST /api/users/createTeacher            # Create teacher (Admin)
PUT /api/users/:id                       # Update user
```

### Program & Course Management

```http
GET /api/programs                        # List programs
POST /api/programs                       # Create program (Admin)
GET /api/courses                         # List courses
GET /api/subjects                        # List subjects
GET /api/chapters/subject/:subject_id    # Chapters by subject
```

### Quiz Management (Core System)

```http
GET /api/quizzes                         # List quizzes
POST /api/quizzes                        # Create quiz
POST /api/quizzes/:id/start              # Start quiz (Teacher)
POST /api/quizzes/:id/join               # Join quiz (Student)
POST /api/quizzes/realtime/answer        # Submit answer (Real-time)
GET /api/quizzes/:id/leaderboard         # Live leaderboard
GET /api/quizzes/pin/:pin                # Find quiz by PIN
```

### Question & Answer Management

```http
GET /api/questions                       # List questions
POST /api/questions                      # Create question
POST /api/questions/import-excel         # Import from Excel
GET /api/answers                         # List answers
POST /api/answers                        # Create answer
```

### Results & Analytics

```http
GET /api/quiz-results/user/:user_id      # User results
GET /api/quiz-results/quiz/:quiz_id      # Quiz results
GET /api/quizzes/:id/statistics          # Quiz statistics
GET /api/reports/program/:id/overview    # Program reports
```

### Learning Outcomes

```http
GET /api/pos/program/:program_id         # Program Outcomes
GET /api/plos/program/:program_id        # Program Learning Outcomes
GET /api/los/subject/:subjectId          # Learning Outcomes by subject
```

### Socket.IO Events (Real-time Quiz)

```javascript
// Client Events
"joinQuizRoom"; // Join quiz room
"submitAnswer"; // Submit answer
"leaveRoom"; // Leave room

// Server Events
"quizStarted"; // Quiz begins
"newQuestion"; // New question
"showAnswerResult"; // Answer feedback
"leaderboardUpdate"; // Live rankings
"quizCompleted"; // Quiz finished
"newParticipant"; // Someone joined
"participantLeft"; // Someone left
```

## Architecture Patterns and Conventions

### Backend Patterns

1. **Controller Pattern**: One controller per entity, methods follow CRUD
2. **Service Layer**: Business logic trong services (quiz realtime, analytics)
3. **Middleware Chain**: Auth → Role Check → Business Logic
4. **Error Handling**: Try-catch với JSON error responses
5. **Database Access**: Sequelize ORM với model associations

### Frontend Patterns

1. **App Router**: Next.js 15 file-based routing
2. **Component Structure**: UI components trong `/ui`, business components grouped by feature
3. **State Management**: React hooks, no global state library
4. **API Integration**: Axios với interceptors cho auth
5. **Real-time**: Socket.IO client với singleton pattern

### Database Patterns

1. **Naming Convention**: PascalCase table names, snake_case columns
2. **Primary Keys**: Auto-increment integers với `_id` suffix
3. **Foreign Keys**: Explicit references trong migrations
4. **Timestamps**: Inconsistent - some tables have, others don't
5. **Indexes**: Basic indexes, optimization needed for analytics queries

## Security Implementation

### Authentication Flow

1. **Login**: POST `/api/users/login` với email/password
2. **JWT Generation**: Server creates token với user_id và role
3. **Token Storage**: Frontend stores trong localStorage
4. **Request Headers**: `Authorization: Bearer <token>`
5. **Middleware Validation**: Every protected route validates JWT

### Authorization Levels

- **Public**: Login, register endpoints
- **Student**: Quiz participation, view own results
- **Teacher**: Create quizzes, manage students, view class results
- **Admin**: Full system access, user management

### Security Issues

1. **localStorage**: JWT stored client-side (XSS vulnerable)
2. **CORS**: Overly permissive với nhiều localhost ports
3. **Rate Limiting**: Không có rate limiting cho APIs
4. **Input Validation**: Minimal validation trong controllers
5. **File Uploads**: Không có file type/size restrictions

## Performance Characteristics

### Database Performance

- **Connection Pool**: Max 5 connections (potential bottleneck)
- **Query Patterns**: Some N+1 queries trong quiz results
- **Indexes**: Basic indexes, needs optimization cho analytics
- **Migrations**: 25+ migrations, some có performance impact

### Real-time Performance

- **Socket.IO**: No connection limits hoặc rate limiting
- **Room Management**: Complex room logic có thể scale poorly
- **Firebase**: External dependency cho real-time data
- **Redis**: Used for session storage, good performance

### Frontend Performance

- **Bundle Size**: Large due to Radix UI và Chart.js
- **Code Splitting**: Minimal route-based splitting
- **Image Optimization**: Next.js built-in optimization
- **Caching**: Browser caching, no service worker

## Deployment Architecture

### Docker Compose Services

```yaml
services:
  postgres: # PostgreSQL 15, port 5433
  redis: # Redis 7 với password auth
  backend: # Node.js API, port 8888
  frontend: # Next.js app, port 3000
  nginx: # Reverse proxy, ports 80/443
```

### Network Configuration

- **Internal Network**: `172.21.0.0/16` subnet
- **Service Discovery**: Docker service names
- **SSL Termination**: Nginx với Let's Encrypt
- **Health Checks**: PostgreSQL và Redis health checks

### Production Considerations

1. **Environment Variables**: Required cho database, Redis, JWT secret
2. **SSL Certificates**: Let's Encrypt auto-renewal needed
3. **Backup Strategy**: No automated database backups
4. **Monitoring**: No application monitoring setup
5. **Logging**: Basic console logging, no centralized logs

## Scalability Analysis

### Current Bottlenecks

1. **Database Connection Pool**: Limited to 5 connections - will bottleneck at ~50 concurrent users
2. **Single Quiz Controller**: 3400+ lines in one file - maintenance nightmare và performance impact
3. **File Storage**: Local uploads without cleanup - disk space will grow indefinitely
4. **Socket.IO Rooms**: No connection limits - memory usage grows with concurrent users
5. **No Caching Strategy**: Every request hits database, no application-level caching

### Scaling Recommendations

1. **Immediate (< 100 users)**:

   - Increase database connection pool to 20-50
   - Implement file cleanup cron job
   - Add basic rate limiting

2. **Short-term (< 1000 users)**:

   - Refactor quiz controller into microservices
   - Implement Redis caching for frequently accessed data
   - Add connection limits for Socket.IO

3. **Long-term (> 1000 users)**:
   - Consider database read replicas
   - Implement CDN for static assets
   - Move to managed file storage (S3, etc.)

## Risk Assessment

### High Risk Areas

| Risk                           | Impact | Probability | Mitigation                         |
| ------------------------------ | ------ | ----------- | ---------------------------------- |
| Quiz Controller Failure        | High   | Medium      | Refactor into smaller services     |
| Database Connection Exhaustion | High   | High        | Increase pool size, add monitoring |
| Disk Space Exhaustion          | Medium | High        | Implement file cleanup             |
| JWT Secret Exposure            | High   | Low         | Use proper secret management       |
| No Backup Strategy             | High   | Medium      | Implement automated backups        |

### Security Vulnerabilities

1. **XSS via localStorage**: JWT tokens vulnerable to XSS attacks
2. **No Input Sanitization**: Controllers lack proper input validation
3. **File Upload Vulnerabilities**: No file type/size restrictions
4. **CORS Misconfiguration**: Too permissive CORS settings
5. **No Rate Limiting**: APIs vulnerable to abuse

## Operational Readiness

### Missing Production Features

- [ ] Health check endpoints
- [ ] Application metrics/monitoring
- [ ] Centralized logging
- [ ] Error tracking (Sentry, etc.)
- [ ] Performance monitoring
- [ ] Automated backups
- [ ] Disaster recovery plan

**Note**: Load testing và performance testing are NOT implemented per project NO TESTING policy.

### Recommended Monitoring

1. **Application Metrics**:

   - Response times per endpoint
   - Database connection pool usage
   - Socket.IO connection count
   - Memory usage trends

2. **Business Metrics**:
   - Active quiz sessions
   - User engagement rates
   - Quiz completion rates
   - Error rates by feature

## Future Architecture Considerations

### Recommended Refactoring Priorities

1. **Priority 1 (Critical)**:

   - Break down quiz controller
   - Implement proper error handling
   - Add input validation

2. **Priority 2 (Important)**:

   - Implement caching strategy
   - Add monitoring and logging
   - Security hardening

3. **Priority 3 (Nice to have)**:
   - Microservices architecture
   - Event-driven patterns
   - Advanced analytics

### Technology Upgrade Path

- **Database**: Consider PostgreSQL 16 for performance improvements
- **Node.js**: Upgrade to Node.js 20 LTS for better performance
- **Frontend**: Next.js 15 is already latest, good choice
- **Monitoring**: Add Prometheus + Grafana stack
- **Logging**: Implement ELK stack or similar

## Architecture Decision Records (ADRs)

### Key Architectural Decisions Made

1. **ADR-001: Monorepo with pnpm Workspace**

   - **Decision**: Use single repository với pnpm workspace
   - **Rationale**: Simplified dependency management, shared tooling
   - **Trade-offs**: Larger repository size, potential build complexity
   - **Status**: Implemented ✅

2. **ADR-002: Socket.IO for Real-time Features**

   - **Decision**: Use Socket.IO instead of WebSockets or Server-Sent Events
   - **Rationale**: Mature library, fallback support, room management
   - **Trade-offs**: Additional complexity, memory usage for connections
   - **Status**: Implemented ✅

3. **ADR-003: JWT for Authentication**

   - **Decision**: Use JWT tokens stored in localStorage
   - **Rationale**: Stateless authentication, easy to implement
   - **Trade-offs**: XSS vulnerability, token size limitations
   - **Status**: Implemented ⚠️ (Security concerns)

4. **ADR-004: Sequelize ORM**

   - **Decision**: Use Sequelize instead of raw SQL or other ORMs
   - **Rationale**: TypeScript support, migration system, associations
   - **Trade-offs**: Performance overhead, complex queries limitations
   - **Status**: Implemented ✅

5. **ADR-005: Local File Storage**

   - **Decision**: Store uploaded files locally instead of cloud storage
   - **Rationale**: Simplicity, no external dependencies
   - **Trade-offs**: Scalability issues, no redundancy, cleanup problems
   - **Status**: Implemented ❌ (Needs revision)

6. **ADR-006: NO TESTING Policy**
   - **Decision**: Completely eliminate all forms of testing from the project
   - **Rationale**: Simplified development process, faster delivery, reduced complexity
   - **Trade-offs**: No automated quality assurance, higher risk of bugs, no regression detection
   - **Status**: Implemented ✅ (Architectural mandate)

### Decisions Needing Review

1. **Massive Quiz Controller**: Should be broken into microservices
2. **localStorage for JWT**: Should move to httpOnly cookies
3. **Local File Storage**: Should migrate to cloud storage
4. **No Rate Limiting**: Should implement API rate limiting
5. **Basic Error Handling**: Should implement structured error handling

## Conclusion

Hệ thống QL_CTDT là một ứng dụng full-stack hiện đại với kiến trúc monorepo, hỗ trợ tính năng real-time quiz mạnh mẽ. Tuy nhiên, cần giải quyết một số technical debt quan trọng để đảm bảo scalability và maintainability trong tương lai.

### Immediate Action Items

1. **Refactor Quiz Controller** - Chia nhỏ file 3400+ lines
2. **Implement File Cleanup** - Tránh disk space exhaustion
3. **Add Rate Limiting** - Bảo vệ APIs khỏi abuse
4. **Increase DB Pool** - Tránh connection bottleneck
5. **Add Monitoring** - Theo dõi performance và errors

### Long-term Roadmap

1. **Security Hardening** - Fix JWT storage, add input validation
2. **Performance Optimization** - Implement caching, optimize queries
3. **Operational Excellence** - Add monitoring, logging, backups
4. **Scalability Improvements** - Microservices, load balancing
5. **Developer Experience** - Better CI/CD, documentation (NO TESTING per project policy)

---

_Tài liệu này phản ánh trạng thái thực tế của hệ thống tại thời điểm 2025-01-23 và sẽ được cập nhật khi có thay đổi kiến trúc quan trọng._
