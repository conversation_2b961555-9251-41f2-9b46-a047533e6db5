# Quiz Racing Reward Calculation Hub

## 📋 System Overview

Đây là **trung tâm tính toán phần thưởng duy nhất** cho toàn bộ Synlearnia platform. Quiz Racing là **nguồn duy nhất** để kiếm XP, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> và Eggs trong hệ thống. Tất cả logic tính toán phần thưởng được tập trung tại đây thay vì rải rác ở các file khác.

### Reward Sources Summary

- **XP (Experience Points):** 100% từ quiz performance - không có nguồn XP nào khác
- **SynCoin (Primary Currency):** Primary source từ quiz, với tier-based multipliers
- **Kristal (Premium Currency):** Exceptional performance và long-term achievements
- **Eggs (Collection Items):** Performance-based drops với rare probability scaling

> **Lưu ý:** Game mechanics và luồng hoạt động đã được document chi tiết trong flowchart riêng biệt. File này chỉ tập trung vào **logic tính toán phần thưởng** sau khi quiz kết thúc.

## 🏆 HỆ THỐNG TÍNH TOÁN PHẦN THƯỞNG CHI TIẾT

> **Input Data từ Flowchart:** File này nhận các input data sau từ quiz gameplay:
>
> - **Total Score:** Tổng điểm đã được tính theo flowchart:
>   - **Base Points:** Vòng 1 (Dễ: +60, Trung bình: +100, Khó: +140) | Vòng 2+: 50% điểm vòng 1
>   - **Speed Bonus:** Tối đa +50% điểm cơ bản (chỉ vòng 1, trong 5s đầu)
>   - **Streak Bonus:** +10, +15, +20... tăng dần (chỉ vòng 1)
>   - **Global Events:** Golden Question (×2), Speed Zone (speed bonus ×2)
> - **Correct Answers:** Số câu trả lời đúng tổng cộng
> - **Final Ranking:** Thứ hạng cuối cùng trong quiz
> - **Max Streak:** Chuỗi thắng dài nhất đạt được
> - **Performance Metrics:** Speed average, skill usage, special achievements
> - **Player Level/Tier:** Current tier for multiplier calculation

### 💰 SynCoin Calculation (Post-Game Currency Rewards)

#### Base SynCoin Formula

**Công thức tính SynCoin cơ bản:**

```
Base SynCoin = 25 (participation) + (Correct_Answers × 5) + (Total_Score ÷ 100) + Ranking_Bonus
```

#### Ranking Bonus Structure

**Thưởng theo thứ hạng cuối game:**

- **1st Place:** +50 SynCoins
- **2nd Place:** +35 SynCoins
- **3rd Place:** +20 SynCoins
- **4th-8th Place:** +10 SynCoins

#### Special Performance Bonuses

**Thưởng đặc biệt dựa trên achievement:**

- **Perfect Quiz (100% accuracy):** +50 SynCoins
- **Speed Demon (Average <10s response):** +25 SynCoins
- **Streak Master (Max streak ≥10):** +35 SynCoins
- **Skill Strategist (Used all 4 skill types):** +20 SynCoins
- **Comeback King (Win from bottom 3):** +40 SynCoins
- **Consistency King (80%+ accuracy in 5+ recent quizzes):** +15 SynCoins

#### Tier Multiplier Effects

**Level-based earning enhancement (áp dụng sau khi tính total):**

- **Wood (L1-12):** ×1.0 (baseline)
- **Bronze (L13-24):** ×1.1 (+10% all SynCoin)
- **Silver (L25-36):** ×1.2 (+20% all SynCoin)
- **Gold (L37-48):** ×1.3 (+30% all SynCoin)
- **Platinum (L49-60):** ×1.4 (+40% all SynCoin)
- **Onyx (L61-72):** ×1.5 (+50% all SynCoin)
- **Sapphire (L73-84):** ×1.6 (+60% all SynCoin)
- **Ruby (L85-96):** ×1.7 (+70% all SynCoin)
- **Amethyst (L97-108):** ×1.8 (+80% all SynCoin)
- **Master (L109-120+):** ×2.0 (+100% all SynCoin)

#### Example SynCoin Calculation

**Scenario: Silver tier player (L25), 12 correct answers, 2,450 total score, 2nd place, perfect quiz**

- Base: 25 + (12×5) + (2450÷100) + 35 = 25 + 60 + 24.5 + 35 = 144.5
- Perfect Quiz Bonus: +50
- Subtotal: 144.5 + 50 = 194.5
- Tier Multiplier: 194.5 × 1.2 = 233.4
- **Final: 233 SynCoins**

### ⭐ XP Calculation (Character Progression)

#### Base XP Formula

**Công thức XP cơ bản:**

```
Base XP = 50 (participation) + (Correct_Answers × 10) + (Total_Score ÷ 50) + Ranking_XP_Bonus
```

#### Ranking XP Bonus Structure

**XP thưởng theo thứ hạng:**

- **1st Place:** +100 XP
- **2nd Place:** +75 XP
- **3rd Place:** +50 XP
- **4th-6th Place:** +25 XP
- **7th-8th Place:** +10 XP

#### Special Performance Bonuses

**Thưởng XP đặc biệt dựa trên achievement:**

- **Perfect Quiz (15/15 correct):** +150 XP
- **Speed Master (Avg <8s):** +100 XP
- **Streak Master (Max streak ≥10):** +75 XP
- **Skill Mastery (Strategic skill usage):** +50 XP
- **Comeback King (Win from bottom 3):** +60 XP
- **Consistency King (80%+ accuracy in 5+ recent quizzes):** +30 XP

#### Tier Multiplier Effects

**Level-based XP enhancement (áp dụng sau khi tính total):**

- **Wood (L1-12):** ×1.0 (baseline)
- **Bronze (L13-24):** ×1.1 (+10% all XP)
- **Silver (L25-36):** ×1.2 (+20% all XP)
- **Gold (L37-48):** ×1.3 (+30% all XP)
- **Platinum (L49-60):** ×1.4 (+40% all XP)
- **Onyx (L61-72):** ×1.5 (+50% all XP)
- **Sapphire (L73-84):** ×1.6 (+60% all XP)
- **Ruby (L85-96):** ×1.7 (+70% all XP)
- **Amethyst (L97-108):** ×1.8 (+80% all XP)
- **Master (L109-120+):** ×2.0 (+100% all XP)

#### Example XP Calculation

**Scenario: Gold tier player (L37), 14 correct answers, 3,200 total score, 1st place, max streak 12**

- Base: 50 + (14×10) + (3200÷50) + 100 = 50 + 140 + 64 + 100 = 354
- Streak Master Bonus: +75
- Subtotal: 354 + 75 = 429
- Tier Multiplier: 429 × 1.3 = 557.7
- **Final: 558 XP**

### 💎 Kristal Rewards (Premium Currency)

#### Base Kristal Formula

**Công thức Kristal cơ bản:**

```
Base Kristal = 0 (participation) + 0 (correct answers) + 0 (score) + Ranking_Kristal_Bonus
```

#### Ranking Kristal Bonus Structure

**Kristal thưởng theo thứ hạng:**

- **1st Place:** +5 Kristal
- **2nd Place:** +3 Kristal
- **3rd Place:** +2 Kristal
- **4th-5th Place:** +1 Kristal
- **6th-8th Place:** +0 Kristal

#### Special Performance Bonuses

**Thưởng Kristal đặc biệt dựa trên achievement:**

- **Perfect Quiz (15/15):** +8 Kristal
- **Speed Master (Avg <8s):** +5 Kristal
- **Streak Master (Max streak ≥10):** +4 Kristal
- **Skill Mastery (Strategic skill usage):** +3 Kristal
- **Triple Crown (Perfect + 1st + Speed):** +15 Kristal (replaces individual bonuses)
- **Consistency King (80%+ accuracy in 5+ recent quizzes):** +2 Kristal

#### Tier Multiplier Effects

**Level-based Kristal enhancement (áp dụng sau khi tính total):**

- **Wood (L1-12):** ×1.0 (baseline)
- **Bronze (L13-24):** ×1.1 (+10% all Kristal)
- **Silver (L25-36):** ×1.2 (+20% all Kristal)
- **Gold (L37-48):** ×1.3 (+30% all Kristal)
- **Platinum (L49-60):** ×1.4 (+40% all Kristal)
- **Onyx (L61-72):** ×1.5 (+50% all Kristal)
- **Sapphire (L73-84):** ×1.6 (+60% all Kristal)
- **Ruby (L85-96):** ×1.7 (+70% all Kristal)
- **Amethyst (L97-108):** ×1.8 (+80% all Kristal)
- **Master (L109-120+):** ×2.0 (+100% all Kristal)

#### Example Kristal Calculation

**Scenario: Ruby tier player (L85), perfect quiz, 1st place, speed master**

- Base: 0 + 0 + 0 + 5 = 5
- Triple Crown Bonus: +15 (replaces individual perfect + speed bonuses)
- Subtotal: 5 + 15 = 20
- Tier Multiplier: 20 × 1.7 = 34
- **Final: 34 Kristal**

### 🥚 Egg Drop System

#### Base Egg Drop Formula

**Công thức Egg Drop cơ bản:**

```
Base Egg Chance = 5% (participation) + 0% (correct answers) + 0% (score) + Ranking_Egg_Bonus
```

#### Ranking Egg Bonus Structure

**Egg chance thưởng theo thứ hạng:**

- **1st Place:** +25% chance for Basic Egg, +15% chance for Royal Egg, +5% chance for Legendary Egg
- **2nd Place:** +20% chance for Basic Egg, +10% chance for Royal Egg, +3% chance for Legendary Egg
- **3rd Place:** +15% chance for Basic Egg, +8% chance for Royal Egg, +2% chance for Legendary Egg
- **4th-5th Place:** +10% chance for Basic Egg, +5% chance for Royal Egg
- **6th-8th Place:** +5% chance for Basic Egg

#### Special Performance Bonuses

**Thưởng Egg đặc biệt dựa trên achievement:**

- **Perfect Quiz (15/15):** +35% chance for Royal Egg, +10% chance for Legendary Egg, +3% chance for Dragon Egg
- **Speed Master (Avg <8s):** +25% chance for Royal Egg, +5% chance for Legendary Egg
- **Streak Master (Max streak ≥10):** +20% chance for Royal Egg, +3% chance for Legendary Egg
- **Skill Mastery (Strategic skill usage):** +15% chance for Royal Egg
- **Triple Crown (Perfect + 1st + Speed):** Guaranteed Mythical Egg (replaces all other chances)
- **Consistency King (80%+ accuracy in 5+ recent quizzes):** +10% chance for Royal Egg

#### Tier Multiplier Effects

**Level-based egg chance enhancement (áp dụng sau khi tính total):**

- **Wood (L1-12):** ×1.0 (baseline)
- **Bronze (L13-24):** ×1.1 (+10% all egg chances)
- **Silver (L25-36):** ×1.2 (+20% all egg chances)
- **Gold (L37-48):** ×1.3 (+30% all egg chances)
- **Platinum (L49-60):** ×1.4 (+40% all egg chances)
- **Onyx (L61-72):** ×1.5 (+50% all egg chances)
- **Sapphire (L73-84):** ×1.6 (+60% all egg chances)
- **Ruby (L85-96):** ×1.7 (+70% all egg chances)
- **Amethyst (L97-108):** ×1.8 (+80% all egg chances)
- **Master (L109-120+):** ×2.0 (+100% all egg chances)

#### Example Egg Drop Calculation

**Scenario: Silver tier player (L25), perfect quiz, 2nd place**

- Base: 5% + 0% + 0% + 20% (Basic) + 10% (Royal) + 3% (Legendary) = 25% Basic, 10% Royal, 3% Legendary
- Perfect Quiz Bonus: +35% Royal, +10% Legendary, +3% Dragon
- Subtotal: 25% Basic, 45% Royal, 13% Legendary, 3% Dragon
- Tier Multiplier: 30% Basic, 54% Royal, 15.6% Legendary, 3.6% Dragon
- **Final Chances: 30% Basic, 54% Royal, 16% Legendary, 4% Dragon**

## 📊 Input Data Requirements

### Required Data từ Flowchart

**Để tính toán rewards chính xác, system cần receive:**

#### Core Performance Data

- **Total Score:** Tổng điểm từ Base Points + Speed Bonus + Streak Bonus + Global Events
- **Correct Answers:** Số câu trả lời đúng tổng cộng
- **Final Ranking:** Thứ hạng cuối cùng (1st-8th place)
- **Max Streak:** Chuỗi thắng dài nhất đạt được trong quiz

#### Detailed Performance Metrics

- **Perfect Quiz:** 15/15 câu đúng (boolean)
- **Speed Master:** Average response time <8 seconds (boolean)
- **Streak Master:** Max streak ≥10 (boolean)
- **Skill Mastery:** Strategic skill usage patterns (boolean)
- **Triple Crown:** Perfect + 1st + Speed Master combined (boolean)
- **Comeback King:** Win từ vị trí bottom 3 (boolean)

#### Player Context Data

- **Player Level:** Current level for tier determination
- **Player Tier:** Wood/Bronze/Silver/Gold/Diamond/Master
- **Tier Multiplier:** Corresponding multiplier value (1.0x-1.5x)

### Reward Calculation Process

**Step-by-step execution:**

1. **Validate Input Data:** Ensure all required metrics are present
2. **Calculate Base Rewards:** Apply base formulas for SynCoin, XP, Kristal, Eggs
3. **Add Ranking Bonuses:** Apply placement-based bonuses
4. **Add Performance Bonuses:** Check achievements và add special bonuses
5. **Apply Tier Multipliers:** Scale final totals by player tier
6. **Return Final Rewards:** Package results for client display

## 🎯 Implementation Notes

### Critical Dependencies

- **Flowchart Scoring System:** Must receive accurate Total Score calculation
- **Performance Detection:** Accurate tracking của speed, streak, và skill usage
- **Tier System Integration:** Current player tier must be available
- **Achievement Validation:** Proper verification của performance bonuses

### Error Handling

- **Missing Data:** Default to minimum rewards if metrics unavailable
- **Invalid Rankings:** Validate placement data before bonus calculation
- **Tier Fallback:** Default to Wood tier if player tier unknown
- **Overflow Protection:** Cap maximum rewards to prevent exploitation
