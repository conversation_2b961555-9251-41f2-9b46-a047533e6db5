# Integration Architecture

## External Integrations

**Firebase Integration**:

- **Purpose**: Real-time database backup
- **Implementation**: Admin SDK
- **Location**: `backend/src/config/firebase.js`
- **Usage**: Participant data synchronization

**Email Service** (Future):

- **Purpose**: Notifications, reports
- **Status**: Not implemented
- **Recommendation**: SendGrid or AWS SES

## Internal Service Communication

**Frontend ↔ Backend**:

- **Protocol**: HTTP/HTTPS REST API
- **Authentication**: JWT Bearer tokens
- **Real-time**: Socket.IO WebSocket connection
- **Error Handling**: Axios interceptors

**Backend ↔ Database**:

- **ORM**: Sequelize with PostgreSQL
- **Connection Pool**: Max 5 connections (needs scaling)
- **Migrations**: Sequelize CLI managed
- **Backup**: Manual PostgreSQL dumps

**Backend ↔ Cache**:

- **Client**: Redis client
- **Usage**: Sessions, quiz state, leaderboards
- **Persistence**: RDB + AOF
- **Clustering**: Single instance (needs scaling)
