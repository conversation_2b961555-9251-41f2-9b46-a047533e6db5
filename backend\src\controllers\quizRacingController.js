// backend/src/controllers/quizRacingController.js
// Real-time Quiz Racing Controller with WebSocket Events

const QuizRacingService = require('../services/quizRacingService');
const SkillService = require('../services/skillService');
const { Quiz, User, QuizQuestion, Question } = require('../models');

class QuizRacingController {
    constructor(io) {
        this.io = io;
        this.quizRacingService = new QuizRacingService(io);
        this.setupSocketEvents();
    }

    // =====================================================
    // WEBSOCKET EVENT HANDLERS
    // =====================================================

    setupSocketEvents() {
        this.io.on('connection', (socket) => {
            console.log(`Quiz Racing client connected: ${socket.id}`);

            // Join quiz racing room
            socket.on('join-quiz-racing', async (data) => {
                await this.handleJoinQuizRacing(socket, data);
            });

            // Submit answer with racing mechanics
            socket.on('submit-racing-answer', async (data) => {
                await this.handleSubmitRacingAnswer(socket, data);
            });

            // Use skill during racing
            socket.on('use-skill', async (data) => {
                await this.handleUseSkill(socket, data);
            });

            // Skip question
            socket.on('skip-question', async (data) => {
                await this.handleSkipQuestion(socket, data);
            });

            // Get random skill when energy = 100%
            socket.on('get-random-skill', async (data) => {
                await this.handleGetRandomSkill(socket, data);
            });

            // Player ready for next round
            socket.on('player-ready', async (data) => {
                await this.handlePlayerReady(socket, data);
            });

            // Heartbeat for connection maintenance
            socket.on('heartbeat', () => {
                socket.emit('heartbeat-ack', { timestamp: Date.now() });
            });

            // Handle disconnection
            socket.on('disconnect', () => {
                console.log(`Quiz Racing client disconnected: ${socket.id}`);
            });
        });
    }

    // =====================================================
    // EVENT HANDLER IMPLEMENTATIONS
    // =====================================================

    /**
     * Handle joining quiz racing session
     */
    async handleJoinQuizRacing(socket, data) {
        try {
            const { quiz_session_id, user_id, username } = data;

            // Validate required data
            if (!quiz_session_id || !user_id || !username) {
                socket.emit('error', { message: 'Missing required data for joining quiz racing' });
                return;
            }

            // Join socket room
            socket.join(`quiz:${quiz_session_id}`);
            socket.join(`quiz:${quiz_session_id}:${user_id}`);

            // Load user's skill loadout
            const loadout = await this.quizRacingService.loadParticipantSkills(quiz_session_id, user_id);

            // Get current session state
            const sessionData = await this.quizRacingService.getSessionData(quiz_session_id);

            // Send join confirmation
            socket.emit('quiz-racing-joined', {
                session_id: quiz_session_id,
                user_id: user_id,
                loadout: loadout,
                session_state: sessionData,
                timestamp: Date.now()
            });

            // Notify other participants
            socket.to(`quiz:${quiz_session_id}`).emit('participant-joined', {
                user_id: user_id,
                username: username,
                timestamp: Date.now()
            });

            console.log(`User ${user_id} joined quiz racing session ${quiz_session_id}`);
        } catch (error) {
            console.error('Error handling join quiz racing:', error);
            socket.emit('error', { message: 'Failed to join quiz racing session' });
        }
    }

    /**
     * Handle submitting answer with racing mechanics
     */
    async handleSubmitRacingAnswer(socket, data) {
        try {
            const { quiz_session_id, user_id, question_id, answer_id, response_time } = data;

            // Validate required data
            if (!quiz_session_id || !user_id || !question_id || !answer_id) {
                socket.emit('error', { message: 'Missing required data for answer submission' });
                return;
            }

            // Get question details for scoring
            const question = await Question.findByPk(question_id);
            if (!question) {
                socket.emit('error', { message: 'Question not found' });
                return;
            }

            // Check if answer is correct
            const isCorrect = answer_id === question.correct_answer;

            // Calculate dynamic scoring with racing mechanics
            const scoringResult = await this.calculateRacingScore(
                quiz_session_id,
                user_id,
                question,
                isCorrect,
                response_time
            );

            // Update energy based on performance
            const energyResult = await this.quizRacingService.updatePlayerEnergy(
                quiz_session_id,
                user_id,
                isCorrect,
                scoringResult.has_speed_bonus,
                scoringResult.has_streak_bonus
            );

            // Update session data with new scores
            await this.updateSessionScores(quiz_session_id, user_id, scoringResult);

            // Send answer result to user
            socket.emit('racing-answer-result', {
                question_id: question_id,
                is_correct: isCorrect,
                points_earned: scoringResult.points_earned,
                total_score: scoringResult.total_score,
                current_streak: scoringResult.current_streak,
                energy_percent: energyResult.energy_percent,
                speed_bonus: scoringResult.speed_bonus,
                streak_bonus: scoringResult.streak_bonus,
                skill_available: energyResult.skill_available,
                timestamp: Date.now()
            });

            // Update leaderboard for all participants
            await this.broadcastLeaderboardUpdate(quiz_session_id);

            console.log(`User ${user_id} submitted racing answer for question ${question_id}`);
        } catch (error) {
            console.error('Error handling racing answer submission:', error);
            socket.emit('error', { message: 'Failed to submit racing answer' });
        }
    }

    /**
     * Handle skill usage during racing
     */
    async handleUseSkill(socket, data) {
        try {
            const { quiz_session_id, user_id, skill_id, target_user_id } = data;

            // Validate required data
            if (!quiz_session_id || !user_id || !skill_id) {
                socket.emit('error', { message: 'Missing required data for skill usage' });
                return;
            }

            // Execute skill in racing context
            const result = await this.quizRacingService.executeSkillInRacing(
                quiz_session_id,
                user_id,
                skill_id,
                target_user_id
            );

            if (result.success) {
                // Update leaderboard after skill effects
                await this.broadcastLeaderboardUpdate(quiz_session_id);
                
                console.log(`User ${user_id} used skill ${skill_id} in quiz ${quiz_session_id}`);
            } else {
                socket.emit('skill-usage-failed', {
                    skill_id: skill_id,
                    error: result.error,
                    timestamp: Date.now()
                });
            }
        } catch (error) {
            console.error('Error handling skill usage:', error);
            socket.emit('error', { message: 'Failed to use skill' });
        }
    }

    /**
     * Handle skip question
     */
    async handleSkipQuestion(socket, data) {
        try {
            const { quiz_session_id, user_id, question_id } = data;

            // Record skip (0 points, no energy gain)
            await this.updateSessionScores(quiz_session_id, user_id, {
                points_earned: 0,
                total_score: 0, // Keep current score
                current_streak: 0, // Reset streak
                has_speed_bonus: false,
                has_streak_bonus: false
            });

            // Send skip confirmation
            socket.emit('question-skipped', {
                question_id: question_id,
                points_earned: 0,
                timestamp: Date.now()
            });

            // Update leaderboard
            await this.broadcastLeaderboardUpdate(quiz_session_id);

            console.log(`User ${user_id} skipped question ${question_id}`);
        } catch (error) {
            console.error('Error handling skip question:', error);
            socket.emit('error', { message: 'Failed to skip question' });
        }
    }

    /**
     * Handle get random skill when energy = 100%
     */
    async handleGetRandomSkill(socket, data) {
        try {
            const { quiz_session_id, user_id } = data;

            const skill = await this.quizRacingService.selectRandomSkill(quiz_session_id, user_id);

            if (skill) {
                socket.emit('random-skill-selected', {
                    skill: skill,
                    timestamp: Date.now()
                });
            } else {
                socket.emit('no-skills-available', {
                    message: 'No skills available or all skills used',
                    timestamp: Date.now()
                });
            }
        } catch (error) {
            console.error('Error getting random skill:', error);
            socket.emit('error', { message: 'Failed to get random skill' });
        }
    }

    /**
     * Handle player ready for next round
     */
    async handlePlayerReady(socket, data) {
        try {
            const { quiz_session_id, user_id } = data;

            // Mark player as ready
            socket.to(`quiz:${quiz_session_id}`).emit('player-ready', {
                user_id: user_id,
                timestamp: Date.now()
            });

            console.log(`User ${user_id} is ready for next round in quiz ${quiz_session_id}`);
        } catch (error) {
            console.error('Error handling player ready:', error);
            socket.emit('error', { message: 'Failed to mark player as ready' });
        }
    }

    // =====================================================
    // HELPER METHODS
    // =====================================================

    /**
     * Calculate racing score with bonuses
     */
    async calculateRacingScore(quizSessionId, userId, question, isCorrect, responseTime) {
        // Implementation will be added based on the scoring rules from the guide
        // This is a placeholder for the complex scoring logic
        return {
            points_earned: isCorrect ? 100 : 0,
            total_score: 100,
            current_streak: isCorrect ? 1 : 0,
            speed_bonus: 0,
            streak_bonus: 0,
            has_speed_bonus: false,
            has_streak_bonus: false
        };
    }

    /**
     * Update session scores
     */
    async updateSessionScores(quizSessionId, userId, scoringResult) {
        const sessionData = await this.quizRacingService.getSessionData(quizSessionId);
        if (!sessionData) return;

        const participant = sessionData.participants.find(p => p.user_id === userId);
        if (participant) {
            if (scoringResult.total_score > 0) {
                participant.current_score = scoringResult.total_score;
            }
            participant.current_streak = scoringResult.current_streak;
        }

        await this.quizRacingService.updateSessionData(quizSessionId, sessionData);
    }

    /**
     * Broadcast leaderboard update to all participants
     */
    async broadcastLeaderboardUpdate(quizSessionId) {
        const sessionData = await this.quizRacingService.getSessionData(quizSessionId);
        if (!sessionData) return;

        // Sort participants by score
        const leaderboard = sessionData.participants
            .sort((a, b) => b.current_score - a.current_score)
            .map((p, index) => ({
                position: index + 1,
                user_id: p.user_id,
                username: p.username,
                current_score: p.current_score,
                current_streak: p.current_streak,
                energy_percent: p.energy_percent
            }));

        // Broadcast to all participants
        this.io.to(`quiz:${quizSessionId}`).emit('leaderboard-update', {
            session_id: quizSessionId,
            leaderboard: leaderboard,
            timestamp: Date.now()
        });
    }
}

module.exports = QuizRacingController;
