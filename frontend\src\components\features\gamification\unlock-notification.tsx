"use client";

import React, { useState, useEffect } from "react";
import { cn } from "@/lib/utils";
import { Badge } from "@/components/ui/feedback";
import { Button } from "@/components/ui/forms";
import { TierIcon } from "@/lib/hooks/use-tier-icon";
import { UserBadgeData, UserTitleData, BadgeRarity } from "@/lib/types/gamification";
import { getVietnameseTierName } from "@/lib/utils/tier-assets";
import { Star, Sparkles, Crown, Gem, Trophy, Award, X, Eye } from "lucide-react";

interface UnlockNotificationProps {
  isVisible: boolean;
  onClose: () => void;
  onViewDetails?: () => void;
  item: UserBadgeData | UserTitleData;
  type: "badge" | "title";
  className?: string;
  position?: "top-right" | "top-left" | "bottom-right" | "bottom-left";
  autoClose?: boolean;
  autoCloseDelay?: number;
}

export const UnlockNotification: React.FC<UnlockNotificationProps> = ({
  isVisible,
  onClose,
  onViewDetails,
  item,
  type,
  className,
  position = "top-right",
  autoClose = true,
  autoCloseDelay = 5000,
}) => {
  const [isAnimating, setIsAnimating] = useState(false);

  useEffect(() => {
    if (isVisible) {
      setIsAnimating(true);
      
      if (autoClose) {
        const timer = setTimeout(() => {
          handleClose();
        }, autoCloseDelay);
        
        return () => clearTimeout(timer);
      }
    }
  }, [isVisible, autoClose, autoCloseDelay]);

  const handleClose = () => {
    setIsAnimating(false);
    setTimeout(() => {
      onClose();
    }, 300);
  };

  if (!isVisible) return null;

  // Get rarity configuration for badges
  const getRarityConfig = (rarity: BadgeRarity) => {
    const rarityConfigs = {
      common: {
        color: "text-gray-600",
        icon: Star,
        label: "Thường",
        gradient: "from-gray-400 to-gray-600",
        bgGradient: "from-gray-50 to-gray-100",
      },
      rare: {
        color: "text-blue-600",
        icon: Sparkles,
        label: "Hiếm",
        gradient: "from-blue-400 to-blue-600",
        bgGradient: "from-blue-50 to-blue-100",
      },
      epic: {
        color: "text-purple-600",
        icon: Crown,
        label: "Sử Thi",
        gradient: "from-purple-400 to-purple-600",
        bgGradient: "from-purple-50 to-purple-100",
      },
      legendary: {
        color: "text-yellow-600",
        icon: Gem,
        label: "Huyền Thoại",
        gradient: "from-yellow-400 to-orange-500",
        bgGradient: "from-yellow-50 to-orange-100",
      },
    };
    return rarityConfigs[rarity] || rarityConfigs.common;
  };

  const isBadge = type === "badge";
  const badgeData = isBadge ? (item as UserBadgeData) : null;
  const titleData = !isBadge ? (item as UserTitleData) : null;

  const rarityConfig = isBadge && badgeData 
    ? getRarityConfig(badgeData.Badge.rarity)
    : { 
        color: "text-blue-600", 
        bgGradient: "from-blue-50 to-blue-100",
        gradient: "from-blue-400 to-blue-600",
        label: "Danh hiệu"
      };

  const positionClasses = {
    "top-right": "top-4 right-4",
    "top-left": "top-4 left-4",
    "bottom-right": "bottom-4 right-4",
    "bottom-left": "bottom-4 left-4",
  };

  return (
    <div
      className={cn(
        "fixed z-50 max-w-sm w-full mx-4",
        positionClasses[position],
        className
      )}
    >
      <div
        className={cn(
          "bg-white rounded-lg shadow-lg border border-gray-200 overflow-hidden",
          "transform transition-all duration-300 ease-out",
          isAnimating 
            ? "translate-x-0 opacity-100 scale-100" 
            : position.includes("right")
              ? "translate-x-full opacity-0 scale-95"
              : "-translate-x-full opacity-0 scale-95"
        )}
      >
        {/* Header with gradient background */}
        <div
          className={cn(
            "relative p-4 bg-gradient-to-r",
            rarityConfig.bgGradient
          )}
        >
          {/* Close button */}
          <Button
            variant="ghost"
            size="icon"
            onClick={handleClose}
            className="absolute top-2 right-2 h-6 w-6 text-gray-500 hover:text-gray-700"
          >
            <X className="h-4 w-4" />
          </Button>

          {/* Content */}
          <div className="flex items-start gap-3 pr-8">
            {/* Icon */}
            <div className="flex-shrink-0">
              {isBadge ? (
                <div className="w-12 h-12 bg-white/80 rounded-full flex items-center justify-center shadow-sm">
                  <Award className="h-6 w-6 text-purple-600" />
                </div>
              ) : (
                <div className="w-12 h-12 bg-white/80 rounded-full flex items-center justify-center shadow-sm">
                  <Trophy className="h-6 w-6 text-blue-600" />
                </div>
              )}
            </div>

            {/* Text content */}
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-1">
                <h4 className="font-semibold text-gray-900 text-sm">
                  🎉 Chúc mừng!
                </h4>
                <Badge
                  variant="outline"
                  className={cn("text-xs bg-white/80", rarityConfig.color)}
                >
                  {isBadge ? "Huy hiệu mới" : "Danh hiệu mới"}
                </Badge>
              </div>
              
              <p className="text-sm text-gray-700 mb-2">
                Bạn đã mở khóa {isBadge ? "huy hiệu" : "danh hiệu"}:
              </p>
              
              <p className="font-medium text-gray-900 text-sm truncate">
                {isBadge && badgeData 
                  ? badgeData.Badge.badge_name 
                  : titleData?.Title.title_display}
              </p>
              
              <div className="flex items-center gap-2 mt-2">
                <Badge variant="outline" className="text-xs">
                  {isBadge && badgeData 
                    ? getVietnameseTierName(badgeData.Badge.tier_name)
                    : titleData ? getVietnameseTierName(titleData.Title.tier_name) : ""}
                </Badge>
                
                {isBadge && badgeData && (
                  <Badge 
                    variant="outline" 
                    className={cn("text-xs", rarityConfig.color)}
                  >
                    {rarityConfig.label}
                  </Badge>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Footer with action buttons */}
        <div className="p-3 bg-gray-50 border-t border-gray-100">
          <div className="flex items-center justify-between gap-2">
            <p className="text-xs text-gray-500">
              Vừa mở khóa • {new Date().toLocaleTimeString('vi-VN', { 
                hour: '2-digit', 
                minute: '2-digit' 
              })}
            </p>
            
            {onViewDetails && (
              <Button
                variant="outline"
                size="sm"
                onClick={onViewDetails}
                className="text-xs h-7 px-2"
              >
                <Eye className="h-3 w-3 mr-1" />
                Xem chi tiết
              </Button>
            )}
          </div>
        </div>

        {/* Celebration sparkles */}
        <div className="absolute top-1 right-8 text-yellow-400 animate-pulse">
          ✨
        </div>
        <div className="absolute bottom-1 left-8 text-yellow-400 animate-pulse delay-300">
          ⭐
        </div>
      </div>
    </div>
  );
};

// Toast-style notification for simpler use cases
interface UnlockToastProps {
  isVisible: boolean;
  onClose: () => void;
  title: string;
  description: string;
  type: "badge" | "title";
  className?: string;
}

export const UnlockToast: React.FC<UnlockToastProps> = ({
  isVisible,
  onClose,
  title,
  description,
  type,
  className,
}) => {
  const [isAnimating, setIsAnimating] = useState(false);

  useEffect(() => {
    if (isVisible) {
      setIsAnimating(true);
      
      const timer = setTimeout(() => {
        handleClose();
      }, 3000);
      
      return () => clearTimeout(timer);
    }
  }, [isVisible]);

  const handleClose = () => {
    setIsAnimating(false);
    setTimeout(() => {
      onClose();
    }, 300);
  };

  if (!isVisible) return null;

  return (
    <div
      className={cn(
        "fixed top-4 right-4 z-50 max-w-sm w-full mx-4",
        className
      )}
    >
      <div
        className={cn(
          "bg-white rounded-lg shadow-lg border border-gray-200 p-4",
          "transform transition-all duration-300 ease-out",
          isAnimating 
            ? "translate-x-0 opacity-100 scale-100" 
            : "translate-x-full opacity-0 scale-95"
        )}
      >
        <div className="flex items-start gap-3">
          <div className="flex-shrink-0">
            {type === "badge" ? (
              <Award className="h-5 w-5 text-purple-600" />
            ) : (
              <Trophy className="h-5 w-5 text-blue-600" />
            )}
          </div>
          
          <div className="flex-1 min-w-0">
            <h4 className="font-semibold text-gray-900 text-sm mb-1">
              {title}
            </h4>
            <p className="text-sm text-gray-600">
              {description}
            </p>
          </div>
          
          <Button
            variant="ghost"
            size="icon"
            onClick={handleClose}
            className="h-6 w-6 text-gray-400 hover:text-gray-600 flex-shrink-0"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
};

// Hook for managing unlock notifications
export const useUnlockNotifications = () => {
  const [notifications, setNotifications] = useState<Array<{
    id: string;
    item: UserBadgeData | UserTitleData;
    type: "badge" | "title";
    timestamp: number;
  }>>([]);

  const addNotification = (item: UserBadgeData | UserTitleData, type: "badge" | "title") => {
    const id = `${type}-${Date.now()}-${Math.random()}`;
    const notification = {
      id,
      item,
      type,
      timestamp: Date.now(),
    };
    
    setNotifications(prev => [...prev, notification]);
  };

  const removeNotification = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  };

  const clearAllNotifications = () => {
    setNotifications([]);
  };

  return {
    notifications,
    addNotification,
    removeNotification,
    clearAllNotifications,
  };
};

export default UnlockNotification;
