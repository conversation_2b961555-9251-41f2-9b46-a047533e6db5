{"info": {"_postman_id": "complete-gamification-system", "name": "🎮 Complete Gamification System API", "description": "Complete API collection for ALL 4 phases: XP/Level, Currency/Avatar, Social/Leaderboard, Skills/Racing (50+ endpoints)", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:8888/api", "type": "string"}, {"key": "authToken", "value": "", "type": "string"}, {"key": "sessionId", "value": "", "type": "string"}, {"key": "userId", "value": "1", "type": "string"}], "item": [{"name": "🔐 Authentication", "item": [{"name": "Login Student1", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.json().success) {", "    pm.globals.set('authToken', pm.response.json().data.token);", "    pm.globals.set('userId', pm.response.json().data.user.user_id);", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"student1\",\n  \"password\": \"password123\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/login", "host": ["{{baseUrl}}"], "path": ["auth", "login"]}}}, {"name": "Login Student2", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"student2\",\n  \"password\": \"password123\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/login", "host": ["{{baseUrl}}"], "path": ["auth", "login"]}}}]}, {"name": "🎯 Phase 1: Core Gamification", "item": [{"name": "Get User Level & XP", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/gamification/level", "host": ["{{baseUrl}}"], "path": ["gamification", "level"]}}}, {"name": "Add XP to User", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_id\": {{userId}},\n  \"xp_amount\": 100,\n  \"reason\": \"Quiz completion\"\n}"}, "url": {"raw": "{{baseUrl}}/gamification/add-xp", "host": ["{{baseUrl}}"], "path": ["gamification", "add-xp"]}}}, {"name": "Get All Titles", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/titles", "host": ["{{baseUrl}}"], "path": ["titles"]}}}, {"name": "Get User Titles", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/titles/user/{{userId}}", "host": ["{{baseUrl}}"], "path": ["titles", "user", "{{userId}}"]}}}, {"name": "Get All Achievements", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/achievements", "host": ["{{baseUrl}}"], "path": ["achievements"]}}}, {"name": "Calculate Dynamic Score", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"base_score\": 100,\n  \"response_time\": 3000,\n  \"current_streak\": 5,\n  \"difficulty_level\": \"MEDIUM\"\n}"}, "url": {"raw": "{{baseUrl}}/scoring/calculate", "host": ["{{baseUrl}}"], "path": ["scoring", "calculate"]}}}]}, {"name": "💰 Phase 2: Economy & Customization", "item": [{"name": "Get Currency Balance", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/currency/balance", "host": ["{{baseUrl}}"], "path": ["currency", "balance"]}}}, {"name": "Add <PERSON>cy", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_id\": {{userId}},\n  \"currency_type\": \"SYNCOIN\",\n  \"amount\": 1000,\n  \"reason\": \"Testing\"\n}"}, "url": {"raw": "{{baseUrl}}/currency/add", "host": ["{{baseUrl}}"], "path": ["currency", "add"]}}}, {"name": "Get Avatar Items", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/avatar/items", "host": ["{{baseUrl}}"], "path": ["avatar", "items"]}}}, {"name": "Get User Inventory", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/avatar/inventory/{{userId}}", "host": ["{{baseUrl}}"], "path": ["avatar", "inventory", "{{userId}}"]}}}, {"name": "Purchase Avatar Item", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"avatar_id\": 1\n}"}, "url": {"raw": "{{baseUrl}}/avatar/purchase", "host": ["{{baseUrl}}"], "path": ["avatar", "purchase"]}}}, {"name": "Get Available Eggs", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/eggs/available", "host": ["{{baseUrl}}"], "path": ["eggs", "available"]}}}, {"name": "Open Egg", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"egg_id\": 1\n}"}, "url": {"raw": "{{baseUrl}}/eggs/open", "host": ["{{baseUrl}}"], "path": ["eggs", "open"]}}}]}, {"name": "🏆 Phase 3: Social & Leaderboard", "item": [{"name": "Get All Emojis", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/emojis", "host": ["{{baseUrl}}"], "path": ["emojis"]}}}, {"name": "Purchase Emoji", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"emoji_id\": 1\n}"}, "url": {"raw": "{{baseUrl}}/emojis/purchase", "host": ["{{baseUrl}}"], "path": ["emojis", "purchase"]}}}, {"name": "Send <PERSON><PERSON><PERSON> to User", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"target_user_id\": 2,\n  \"emoji_id\": 1,\n  \"context\": \"quiz_completion\"\n}"}, "url": {"raw": "{{baseUrl}}/social/send-emoji", "host": ["{{baseUrl}}"], "path": ["social", "send-emoji"]}}}, {"name": "Global Leaderboard", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/leaderboard/global?limit=10", "host": ["{{baseUrl}}"], "path": ["leaderboard", "global"], "query": [{"key": "limit", "value": "10"}]}}}, {"name": "Subject Leaderboard", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/leaderboard/subject/1?limit=10", "host": ["{{baseUrl}}"], "path": ["leaderboard", "subject", "1"], "query": [{"key": "limit", "value": "10"}]}}}, {"name": "Weekly Rankings", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/leaderboard/weekly?limit=10", "host": ["{{baseUrl}}"], "path": ["leaderboard", "weekly"], "query": [{"key": "limit", "value": "10"}]}}}]}, {"name": "⚔️ Phase 4: Skills Management", "item": [{"name": "Get Skills Shop", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/skills/shop", "host": ["{{baseUrl}}"], "path": ["skills", "shop"]}}}, {"name": "Get Skills by Category - Attack", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/skills/shop/category/ATTACK", "host": ["{{baseUrl}}"], "path": ["skills", "shop", "category", "ATTACK"]}}}, {"name": "Purchase Skill - Blackhole", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"skill_id\": 1\n}"}, "url": {"raw": "{{baseUrl}}/skills/purchase", "host": ["{{baseUrl}}"], "path": ["skills", "purchase"]}}}, {"name": "Purchase Multiple Skills", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"skill_id\": 5\n}"}, "url": {"raw": "{{baseUrl}}/skills/purchase", "host": ["{{baseUrl}}"], "path": ["skills", "purchase"]}}}, {"name": "Get User Inventory", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/skills/inventory", "host": ["{{baseUrl}}"], "path": ["skills", "inventory"]}}}, {"name": "Set Skill Lo<PERSON>ut", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"skill_ids\": [1, 5, 8, 13]\n}"}, "url": {"raw": "{{baseUrl}}/skills/loadout", "host": ["{{baseUrl}}"], "path": ["skills", "loadout"]}}}, {"name": "Get Current Loadout", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/skills/loadout", "host": ["{{baseUrl}}"], "path": ["skills", "loadout"]}}}, {"name": "Get Skill Usage History", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/skills/usage-history", "host": ["{{baseUrl}}"], "path": ["skills", "usage-history"]}}}, {"name": "Get Skill Statistics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/skills/stats", "host": ["{{baseUrl}}"], "path": ["skills", "stats"]}}}]}, {"name": "🏁 Quiz Racing", "item": [{"name": "Initialize Racing Session", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.json().success) {", "    pm.globals.set('sessionId', pm.response.json().data.session_id);", "}"]}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"quiz_id\": 1,\n  \"participants\": [\n    {\"user_id\": 1, \"username\": \"student1\"},\n    {\"user_id\": 2, \"username\": \"student2\"},\n    {\"user_id\": 3, \"username\": \"student3\"}\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/quiz-racing/initialize", "host": ["{{baseUrl}}"], "path": ["quiz-racing", "initialize"]}}}, {"name": "Get Session Data", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/quiz-racing/session/{{sessionId}}", "host": ["{{baseUrl}}"], "path": ["quiz-racing", "session", "{{sessionId}}"]}}}, {"name": "Set Racing Loadout", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"quiz_session_id\": \"{{sessionId}}\",\n  \"skill_ids\": [1, 5, 8, 13]\n}"}, "url": {"raw": "{{baseUrl}}/quiz-racing/loadout", "host": ["{{baseUrl}}"], "path": ["quiz-racing", "loadout"]}}}, {"name": "Get Racing Loadout", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/quiz-racing/loadout/{{sessionId}}", "host": ["{{baseUrl}}"], "path": ["quiz-racing", "loadout", "{{sessionId}}"]}}}, {"name": "Get Racing Statistics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/quiz-racing/stats/{{sessionId}}", "host": ["{{baseUrl}}"], "path": ["quiz-racing", "stats", "{{sessionId}}"]}}}]}, {"name": "💰 Currency & Economy", "item": [{"name": "Get User Balance", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/currency/balance", "host": ["{{baseUrl}}"], "path": ["currency", "balance"]}}}, {"name": "<PERSON><PERSON> (Admin)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_id\": {{userId}},\n  \"currency_type\": \"SYNCOIN\",\n  \"amount\": 1000,\n  \"reason\": \"Testing skills purchase\"\n}"}, "url": {"raw": "{{baseUrl}}/currency/add", "host": ["{{baseUrl}}"], "path": ["currency", "add"]}}}]}]}