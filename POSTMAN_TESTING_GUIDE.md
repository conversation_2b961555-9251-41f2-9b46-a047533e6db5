# 🎮 Hướng Dẫn Test Gamification với Postman

## 📋 Tổng Quan
Collection này được thiết kế để test và debug các vấn đề về **Avatar không hiện**, **Title và Badge không đồng bộ** trong hệ thống Synlearnia.

## 🚀 Cài Đặt

### 1. Import vào Postman
1. Mở Postman
2. Click **Import** 
3. Chọn file `Gamification_Debug_Test.postman_collection.json`
4. Import environment: `Gamification_Environment.postman_environment.json`

### 2. Cấu Hình Environment
1. Chọn environment **"🎮 Gamification Debug Environment"**
2. Kiểm tra các biến:
   - `baseUrl`: `http://localhost:8888` (port Docker của bạn)
   - `testEmail`: Email user để test
   - `testPassword`: Password user để test

### 3. Ki<PERSON><PERSON> Tra Server
Đảm bảo Docker containers đang chạy:
```bash
docker-compose up -d
docker ps  # Kiểm tra containers
```

## 🔍 Quy Trình Test

### Bước 1: Authentication
1. Chạy **"🔐 Authentication > Login"**
2. Kiểm tra Console để xem token được lưu
3. Nếu login thất bại, cập nhật `testEmail` và `testPassword` trong environment

### Bước 2: Debug Current State
Chạy tất cả requests trong folder **"🔍 Debug Current State"** theo thứ tự:

1. **Get User Gamification Info** - Xem tổng quan level, points, titles, badges
2. **Get Avatar Data** - Kiểm tra avatar inventory và equipped items
3. **Get Display Info** - Xem thông tin hiển thị avatar/frame
4. **Get Active Title** - Kiểm tra title đang active
5. **Get User Titles** - Xem danh sách titles đã unlock
6. **Get User Badges** - Xem danh sách badges đã unlock

### Bước 3: Fix Issues
Nếu phát hiện vấn đề, chạy các requests trong **"🔧 Fix Issues"**:

1. **Initialize Avatar System** - Khởi tạo avatar system nếu chưa có
2. **Sync Gamification** - Đồng bộ titles/badges theo level hiện tại

### Bước 4: Additional Tests
Chạy **"🧪 Additional Tests"** để test thêm:

1. **Add Points** - Test level up và auto-unlock
2. **Get Leaderboard** - Xem bảng xếp hạng

## 🐛 Debug Các Vấn đề Thường Gặp

### ❌ Avatar Không Hiện
**Triệu chứng:**
- `Get Avatar Data` trả về `equipped_avatar: null`
- `Get Display Info` trả về `avatar: null`

**Cách fix:**
1. Chạy `Initialize Avatar System`
2. Chạy lại `Get Avatar Data` để kiểm tra
3. Nếu vẫn lỗi, kiểm tra database có data Avatar không

### ❌ Title/Badge Không Đồng Bộ
**Triệu chứng:**
- Level cao nhưng ít titles/badges
- `Get Active Title` trả về `null`
- Completion rate thấp bất thường

**Cách fix:**
1. Chạy `Sync Gamification`
2. Kiểm tra Console để xem titles/badges mới unlock
3. Chạy lại `Get User Titles` và `Get User Badges`

### ❌ Level Không Đúng
**Triệu chứng:**
- Points cao nhưng level thấp
- Level không tăng khi add points

**Cách fix:**
1. Chạy `Add Points` với số points nhỏ (100)
2. Kiểm tra `level_up: true/false`
3. Chạy `Sync Gamification` để force sync

## 📊 Đọc Kết Quả

### Console Output
Mỗi request sẽ log thông tin chi tiết vào Console:

```
✅ Gamification Info Retrieved
📊 Level: 5
🎯 Points: 2500
🏆 Tier: Bronze
👑 Titles: 3/10
🎖️ Badges: 2/8
👑 Active Title: Người Mới Bắt Đầu
```

### Response Data
Kiểm tra tab **Response** để xem raw data:
- `success: true/false`
- `data: {...}` - Dữ liệu chính
- `message: "..."` - Thông báo lỗi nếu có

## 🔧 Troubleshooting

### 🚫 Connection Refused
```
Error: connect ECONNREFUSED 127.0.0.1:8888
```
**Fix:** Kiểm tra Docker containers có chạy không

### 🔐 Unauthorized (401)
```
{"success": false, "message": "Token không hợp lệ"}
```
**Fix:** Chạy lại Login để lấy token mới

### 👤 User Not Found
```
{"success": false, "message": "User không tồn tại"}
```
**Fix:** Tạo user test hoặc cập nhật credentials

### 🗄️ Database Error
```
{"success": false, "message": "Database connection failed"}
```
**Fix:** Kiểm tra database container và connection string

## 📝 Test Cases Quan Trọng

### Test Case 1: User Mới
1. Tạo user mới
2. Login
3. Chạy `Initialize Avatar System`
4. Kiểm tra có default avatar không
5. Add points để test level up

### Test Case 2: User Cũ Bị Lỗi Sync
1. Login user cũ
2. Debug current state
3. Chạy `Sync Gamification`
4. So sánh trước và sau sync

### Test Case 3: Level Up Flow
1. Note current level/points
2. Add points để level up
3. Kiểm tra auto-unlock titles/badges
4. Verify display info updated

## 🎯 Expected Results

### Sau khi Fix Thành Công:
- ✅ Avatar hiển thị trong `Get Display Info`
- ✅ Active title không null
- ✅ Completion rate titles/badges hợp lý với level
- ✅ Level up trigger auto-unlock
- ✅ Display info sync với inventory

### Red Flags:
- ❌ `equipped_avatar: null` sau Initialize
- ❌ `active_title: null` khi có titles
- ❌ Completion rate < 50% với level > 5
- ❌ Level không tăng khi add points
- ❌ Sync không unlock titles/badges mới

## 📞 Support
Nếu gặp vấn đề:
1. Check Console logs trong Postman
2. Check server logs: `docker logs <container_name>`
3. Verify database data trực tiếp
4. Run debug scripts trong container
