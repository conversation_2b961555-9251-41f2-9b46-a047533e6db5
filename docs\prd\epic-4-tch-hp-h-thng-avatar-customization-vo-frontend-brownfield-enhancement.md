# Epic 4: <PERSON><PERSON><PERSON>ệ Thống Avatar & Customization vào Frontend - Brownfield Enhancement

**Epic Goal**: <PERSON><PERSON><PERSON> hợp hệ thống Avatar & Customization từ TASK_2_2 vào giao diện frontend hiện tại, cho ph<PERSON><PERSON> sinh viên tùy chỉnh avatar, khung, hi<PERSON><PERSON> <PERSON>ng tên và sử dụng emoji để thể hiện cá tính và thành tích học tập.

**Business Context**: Backend đã hoàn thành hệ thống với 30 avatar động vật, khung theo tier, hiệu ứng tên cho level cao và emoji system trong TASK_2_2. Frontend hiện tại chỉ có gamification system cơ bản, cần tích hợp avatar customization để sinh viên có trải nghiệm visual phong phú và motivation mạnh mẽ hơn.

**Integration Requirements**: Tích hợp với Avatar APIs từ TASK_2_2 và sử dụng visual assets trong `/public/avatar-animal-pack/`, `/public/vector-ranks-pack/` để hiển thị avatar system hoàn chỉnh.

## Epic Description

**Existing System Context:**

- Current functionality: Frontend có UserLevelBadge component cơ bản với tier system
- Technology stack: Next.js 15, React 19, TypeScript, Radix UI, Tailwind CSS
- Integration points:
  - `useGamification` hook trong `/lib/hooks/use-gamification`
  - `UserLevelBadge` component trong `/components/features/gamification/`
  - Dashboard pages: `/dashboard/page.tsx`
  - API services: `gamificationService` trong `/lib/services/`
- Visual assets: 30 avatar animals trong `/public/avatar-animal-pack/`, frames trong `/public/vector-ranks-pack/`

**Enhancement Details:**

- What's being added/changed:
  - Hệ thống 30 avatar động vật với unlock mechanisms đa dạng
  - Khung avatar theo tier và achievements
  - Hiệu ứng tên CSS cho tier cao (Onyx, Sapphire, Ruby, Amethyst, Master)
  - Emoji system với 9 categories cho social interactions
  - Inventory management và collection progress tracking
  - Rarity system với decomposition values
- How it integrates:
  - Sử dụng API endpoints mới: `/api/avatar/my-data`, `/api/avatar/equip`, `/api/avatar/available-items`
  - Mapping avatar assets với database records
  - Integration với existing gamification system
- Success criteria:
  - Sinh viên có thể customize avatar, frame, name effects
  - Collection progress motivates continued engagement
  - Avatar displays consistently across app
  - Emoji system enhances social interactions
  - No regression trong existing functionality

## Story 4.1: Tạo Avatar Service và API Integration

Là một **developer**,
Tôi muốn **tích hợp Avatar APIs vào frontend service layer**,
Để **có thể fetch và quản lý dữ liệu avatar customization từ backend**.

### Acceptance Criteria

1. Tạo `avatarService` trong `/lib/services/api/avatar.service.ts`
2. Implement TypeScript types cho Avatar API responses:
   - `AvatarData`, `AvatarFrame`, `NameEffect`, `EmojiData`
   - `UserInventory`, `UserCustomization`, `CollectionProgress`
3. Tích hợp các API endpoints chính:
   - `GET /api/avatar/my-data` - Dữ liệu avatar hoàn chỉnh
   - `GET /api/avatar/available-items` - Items có thể mở khóa
   - `POST /api/avatar/equip` - Trang bị item
   - `GET /api/avatar/collection-progress` - Tiến độ sưu tập
4. Tạo `useAvatar` hook trong `/lib/hooks/use-avatar.ts`
5. Add error handling và loading states cho avatar APIs
6. Implement caching strategy cho avatar data

### Integration Verification

- **IV1**: API calls return correct avatar data structure với proper typing
- **IV2**: TypeScript compilation successful với new types
- **IV3**: Error handling works correctly cho network failures
- **IV4**: Caching reduces unnecessary API calls

## Story 4.2: Xây Dựng Avatar Display Component

Là một **sinh viên**,
Tôi muốn **thấy avatar hiện tại của mình với khung và hiệu ứng tên đẹp mắt**,
Để **thể hiện cá tính và thành tích học tập qua visual elements**.

### Acceptance Criteria

1. Tạo `AvatarDisplay` component trong `/components/features/avatar/`
2. Hiển thị:
   - Avatar image từ `/public/avatar-animal-pack/` với proper fallbacks
   - Frame overlay từ `/public/vector-ranks-pack/` aligned perfectly
   - Name với CSS effects theo tier (gold, blue, red, purple, rainbow)
   - Rarity indicator với color coding (common, rare, epic, legendary)
3. Support multiple sizes: small (32px), medium (64px), large (128px)
4. Hover effects và smooth animations
5. Integrate với existing design system (Radix UI + Tailwind)
6. Lazy loading cho avatar images với placeholders

### Integration Verification

- **IV1**: Avatar images load correctly với proper fallbacks
- **IV2**: Frame overlays align perfectly với avatar boundaries
- **IV3**: Name effects render correctly cho tất cả tier levels
- **IV4**: Component performance optimized với image loading

## Story 4.3: Tạo Avatar Customization Interface

Là một **sinh viên**,
Tôi muốn **có giao diện để thay đổi avatar, khung và hiệu ứng tên**,
Để **tùy chỉnh appearance theo sở thích và thể hiện achievements**.

### Acceptance Criteria

1. Tạo `AvatarCustomization` page trong `/app/dashboard/avatar/page.tsx`
2. Tabs cho từng loại item:
   - **Avatars**: Grid hiển thị 30 avatars với unlock status
   - **Frames**: Grid hiển thị frames theo tier và achievements
   - **Name Effects**: Preview effects với unlock requirements
3. Item states với visual indicators:
   - **Owned**: Có thể equip ngay (green border)
   - **Unlockable**: Hiển thị requirements (yellow border)
   - **Locked**: Grayed out với unlock conditions (gray border)
4. Preview functionality trước khi equip
5. Equip/unequip với real-time updates
6. Search và filter functionality cho large collections
7. Responsive design cho mobile devices

### Integration Verification

- **IV1**: Item grids load efficiently với lazy loading
- **IV2**: Preview accurately reflects final appearance
- **IV3**: Equip actions update immediately across app
- **IV4**: Mobile interface intuitive và touch-friendly

## Story 4.4: Collection Progress và Achievement Tracking

Là một **sinh viên**,
Tôi muốn **theo dõi tiến độ sưu tập và achievements**,
Để **có motivation collect tất cả items và unlock rare items**.

### Acceptance Criteria

1. Tạo `CollectionProgress` component trong avatar customization
2. Hiển thị:
   - Overall collection percentage với animated progress bar
   - Progress bars cho từng category (Avatars, Frames, Emojis)
   - Rarity breakdown (Common, Rare, Epic, Legendary counts)
   - Next unlock milestones với requirements
3. Achievement badges cho collection milestones:
   - "First Collection" (5 items)
   - "Collector" (15 items)
   - "Master Collector" (25 items)
   - "Completionist" (all items)
4. Statistics dashboard với:
   - Total items owned
   - Rarest item owned
   - Collection rank among users
5. Filter và search functionality
6. Export collection summary feature

### Integration Verification

- **IV1**: Progress calculations accurate và real-time
- **IV2**: Achievement badges unlock correctly
- **IV3**: Statistics reflect current collection state
- **IV4**: Performance optimized cho large datasets

## Story 4.5: Emoji System Integration

Là một **sinh viên**,
Tôi muốn **sử dụng emoji system để express reactions**,
Để **có thêm cách tương tác xã hội trong quiz và leaderboard**.

### Acceptance Criteria

1. Tạo `EmojiPicker` component trong `/components/features/avatar/`
2. 9 categories với organized layout:
   - GENERAL, HAPPY, SAD, ANGRY, SURPRISED, LOVE, CELEBRATION, ANIMALS, SPECIAL
3. Unlock status cho từng emoji với visual indicators
4. Integration points:
   - Quiz completion reactions
   - Leaderboard interactions
   - Profile customization
   - Chat/messaging features (future)
5. Emoji preview với hover animations
6. Recent/favorite emoji shortcuts
7. Search functionality trong emoji picker

### Integration Verification

- **IV1**: Emoji categories load correctly với proper icons
- **IV2**: Unlock system works với level progression
- **IV3**: Emoji interactions work trong quiz và social features
- **IV4**: Picker performance smooth với large emoji sets

## Story 4.6: Avatar Integration vào Existing Components

Là một **user**,
Tôi muốn **thấy avatar customization reflected across toàn bộ app**,
Để **có consistent visual identity và thể hiện progression**.

### Acceptance Criteria

1. Update existing components để hiển thị avatar:
   - **UserLevelBadge**: Thêm avatar display option
   - **Dashboard**: Avatar section với quick customization access
   - **Leaderboard**: Avatar hiển thị cho tất cả users
   - **Quiz interface**: Avatar trong user info panel
2. Name effects hiển thị consistently:
   - Leaderboard names với tier effects
   - Quiz participant names
   - Profile displays
   - Achievement notifications
3. Frame integration:
   - Profile pictures với frames
   - Achievement displays
   - Social interaction contexts
4. Performance optimization:
   - Image preloading cho common avatars
   - Lazy loading cho avatar grids
   - CDN caching cho static assets
5. Responsive design cho mobile devices
6. Accessibility features cho screen readers

### Integration Verification

- **IV1**: Avatar displays consistently across all components
- **IV2**: Name effects render correctly trong different contexts
- **IV3**: Performance maintained với avatar loading
- **IV4**: Accessibility standards met

## Compatibility Requirements

- [x] Existing gamification system enhanced với avatar features
- [x] Level progression triggers avatar unlocks automatically
- [x] Mobile responsiveness cho tất cả avatar components
- [x] API backward compatibility với existing user data
- [x] Visual consistency với existing design system
- [x] Performance impact minimal với optimized asset loading

## Risk Mitigation

- **Primary Risk**: Large number of avatar assets impact loading performance
- **Mitigation**:
  - Implement lazy loading cho avatar grids
  - Image optimization và compression (WebP format)
  - Progressive loading với skeleton placeholders
  - CDN caching cho static assets
  - Asset bundling optimization

## Definition of Done

- [x] All stories completed với acceptance criteria met
- [x] Avatar system fully integrated vào frontend
- [x] Customization interface intuitive và responsive
- [x] Collection progress tracking accurate và motivating
- [x] Emoji system functional và engaging
- [x] Avatar displays consistent across entire app
- [x] No regression trong existing gamification features
- [x] Performance optimized với proper asset management
- [x] Sinh viên có rich customization experience với 30 avatars, tier-based frames, name effects, và emoji system

---

_PRD này được tạo để đảm bảo việc tái cấu trúc frontend được thực hiện một cách an toàn, có hệ thống và không ảnh hưởng đến functionality hiện có._

**CRITICAL NOTE**: This PRD implements a **NO TESTING POLICY**. All development work must completely eliminate testing considerations and focus solely on functionality implementation with quality assurance through TypeScript compilation and basic runtime verification.
