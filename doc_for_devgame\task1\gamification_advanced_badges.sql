-- =====================================================
-- TASK 1.2: ADVANCED BADGE SYSTEM
-- Mở rộng hệ thống badges với achievement-based system
-- =====================================================

-- Thêm cột mới cho Badges table để hỗ trợ achievement system
ALTER TABLE "Badges" ADD COLUMN IF NOT EXISTS "badge_type" VARCHAR(20) DEFAULT 'level';
ALTER TABLE "Badges" ADD COLUMN IF NOT EXISTS "unlock_criteria" JSONB DEFAULT '{}';
ALTER TABLE "Badges" ADD COLUMN IF NOT EXISTS "is_active" BOOLEAN DEFAULT TRUE;
ALTER TABLE "Badges" ADD COLUMN IF NOT EXISTS "event_type" VARCHAR(50) DEFAULT NULL;
ALTER TABLE "Badges" ADD COLUMN IF NOT EXISTS "valid_from" TIMESTAMP DEFAULT NULL;
ALTER TABLE "Badges" ADD COLUMN IF NOT EXISTS "valid_until" TIMESTAMP DEFAULT NULL;

-- Thêm index cho performance
CREATE INDEX IF NOT EXISTS idx_badges_type ON "Badges"("badge_type");
CREATE INDEX IF NOT EXISTS idx_badges_event ON "Badges"("event_type");
CREATE INDEX IF NOT EXISTS idx_badges_active ON "Badges"("is_active");

-- =====================================================
-- ACHIEVEMENT-BASED BADGES
-- =====================================================

-- Clear existing badges và thêm badges mới với achievement system
DELETE FROM "UserBadges";
DELETE FROM "Badges";

-- Level-based Badges (Tier progression) - Giữ nguyên từ hệ thống cũ
INSERT INTO "Badges" ("tier_name", "badge_name", "badge_description", "unlock_level", "rarity", "badge_type", "unlock_criteria") VALUES
-- Wood Tier
('Wood', 'First Steps', 'Bắt đầu hành trình học tập', 1, 'common', 'level', '{"type": "level", "required_level": 1}'),
('Wood', 'Wood Apprentice', 'Học việc tier Wood', 6, 'rare', 'level', '{"type": "level", "required_level": 6}'),
('Wood', 'Wood Master', 'Thành thạo tier Wood', 12, 'epic', 'level', '{"type": "level", "required_level": 12}'),

-- Bronze Tier
('Bronze', 'Bronze Warrior', 'Chiến binh đồng thực thụ', 13, 'common', 'level', '{"type": "level", "required_level": 13}'),
('Bronze', 'Bronze Elite', 'Tinh anh đồng', 18, 'rare', 'level', '{"type": "level", "required_level": 18}'),
('Bronze', 'Bronze Legend', 'Huyền thoại tier Bronze', 24, 'epic', 'level', '{"type": "level", "required_level": 24}'),

-- Silver Tier
('Silver', 'Silver Guardian', 'Người bảo vệ bạc', 25, 'common', 'level', '{"type": "level", "required_level": 25}'),
('Silver', 'Silver Champion', 'Nhà vô địch bạc', 30, 'rare', 'level', '{"type": "level", "required_level": 30}'),
('Silver', 'Silver Perfection', 'Hoàn hảo tier Silver', 36, 'epic', 'level', '{"type": "level", "required_level": 36}'),

-- Gold Tier
('Gold', 'Gold Champion', 'Nhà vô địch vàng', 37, 'common', 'level', '{"type": "level", "required_level": 37}'),
('Gold', 'Gold Master', 'Bậc thầy vàng', 42, 'rare', 'level', '{"type": "level", "required_level": 42}'),
('Gold', 'Gold Mastery', 'Tinh thông tier Gold', 48, 'epic', 'level', '{"type": "level", "required_level": 48}'),

-- Platinum Tier
('Platinum', 'Platinum Elite', 'Tầng lớp ưu tú bạch kim', 49, 'common', 'level', '{"type": "level", "required_level": 49}'),
('Platinum', 'Platinum Lord', 'Lãnh chủ bạch kim', 54, 'rare', 'level', '{"type": "level", "required_level": 54}'),
('Platinum', 'Platinum Ascension', 'Thăng thiên bạch kim', 60, 'legendary', 'level', '{"type": "level", "required_level": 60}'),

-- Higher tiers...
('Onyx', 'Onyx Overlord', 'Chúa tể Onyx', 61, 'common', 'level', '{"type": "level", "required_level": 61}'),
('Sapphire', 'Sapphire Sage', 'Hiền triết Sapphire', 73, 'common', 'level', '{"type": "level", "required_level": 73}'),
('Ruby', 'Ruby Emperor', 'Hoàng đế Ruby', 85, 'common', 'level', '{"type": "level", "required_level": 85}'),
('Amethyst', 'Amethyst Deity', 'Thần linh Amethyst', 97, 'common', 'level', '{"type": "level", "required_level": 97}'),
('Master', 'Master of All', 'Bậc thầy vạn vật', 109, 'common', 'level', '{"type": "level", "required_level": 109}');

-- =====================================================
-- ACHIEVEMENT-BASED BADGES (Performance & Behavior)
-- =====================================================

INSERT INTO "Badges" ("tier_name", "badge_name", "badge_description", "unlock_level", "rarity", "badge_type", "unlock_criteria") VALUES
-- Speed-based achievements
('Wood', 'Speed Demon', 'Trả lời nhanh như chớp - 10 câu dưới 5 giây', 1, 'rare', 'achievement', '{"type": "speed_answers", "count": 10, "max_time": 5000}'),
('Bronze', 'Lightning Fast', 'Nhanh như chớp - 25 câu dưới 3 giây', 13, 'rare', 'achievement', '{"type": "speed_answers", "count": 25, "max_time": 3000}'),
('Silver', 'Flash Master', 'Bậc thầy tia chớp - 50 câu dưới 2 giây', 25, 'epic', 'achievement', '{"type": "speed_answers", "count": 50, "max_time": 2000}'),
('Gold', 'Time Lord', 'Chúa tể thời gian - 100 câu dưới 1.5 giây', 37, 'legendary', 'achievement', '{"type": "speed_answers", "count": 100, "max_time": 1500}'),

-- Streak-based achievements
('Wood', 'Streak Starter', 'Chuỗi khởi đầu - 5 câu đúng liên tiếp', 1, 'common', 'achievement', '{"type": "streak", "count": 5}'),
('Wood', 'Combo King', 'Vua combo - 10 câu đúng liên tiếp', 1, 'rare', 'achievement', '{"type": "streak", "count": 10}'),
('Bronze', 'Unstoppable', 'Không thể cản - 15 câu đúng liên tiếp', 13, 'epic', 'achievement', '{"type": "streak", "count": 15}'),
('Silver', 'Legendary Streak', 'Chuỗi huyền thoại - 20 câu đúng liên tiếp', 25, 'legendary', 'achievement', '{"type": "streak", "count": 20}'),
('Gold', 'Infinite Chain', 'Chuỗi vô tận - 30 câu đúng liên tiếp', 37, 'legendary', 'achievement', '{"type": "streak", "count": 30}'),

-- Perfect score achievements
('Wood', 'Perfect Score', 'Điểm số hoàn hảo - 1 quiz 100%', 1, 'rare', 'achievement', '{"type": "perfect_quiz", "count": 1}'),
('Bronze', 'Perfectionist', 'Người hoàn hảo - 3 quiz 100%', 13, 'epic', 'achievement', '{"type": "perfect_quiz", "count": 3}'),
('Silver', 'Flawless Victory', 'Chiến thắng hoàn hảo - 5 quiz 100%', 25, 'legendary', 'achievement', '{"type": "perfect_quiz", "count": 5}'),
('Gold', 'Perfection Incarnate', 'Hiện thân hoàn hảo - 10 quiz 100%', 37, 'legendary', 'achievement', '{"type": "perfect_quiz", "count": 10}'),

-- Quiz completion achievements
('Wood', 'Quiz Newbie', 'Người mới quiz - 5 quiz hoàn thành', 1, 'common', 'achievement', '{"type": "quiz_completed", "count": 5}'),
('Wood', 'Quiz Addict', 'Nghiện quiz - 10 quiz hoàn thành', 1, 'common', 'achievement', '{"type": "quiz_completed", "count": 10}'),
('Bronze', 'Knowledge Hunter', 'Thợ săn tri thức - 25 quiz hoàn thành', 13, 'rare', 'achievement', '{"type": "quiz_completed", "count": 25}'),
('Silver', 'Scholar', 'Học giả - 50 quiz hoàn thành', 25, 'rare', 'achievement', '{"type": "quiz_completed", "count": 50}'),
('Gold', 'Quiz Master', 'Bậc thầy quiz - 100 quiz hoàn thành', 37, 'epic', 'achievement', '{"type": "quiz_completed", "count": 100}'),
('Platinum', 'Quiz Legend', 'Huyền thoại quiz - 200 quiz hoàn thành', 49, 'legendary', 'achievement', '{"type": "quiz_completed", "count": 200}'),

-- Subject mastery achievements
('Bronze', 'HTML Expert', 'Chuyên gia HTML - 100 điểm HTML', 13, 'rare', 'achievement', '{"type": "subject_mastery", "subject": "HTML", "points": 100}'),
('Bronze', 'CSS Master', 'Bậc thầy CSS - 100 điểm CSS', 13, 'rare', 'achievement', '{"type": "subject_mastery", "subject": "CSS", "points": 100}'),
('Silver', 'JavaScript Guru', 'Guru JavaScript - 150 điểm JS', 25, 'epic', 'achievement', '{"type": "subject_mastery", "subject": "JavaScript", "points": 150}'),
('Gold', 'Full Stack Hero', 'Anh hùng Full Stack - Thành thạo 5 môn', 37, 'legendary', 'achievement', '{"type": "multi_subject", "subjects": 5, "min_points": 100}'),

-- Daily/Weekly achievements
('Wood', 'Daily Warrior', 'Chiến binh hàng ngày - 7 ngày liên tiếp', 1, 'rare', 'achievement', '{"type": "daily_streak", "days": 7}'),
('Bronze', 'Weekly Champion', 'Nhà vô địch tuần - 30 ngày liên tiếp', 13, 'epic', 'achievement', '{"type": "daily_streak", "days": 30}'),
('Silver', 'Monthly Legend', 'Huyền thoại tháng - 90 ngày liên tiếp', 25, 'legendary', 'achievement', '{"type": "daily_streak", "days": 90}');

-- =====================================================
-- SEASONAL & EVENT BADGES
-- =====================================================

INSERT INTO "Badges" ("tier_name", "badge_name", "badge_description", "unlock_level", "rarity", "badge_type", "unlock_criteria", "event_type", "valid_from", "valid_until") VALUES
-- Tết 2025 Event
('Wood', 'Tết Champion 2025', 'Nhà vô địch Tết Nguyên Đán 2025', 1, 'legendary', 'event', '{"type": "event_participation", "event": "tet_2025", "min_score": 500}', 'tet_2025', '2025-01-25 00:00:00', '2025-02-15 23:59:59'),
('Bronze', 'Dragon Slayer', 'Người diệt rồng - Sự kiện Tết 2025', 13, 'legendary', 'event', '{"type": "event_boss", "event": "tet_2025", "boss": "dragon"}', 'tet_2025', '2025-01-25 00:00:00', '2025-02-15 23:59:59'),

-- Valentine 2025 Event
('Wood', 'Love Scholar', 'Học giả tình yêu - Valentine 2025', 1, 'epic', 'event', '{"type": "event_participation", "event": "valentine_2025", "min_score": 300}', 'valentine_2025', '2025-02-10 00:00:00', '2025-02-20 23:59:59'),

-- Summer 2025 Event
('Silver', 'Summer Warrior', 'Chiến binh mùa hè 2025', 25, 'epic', 'event', '{"type": "event_participation", "event": "summer_2025", "min_score": 800}', 'summer_2025', '2025-06-01 00:00:00', '2025-08-31 23:59:59'),

-- Halloween 2025 Event
('Gold', 'Spooky Master', 'Bậc thầy ma quái - Halloween 2025', 37, 'legendary', 'event', '{"type": "event_participation", "event": "halloween_2025", "min_score": 1000}', 'halloween_2025', '2025-10-25 00:00:00', '2025-11-05 23:59:59'),

-- Christmas 2025 Event
('Platinum', 'Santa Helper', 'Trợ thủ ông già Noel 2025', 49, 'legendary', 'event', '{"type": "event_participation", "event": "christmas_2025", "min_score": 1200}', 'christmas_2025', '2025-12-15 00:00:00', '2025-12-31 23:59:59');

-- =====================================================
-- SPECIAL MILESTONE BADGES
-- =====================================================

INSERT INTO "Badges" ("tier_name", "badge_name", "badge_description", "unlock_level", "rarity", "badge_type", "unlock_criteria") VALUES
-- Milestone achievements
('Wood', 'First Blood', 'Câu trả lời đúng đầu tiên', 1, 'common', 'milestone', '{"type": "first_correct_answer"}'),
('Wood', 'Century Club', 'Câu lạc bộ trăm điểm', 1, 'rare', 'milestone', '{"type": "total_points", "points": 100}'),
('Bronze', 'Thousand Club', 'Câu lạc bộ nghìn điểm', 13, 'epic', 'milestone', '{"type": "total_points", "points": 1000}'),
('Silver', 'Ten Thousand Club', 'Câu lạc bộ vạn điểm', 25, 'legendary', 'milestone', '{"type": "total_points", "points": 10000}'),

-- Social achievements
('Bronze', 'Team Player', 'Người chơi đồng đội - Tham gia 10 quiz nhóm', 13, 'rare', 'social', '{"type": "group_quiz", "count": 10}'),
('Silver', 'Leader', 'Người lãnh đạo - Tạo 5 phòng quiz', 25, 'epic', 'social', '{"type": "room_created", "count": 5}'),
('Gold', 'Mentor', 'Người cố vấn - Giúp đỡ 20 người mới', 37, 'legendary', 'social', '{"type": "help_newbies", "count": 20}');

-- =====================================================
-- COMMENTS & METADATA
-- =====================================================

COMMENT ON COLUMN "Badges"."badge_type" IS 'Loại badge: level, achievement, event, milestone, social';
COMMENT ON COLUMN "Badges"."unlock_criteria" IS 'Điều kiện unlock dạng JSON';
COMMENT ON COLUMN "Badges"."event_type" IS 'Loại sự kiện (nếu là event badge)';
COMMENT ON COLUMN "Badges"."valid_from" IS 'Thời gian bắt đầu hiệu lực (cho event badges)';
COMMENT ON COLUMN "Badges"."valid_until" IS 'Thời gian kết thúc hiệu lực (cho event badges)';

-- =====================================================
-- BADGE PROGRESS TRACKING TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS "BadgeProgress" (
    "progress_id" SERIAL PRIMARY KEY,
    "user_id" INTEGER NOT NULL,
    "badge_id" INTEGER NOT NULL,
    "current_progress" JSONB DEFAULT '{}',
    "progress_percentage" DECIMAL(5,2) DEFAULT 0.00,
    "is_completed" BOOLEAN DEFAULT FALSE,
    "started_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "completed_at" TIMESTAMP DEFAULT NULL,
    "updated_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY ("user_id") REFERENCES "Users"("user_id") ON DELETE CASCADE,
    FOREIGN KEY ("badge_id") REFERENCES "Badges"("badge_id") ON DELETE CASCADE,
    UNIQUE("user_id", "badge_id")
);

CREATE INDEX IF NOT EXISTS idx_badge_progress_user ON "BadgeProgress"("user_id");
CREATE INDEX IF NOT EXISTS idx_badge_progress_badge ON "BadgeProgress"("badge_id");
CREATE INDEX IF NOT EXISTS idx_badge_progress_completed ON "BadgeProgress"("is_completed");

COMMENT ON TABLE "BadgeProgress" IS 'Theo dõi tiến độ đạt badge của user';

-- =====================================================
-- FUNCTIONS FOR BADGE SYSTEM
-- =====================================================

-- Function để check badge criteria
CREATE OR REPLACE FUNCTION check_badge_criteria(
    p_user_id INTEGER,
    p_badge_id INTEGER
) RETURNS BOOLEAN AS $$
DECLARE
    badge_criteria JSONB;
    user_stats JSONB;
    criteria_type TEXT;
BEGIN
    -- Lấy criteria của badge
    SELECT unlock_criteria INTO badge_criteria
    FROM "Badges" 
    WHERE badge_id = p_badge_id;
    
    -- Lấy stats của user (sẽ implement sau)
    -- SELECT gamification_stats INTO user_stats
    -- FROM "Users"
    -- WHERE user_id = p_user_id;
    
    criteria_type := badge_criteria->>'type';
    
    -- Implement logic check criteria ở đây
    -- Tạm thời return true để test
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

COMMENT ON FUNCTION check_badge_criteria IS 'Kiểm tra điều kiện đạt badge';

-- Trigger để update badge progress
CREATE OR REPLACE FUNCTION update_badge_progress_trigger()
RETURNS TRIGGER AS $$
BEGIN
    -- Logic update badge progress sẽ implement sau
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- INITIAL DATA VERIFICATION
-- =====================================================

-- Verify data
SELECT 
    badge_type,
    COUNT(*) as count,
    COUNT(CASE WHEN rarity = 'common' THEN 1 END) as common,
    COUNT(CASE WHEN rarity = 'rare' THEN 1 END) as rare,
    COUNT(CASE WHEN rarity = 'epic' THEN 1 END) as epic,
    COUNT(CASE WHEN rarity = 'legendary' THEN 1 END) as legendary
FROM "Badges" 
GROUP BY badge_type
ORDER BY badge_type;

-- Show total badges
SELECT COUNT(*) as total_badges FROM "Badges";
