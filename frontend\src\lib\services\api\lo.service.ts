import api from "./client";

// Interface cho tham số phân trang
interface PaginationParams {
  page?: number;
  limit?: number;
}

// Cấu trúc dữ liệu LO từ API
export interface LOResponse {
  lo_id: number;
  name: string;
  description?: string;
  subject_id?: number;
  Chapters?: Array<{
    chapter_id: number;
    name: string;
  }>;
}

// Response từ API getAllLOs
export interface LOPaginatedResponse {
  totalItems: number;
  totalPages: number;
  currentPage: number;
  los: LOResponse[];
}

// Response từ API getLOsBySubject
export interface LOsBySubjectResponse {
  totalItems: number;
  los: LOResponse[];
}

// Service quản lý mục tiêu học tập (Learning Objectives)
export const loService = {
  // Lấy danh sách tất cả mục tiêu học tập (có phân trang)
  getAllLOs: async (
    params: PaginationParams = {}
  ): Promise<LOPaginatedResponse> => {
    const { page = 1, limit = 10 } = params;
    const response = await api.get("/los", {
      params: { page, limit },
    });
    return response.data;
  },

  // Lấy mục tiêu học tập theo ID
  getLOById: async (loId: number): Promise<LOResponse> => {
    const response = await api.get(`/los/${loId}`);
    return response.data;
  },

  // Lấy danh sách mục tiêu học tập theo môn học
  getLOsBySubject: async (subjectId: number): Promise<LOsBySubjectResponse> => {
    const response = await api.get(`/los/subject/${subjectId}`);
    return response.data;
  },

  // Tạo mục tiêu học tập mới
  createLO: async (data: {
    subject_id: number;
    name: string;
    description?: string;
  }): Promise<LOResponse> => {
    const response = await api.post("/los", data);
    return response.data;
  },

  // Cập nhật mục tiêu học tập
  updateLO: async (
    loId: number,
    data: {
      subject_id?: number;
      name?: string;
      description?: string;
    }
  ): Promise<LOResponse> => {
    const response = await api.put(`/los/${loId}`, data);
    return response.data;
  },

  // Xóa mục tiêu học tập
  deleteLO: async (loId: number): Promise<{ message: string }> => {
    const response = await api.delete(`/los/${loId}`);
    return response.data;
  },
};

export default loService;
