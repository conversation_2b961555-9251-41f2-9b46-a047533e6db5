-- =====================================================
-- GAMIFICATION CURRENCY SYSTEM FOR SYNLEARNIA
-- Task 2.1: Hệ thống tiền tệ (SynCoin & Kristal)
-- =====================================================

-- Bắt đầu transaction
BEGIN;

-- =====================================================
-- 1. BẢNG CURRENCIES - Định nghĩa các loại tiền tệ
-- =====================================================

CREATE TABLE IF NOT EXISTS "Currencies" (
    "currency_id" SERIAL PRIMARY KEY,
    "currency_code" VARCHAR(10) NOT NULL UNIQUE,
    "currency_name" VARCHAR(50) NOT NULL,
    "description" TEXT,
    "icon_path" VARCHAR(255),
    "is_premium" BOOLEAN NOT NULL DEFAULT FALSE,
    "exchange_rate" DECIMAL(10,4) DEFAULT 1.0000,
    "max_daily_earn" INTEGER DEFAULT NULL,
    "is_active" BOOLEAN NOT NULL DEFAULT TRUE,
    "created_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Thêm comment cho bảng
COMMENT ON TABLE "Currencies" IS 'Định nghĩa các loại tiền tệ trong hệ thống';
COMMENT ON COLUMN "Currencies"."currency_code" IS 'Mã tiền tệ (SYNC, KRIS)';
COMMENT ON COLUMN "Currencies"."currency_name" IS 'Tên tiền tệ (SynCoin, Kristal)';
COMMENT ON COLUMN "Currencies"."is_premium" IS 'Có phải tiền tệ cao cấp không';
COMMENT ON COLUMN "Currencies"."exchange_rate" IS 'Tỷ giá quy đổi so với SynCoin';
COMMENT ON COLUMN "Currencies"."max_daily_earn" IS 'Giới hạn kiếm tối đa mỗi ngày';

-- =====================================================
-- 2. BẢNG USER_CURRENCIES - Số dư tiền tệ của user
-- =====================================================

CREATE TABLE IF NOT EXISTS "UserCurrencies" (
    "user_currency_id" SERIAL PRIMARY KEY,
    "user_id" INTEGER NOT NULL,
    "currency_id" INTEGER NOT NULL,
    "balance" BIGINT NOT NULL DEFAULT 0,
    "total_earned" BIGINT NOT NULL DEFAULT 0,
    "total_spent" BIGINT NOT NULL DEFAULT 0,
    "daily_earned_today" INTEGER NOT NULL DEFAULT 0,
    "last_earn_date" DATE DEFAULT CURRENT_DATE,
    "created_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign keys
    FOREIGN KEY ("user_id") REFERENCES "Users"("user_id") ON DELETE CASCADE,
    FOREIGN KEY ("currency_id") REFERENCES "Currencies"("currency_id") ON DELETE CASCADE,
    
    -- Unique constraint
    UNIQUE("user_id", "currency_id")
);

-- Thêm comment cho bảng
COMMENT ON TABLE "UserCurrencies" IS 'Số dư tiền tệ của từng user';
COMMENT ON COLUMN "UserCurrencies"."balance" IS 'Số dư hiện tại';
COMMENT ON COLUMN "UserCurrencies"."total_earned" IS 'Tổng số tiền đã kiếm được';
COMMENT ON COLUMN "UserCurrencies"."total_spent" IS 'Tổng số tiền đã tiêu';
COMMENT ON COLUMN "UserCurrencies"."daily_earned_today" IS 'Số tiền kiếm được hôm nay';

-- =====================================================
-- 3. BẢNG CURRENCY_TRANSACTIONS - Lịch sử giao dịch
-- =====================================================

CREATE TABLE IF NOT EXISTS "CurrencyTransactions" (
    "transaction_id" SERIAL PRIMARY KEY,
    "user_id" INTEGER NOT NULL,
    "currency_id" INTEGER NOT NULL,
    "transaction_type" VARCHAR(20) NOT NULL CHECK (transaction_type IN ('EARN', 'SPEND', 'TRANSFER', 'ADMIN_ADJUST')),
    "amount" BIGINT NOT NULL,
    "balance_before" BIGINT NOT NULL,
    "balance_after" BIGINT NOT NULL,
    "source_type" VARCHAR(30) NOT NULL,
    "source_id" INTEGER,
    "description" TEXT,
    "metadata" JSONB DEFAULT '{}',
    "created_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign keys
    FOREIGN KEY ("user_id") REFERENCES "Users"("user_id") ON DELETE CASCADE,
    FOREIGN KEY ("currency_id") REFERENCES "Currencies"("currency_id") ON DELETE CASCADE
);

-- Thêm comment cho bảng
COMMENT ON TABLE "CurrencyTransactions" IS 'Lịch sử giao dịch tiền tệ';
COMMENT ON COLUMN "CurrencyTransactions"."transaction_type" IS 'Loại giao dịch: EARN, SPEND, TRANSFER, ADMIN_ADJUST';
COMMENT ON COLUMN "CurrencyTransactions"."source_type" IS 'Nguồn giao dịch: QUIZ_COMPLETION, DAILY_LOGIN, ITEM_DECOMPOSE, SHOP_PURCHASE, etc.';
COMMENT ON COLUMN "CurrencyTransactions"."source_id" IS 'ID của nguồn (quiz_id, item_id, etc.)';
COMMENT ON COLUMN "CurrencyTransactions"."metadata" IS 'Thông tin bổ sung dạng JSON';

-- =====================================================
-- 4. BẢNG CURRENCY_EARNING_RULES - Quy tắc kiếm tiền
-- =====================================================

CREATE TABLE IF NOT EXISTS "CurrencyEarningRules" (
    "rule_id" SERIAL PRIMARY KEY,
    "currency_id" INTEGER NOT NULL,
    "source_type" VARCHAR(30) NOT NULL,
    "base_amount" INTEGER NOT NULL DEFAULT 0,
    "bonus_conditions" JSONB DEFAULT '{}',
    "daily_limit" INTEGER DEFAULT NULL,
    "is_active" BOOLEAN NOT NULL DEFAULT TRUE,
    "priority" INTEGER NOT NULL DEFAULT 0,
    "description" TEXT,
    "created_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign keys
    FOREIGN KEY ("currency_id") REFERENCES "Currencies"("currency_id") ON DELETE CASCADE
);

-- Thêm comment cho bảng
COMMENT ON TABLE "CurrencyEarningRules" IS 'Quy tắc kiếm tiền tệ từ các hoạt động';
COMMENT ON COLUMN "CurrencyEarningRules"."source_type" IS 'Loại hoạt động: QUIZ_COMPLETION, DAILY_LOGIN, ACHIEVEMENT_UNLOCK, etc.';
COMMENT ON COLUMN "CurrencyEarningRules"."base_amount" IS 'Số tiền cơ bản nhận được';
COMMENT ON COLUMN "CurrencyEarningRules"."bonus_conditions" IS 'Điều kiện bonus dạng JSON';
COMMENT ON COLUMN "CurrencyEarningRules"."daily_limit" IS 'Giới hạn kiếm mỗi ngày cho rule này';

-- =====================================================
-- 5. BẢNG ITEM_DECOMPOSITION - Phân giải vật phẩm
-- =====================================================

CREATE TABLE IF NOT EXISTS "ItemDecomposition" (
    "decomposition_id" SERIAL PRIMARY KEY,
    "item_type" VARCHAR(20) NOT NULL CHECK (item_type IN ('AVATAR', 'EMOJI', 'FRAME', 'BADGE')),
    "item_id" INTEGER NOT NULL,
    "rarity" VARCHAR(20) NOT NULL CHECK (rarity IN ('COMMON', 'UNCOMMON', 'RARE', 'EPIC', 'LEGENDARY', 'MYTHIC')),
    "kristal_value" INTEGER NOT NULL,
    "is_active" BOOLEAN NOT NULL DEFAULT TRUE,
    "created_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Thêm comment cho bảng
COMMENT ON TABLE "ItemDecomposition" IS 'Giá trị phân giải vật phẩm thành Kristal';
COMMENT ON COLUMN "ItemDecomposition"."item_type" IS 'Loại vật phẩm: AVATAR, EMOJI, FRAME, BADGE';
COMMENT ON COLUMN "ItemDecomposition"."item_id" IS 'ID của vật phẩm tương ứng';
COMMENT ON COLUMN "ItemDecomposition"."rarity" IS 'Độ hiếm của vật phẩm';
COMMENT ON COLUMN "ItemDecomposition"."kristal_value" IS 'Số Kristal nhận được khi phân giải';

-- =====================================================
-- 6. INDEXES FOR PERFORMANCE
-- =====================================================

-- Indexes cho UserCurrencies
CREATE INDEX IF NOT EXISTS "idx_user_currencies_user_id" ON "UserCurrencies"("user_id");
CREATE INDEX IF NOT EXISTS "idx_user_currencies_currency_id" ON "UserCurrencies"("currency_id");
CREATE INDEX IF NOT EXISTS "idx_user_currencies_balance" ON "UserCurrencies"("balance");

-- Indexes cho CurrencyTransactions
CREATE INDEX IF NOT EXISTS "idx_currency_transactions_user_id" ON "CurrencyTransactions"("user_id");
CREATE INDEX IF NOT EXISTS "idx_currency_transactions_currency_id" ON "CurrencyTransactions"("currency_id");
CREATE INDEX IF NOT EXISTS "idx_currency_transactions_type" ON "CurrencyTransactions"("transaction_type");
CREATE INDEX IF NOT EXISTS "idx_currency_transactions_source" ON "CurrencyTransactions"("source_type", "source_id");
CREATE INDEX IF NOT EXISTS "idx_currency_transactions_created_at" ON "CurrencyTransactions"("created_at");

-- Indexes cho CurrencyEarningRules
CREATE INDEX IF NOT EXISTS "idx_currency_earning_rules_currency_id" ON "CurrencyEarningRules"("currency_id");
CREATE INDEX IF NOT EXISTS "idx_currency_earning_rules_source_type" ON "CurrencyEarningRules"("source_type");
CREATE INDEX IF NOT EXISTS "idx_currency_earning_rules_active" ON "CurrencyEarningRules"("is_active");

-- Indexes cho ItemDecomposition
CREATE INDEX IF NOT EXISTS "idx_item_decomposition_item" ON "ItemDecomposition"("item_type", "item_id");
CREATE INDEX IF NOT EXISTS "idx_item_decomposition_rarity" ON "ItemDecomposition"("rarity");

-- =====================================================
-- 7. INSERT INITIAL DATA
-- =====================================================

-- Thêm các loại tiền tệ cơ bản
INSERT INTO "Currencies" ("currency_code", "currency_name", "description", "icon_path", "is_premium", "exchange_rate", "max_daily_earn") VALUES
('SYNC', 'SynCoin', 'Tiền tệ cơ bản trong Synlearnia, dễ kiếm và dùng cho các giao dịch hàng ngày', 'icons/coin.png', FALSE, 1.0000, 1000),
('KRIS', 'Kristal', 'Tiền tệ cao cấp, khó kiếm hơn, dùng để mua các vật phẩm giá trị', 'icons/gem.png', TRUE, 10.0000, 100)
ON CONFLICT ("currency_code") DO NOTHING;

-- Thêm quy tắc kiếm SynCoin
INSERT INTO "CurrencyEarningRules" ("currency_id", "source_type", "base_amount", "bonus_conditions", "daily_limit", "description") VALUES
-- SynCoin earning rules
((SELECT currency_id FROM "Currencies" WHERE currency_code = 'SYNC'), 'QUIZ_COMPLETION', 10, '{"correct_answer_bonus": 2, "perfect_score_bonus": 20, "speed_bonus": 5}', 500, 'Kiếm SynCoin từ hoàn thành quiz'),
((SELECT currency_id FROM "Currencies" WHERE currency_code = 'SYNC'), 'DAILY_LOGIN', 50, '{"consecutive_days": {"3": 10, "7": 25, "30": 50}}', 50, 'Thưởng đăng nhập hàng ngày'),
((SELECT currency_id FROM "Currencies" WHERE currency_code = 'SYNC'), 'ACHIEVEMENT_UNLOCK', 25, '{"rarity_multiplier": {"COMMON": 1, "RARE": 2, "EPIC": 3, "LEGENDARY": 5}}', NULL, 'Thưởng mở khóa thành tích'),
((SELECT currency_id FROM "Currencies" WHERE currency_code = 'SYNC'), 'LEVEL_UP', 100, '{"level_multiplier": 2}', NULL, 'Thưởng lên cấp'),

-- Kristal earning rules
((SELECT currency_id FROM "Currencies" WHERE currency_code = 'KRIS'), 'ITEM_DECOMPOSE', 0, '{"base_by_rarity": {"COMMON": 5, "UNCOMMON": 15, "RARE": 50, "EPIC": 150, "LEGENDARY": 500, "MYTHIC": 1000}}', NULL, 'Phân giải vật phẩm trùng lặp'),
((SELECT currency_id FROM "Currencies" WHERE currency_code = 'KRIS'), 'TITLE_UNLOCK', 10, '{"tier_multiplier": {"BRONZE": 1, "SILVER": 2, "GOLD": 3, "PLATINUM": 5, "MASTER": 10}}', NULL, 'Thưởng mở khóa danh hiệu'),
((SELECT currency_id FROM "Currencies" WHERE currency_code = 'KRIS'), 'LEADERBOARD_REWARD', 50, '{"position_bonus": {"1": 100, "2": 75, "3": 50, "top10": 25}}', NULL, 'Thưởng bảng xếp hạng'),
((SELECT currency_id FROM "Currencies" WHERE currency_code = 'KRIS'), 'PERFECT_QUIZ_STREAK', 5, '{"streak_multiplier": 2}', 50, 'Thưởng chuỗi quiz hoàn hảo')
ON CONFLICT DO NOTHING;

-- Thêm giá trị phân giải vật phẩm
INSERT INTO "ItemDecomposition" ("item_type", "item_id", "rarity", "kristal_value") VALUES
-- Avatar decomposition values
('AVATAR', 1, 'COMMON', 5),
('AVATAR', 2, 'COMMON', 5),
('AVATAR', 3, 'UNCOMMON', 15),
('AVATAR', 4, 'RARE', 50),
('AVATAR', 5, 'EPIC', 150),
('AVATAR', 6, 'LEGENDARY', 500),

-- Emoji decomposition values
('EMOJI', 1, 'COMMON', 3),
('EMOJI', 2, 'COMMON', 3),
('EMOJI', 3, 'UNCOMMON', 10),
('EMOJI', 4, 'RARE', 30),
('EMOJI', 5, 'EPIC', 100),

-- Frame decomposition values
('FRAME', 1, 'RARE', 75),
('FRAME', 2, 'EPIC', 200),
('FRAME', 3, 'LEGENDARY', 750),

-- Badge decomposition values (special items)
('BADGE', 1, 'EPIC', 100),
('BADGE', 2, 'LEGENDARY', 300)
ON CONFLICT DO NOTHING;

-- =====================================================
-- 8. VIEWS FOR ANALYTICS
-- =====================================================

-- View tổng hợp thống kê tiền tệ user
CREATE OR REPLACE VIEW "UserCurrencyStats" AS
SELECT 
    u.user_id,
    u.name as user_name,
    u.current_level,
    uc_sync.balance as syncoin_balance,
    uc_sync.total_earned as syncoin_total_earned,
    uc_sync.total_spent as syncoin_total_spent,
    uc_kris.balance as kristal_balance,
    uc_kris.total_earned as kristal_total_earned,
    uc_kris.total_spent as kristal_total_spent,
    (uc_sync.balance + (uc_kris.balance * 10)) as total_wealth_in_syncoin
FROM "Users" u
LEFT JOIN "UserCurrencies" uc_sync ON u.user_id = uc_sync.user_id 
    AND uc_sync.currency_id = (SELECT currency_id FROM "Currencies" WHERE currency_code = 'SYNC')
LEFT JOIN "UserCurrencies" uc_kris ON u.user_id = uc_kris.user_id 
    AND uc_kris.currency_id = (SELECT currency_id FROM "Currencies" WHERE currency_code = 'KRIS');

-- View thống kê giao dịch theo ngày
CREATE OR REPLACE VIEW "DailyCurrencyStats" AS
SELECT 
    DATE(created_at) as transaction_date,
    c.currency_code,
    c.currency_name,
    transaction_type,
    COUNT(*) as transaction_count,
    SUM(amount) as total_amount,
    AVG(amount) as average_amount
FROM "CurrencyTransactions" ct
JOIN "Currencies" c ON ct.currency_id = c.currency_id
GROUP BY DATE(created_at), c.currency_code, c.currency_name, transaction_type
ORDER BY transaction_date DESC, c.currency_code, transaction_type;

-- View leaderboard wealth
CREATE OR REPLACE VIEW "WealthLeaderboard" AS
SELECT 
    u.user_id,
    u.name,
    u.current_level,
    uc_sync.balance as syncoin,
    uc_kris.balance as kristal,
    (uc_sync.balance + (uc_kris.balance * 10)) as total_wealth,
    RANK() OVER (ORDER BY (uc_sync.balance + (uc_kris.balance * 10)) DESC) as wealth_rank
FROM "Users" u
LEFT JOIN "UserCurrencies" uc_sync ON u.user_id = uc_sync.user_id 
    AND uc_sync.currency_id = (SELECT currency_id FROM "Currencies" WHERE currency_code = 'SYNC')
LEFT JOIN "UserCurrencies" uc_kris ON u.user_id = uc_kris.user_id 
    AND uc_kris.currency_id = (SELECT currency_id FROM "Currencies" WHERE currency_code = 'KRIS')
WHERE u.role_id = (SELECT role_id FROM "Roles" WHERE role_name = 'student')
ORDER BY total_wealth DESC;

-- Commit transaction
COMMIT;

-- =====================================================
-- 9. COMPLETION MESSAGE
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '✅ CURRENCY SYSTEM SETUP COMPLETED SUCCESSFULLY!';
    RAISE NOTICE '📊 Created tables: Currencies, UserCurrencies, CurrencyTransactions, CurrencyEarningRules, ItemDecomposition';
    RAISE NOTICE '💰 Initialized currencies: SynCoin (SYNC) and Kristal (KRIS)';
    RAISE NOTICE '📋 Added earning rules for both currencies';
    RAISE NOTICE '🔄 Added item decomposition values';
    RAISE NOTICE '📈 Created analytics views: UserCurrencyStats, DailyCurrencyStats, WealthLeaderboard';
    RAISE NOTICE '🚀 Ready to implement currency services and APIs!';
END $$;
