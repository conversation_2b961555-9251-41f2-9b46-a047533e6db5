-- =====================================================
-- SCRIPT BỎ GIỚI HẠN TIỀN TỆ THEO NGÀY
-- =====================================================
-- Chạy script này để loại bỏ hoàn toàn cơ chế giới hạn tiền theo ngày

-- 1. Bỏ giới hạn cấp Currency (toàn bộ loại tiền)
UPDATE "Currencies" 
SET "max_daily_earn" = NULL 
WHERE "max_daily_earn" IS NOT NULL;

-- 2. Bỏ giới hạn cấp Rule (từng hoạt động)
UPDATE "CurrencyEarningRules" 
SET "daily_limit" = NULL 
WHERE "daily_limit" IS NOT NULL;

-- 3. Reset daily earned counter cho tất cả users (optional)
UPDATE "UserCurrencies" 
SET "daily_earned_today" = 0;

-- 4. Verify changes
SELECT 
    'Currencies' as table_name,
    currency_code,
    currency_name,
    max_daily_earn
FROM "Currencies"
UNION ALL
SELECT 
    'CurrencyEarningRules' as table_name,
    source_type as currency_code,
    description as currency_name,
    daily_limit::text as max_daily_earn
FROM "CurrencyEarningRules";

-- Expected result: All max_daily_earn and daily_limit should be NULL

-- =====================================================
-- THÔNG TIN VỀ CÁC THAY ĐỔI ĐÃ THỰC HIỆN
-- =====================================================

-- 1. DATABASE CHANGES:
--    - Currencies.max_daily_earn = NULL (was: SYNC=1000, KRIS=100)
--    - CurrencyEarningRules.daily_limit = NULL (was: QUIZ=500, LOGIN=50)
--    - UserCurrencies.daily_earned_today reset to 0

-- 2. BACKEND CODE CHANGES:
--    - currencyService.js: Removed daily limit checks
--    - userCurrency.js: getDailyEarningProgress() always returns no limits
--    - currency.js: isDailyLimitReached() always returns false
--    - currencyEarningRule.js: hasDailyLimit() always returns false

-- 3. IMPACT:
--    - Users can now earn unlimited currency per day
--    - No more "daily limit reached" errors
--    - All earning activities work without restrictions
--    - Quiz completion, daily login, achievements give full rewards

-- 4. ROLLBACK (if needed):
-- UPDATE "Currencies" SET "max_daily_earn" = CASE
--     WHEN currency_code = 'SYNC' THEN 1000
--     WHEN currency_code = 'KRIS' THEN 100
--     ELSE NULL END;
--
-- UPDATE "CurrencyEarningRules" SET "daily_limit" = CASE
--     WHEN source_type = 'QUIZ_COMPLETION' THEN 500
--     WHEN source_type = 'DAILY_LOGIN' THEN 50
--     ELSE NULL END;
