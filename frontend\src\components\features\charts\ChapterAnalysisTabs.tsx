"use client";

import React, { useState, useMemo, useCallback } from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardHeader,
  CardTitle,
} from "@/components/ui/layout";
// Removed Tabs import - no longer using tabs
import { Badge } from "@/components/ui/feedback";
import {
  BookOpen,
  Target,
  BarChart3,
  ChevronDown,
  ChevronUp,
  TrendingUp,
  TrendingDown,
  CheckCircle2,
  AlertTriangle,
} from "lucide-react";
import { ChapterAnalysisData } from "@/lib/types/chapter-analytics";

interface ChapterAnalysisTabsProps {
  analysisData: ChapterAnalysisData;
  className?: string;
}

const ChapterAnalysisTabs = React.memo(function ChapterAnalysisTabs({
  analysisData,
  className = "",
}: ChapterAnalysisTabsProps) {
  const [expandedChapters, setExpandedChapters] = useState<Set<number>>(
    new Set()
  );

  // Memoize chapter data processing
  const chapterData = useMemo(() => {
    if (!analysisData.chapter_analysis) return [];

    const allChapters = [
      ...(analysisData.chapter_analysis.strengths || []),
      ...(analysisData.chapter_analysis.weaknesses || []),
    ];

    // Deduplicate by chapter_id and sort by accuracy
    const uniqueChapters = allChapters
      .filter(
        (chapter, index, self) =>
          index === self.findIndex((c) => c.chapter_id === chapter.chapter_id)
      )
      .sort((a, b) => b.accuracy_percentage - a.accuracy_percentage);

    return uniqueChapters;
  }, [analysisData.chapter_analysis]);

  const toggleChapterExpansion = useCallback((chapterId: number) => {
    setExpandedChapters((prev) => {
      const newExpanded = new Set(prev);
      if (newExpanded.has(chapterId)) {
        newExpanded.delete(chapterId);
      } else {
        newExpanded.add(chapterId);
      }
      return newExpanded;
    });
  }, []);

  const getPerformanceColor = useCallback((accuracy: number) => {
    if (accuracy >= 80) return "text-green-600 bg-green-50 border-green-200";
    if (accuracy >= 65) return "text-blue-600 bg-blue-50 border-blue-200";
    if (accuracy >= 50) return "text-yellow-600 bg-yellow-50 border-yellow-200";
    return "text-red-600 bg-red-50 border-red-200";
  }, []);

  const getPerformanceIcon = useCallback((accuracy: number) => {
    if (accuracy >= 80) return <TrendingUp className="h-4 w-4" />;
    if (accuracy >= 65) return <CheckCircle2 className="h-4 w-4" />;
    if (accuracy >= 50) return <Target className="h-4 w-4" />;
    return <TrendingDown className="h-4 w-4" />;
  }, []);

  const getPerformanceLabel = useCallback((accuracy: number) => {
    if (accuracy >= 80) return "Xuất sắc";
    if (accuracy >= 65) return "Khá";
    if (accuracy >= 50) return "Trung bình";
    return "Cần cải thiện";
  }, []);

  const formatTimeFromSeconds = useCallback((seconds: number): string => {
    if (seconds < 60) {
      return `${seconds}s`;
    } else if (seconds < 3600) {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;
      return remainingSeconds > 0
        ? `${minutes}m ${remainingSeconds}s`
        : `${minutes}m`;
    } else {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      return minutes > 0 ? `${hours}h ${minutes}m` : `${hours}h`;
    }
  }, []);

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="text-xl font-bold flex items-center gap-2">
          <BarChart3 className="h-6 w-6 text-primary" />
          Phân tích chi tiết
        </CardTitle>
        <p className="text-sm text-muted-foreground">
          Xem chi tiết hiệu suất theo từng khía cạnh
        </p>
      </CardHeader>

      <CardContent>
        <div className="w-full">
          {/* Removed tabs - only show chapter analysis */}
          <div className="mb-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <BookOpen className="h-5 w-5 text-primary" />
              Phân tích theo chương
            </h3>
            <p className="text-sm text-muted-foreground mt-1">
              Xem chi tiết hiệu suất của bạn theo từng chương học
            </p>
          </div>

          {/* Phân tích theo Chương */}
          <div className="space-y-4">
            <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
              <h3 className="font-semibold text-blue-900 mb-2">Tóm tắt</h3>
              <p className="text-sm text-blue-800">
                Bạn đã thể hiện tốt ở{" "}
                <span className="font-bold">{chapterData.length}</span> chương
                được kiểm tra.
                {analysisData.chapter_analysis?.summary && (
                  <>
                    {" "}
                    Trong đó có{" "}
                    <span className="font-bold text-green-600">
                      {
                        analysisData.chapter_analysis.summary
                          .strong_chapters_count
                      }
                    </span>{" "}
                    chương mạnh và{" "}
                    <span className="font-bold text-red-600">
                      {
                        analysisData.chapter_analysis.summary
                          .weak_chapters_count
                      }
                    </span>{" "}
                    chương cần cải thiện.
                  </>
                )}
              </p>
            </div>

            {/* Interactive Progress Bar Chapters */}
            <div className="space-y-3">
              <h4 className="font-semibold">Chi tiết theo chương</h4>
              <div className="space-y-3">
                {chapterData.map((chapter) => (
                  <div
                    key={chapter.chapter_id}
                    className="border border-gray-200 rounded-lg overflow-hidden"
                  >
                    {/* Interactive Progress Bar Header */}
                    <div
                      className="relative cursor-pointer hover:bg-gray-50 transition-colors"
                      onClick={() => toggleChapterExpansion(chapter.chapter_id)}
                    >
                      {/* Progress Bar Background */}
                      <div className="w-full bg-gray-200 h-16 relative">
                        {/* Progress Fill */}
                        <div
                          className={`h-full transition-all duration-500 ${
                            chapter.accuracy_percentage >= 80
                              ? "bg-green-500"
                              : chapter.accuracy_percentage >= 65
                              ? "bg-blue-500"
                              : chapter.accuracy_percentage >= 50
                              ? "bg-yellow-500"
                              : "bg-red-500"
                          }`}
                          style={{ width: `${chapter.accuracy_percentage}%` }}
                        />

                        {/* Content Overlay */}
                        <div className="absolute inset-0 flex items-center justify-between px-4">
                          <div className="flex items-center gap-3">
                            <div className="flex items-center gap-2">
                              {chapter.accuracy_percentage >= 70 ? (
                                <CheckCircle2
                                  className="h-5 w-5 text-white"
                                  style={{
                                    filter:
                                      "drop-shadow(1px 1px 2px rgba(0,0,0,0.5))",
                                  }}
                                />
                              ) : (
                                <AlertTriangle
                                  className="h-5 w-5 text-white"
                                  style={{
                                    filter:
                                      "drop-shadow(1px 1px 2px rgba(0,0,0,0.5))",
                                  }}
                                />
                              )}
                              <h4
                                className="font-semibold text-white"
                                style={{
                                  textShadow: "1px 1px 2px rgba(0,0,0,0.5)",
                                }}
                              >
                                {chapter.chapter_name}
                              </h4>
                            </div>
                            <Badge
                              variant="secondary"
                              className="bg-white/90 text-gray-800 text-xs"
                            >
                              {getPerformanceLabel(chapter.accuracy_percentage)}
                            </Badge>
                          </div>
                          <div className="flex items-center gap-2">
                            <span
                              className="text-sm font-bold text-white"
                              style={{
                                textShadow: "1px 1px 2px rgba(0,0,0,0.5)",
                              }}
                            >
                              {chapter.accuracy_percentage.toFixed(1)}%
                            </span>
                            {expandedChapters.has(chapter.chapter_id) ? (
                              <ChevronUp
                                className="h-4 w-4 text-white"
                                style={{
                                  filter:
                                    "drop-shadow(1px 1px 2px rgba(0,0,0,0.5))",
                                }}
                              />
                            ) : (
                              <ChevronDown
                                className="h-4 w-4 text-white"
                                style={{
                                  filter:
                                    "drop-shadow(1px 1px 2px rgba(0,0,0,0.5))",
                                }}
                              />
                            )}
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Expanded Content */}
                    {expandedChapters.has(chapter.chapter_id) && (
                      <div className="px-4 pb-4 bg-white border-t">
                        <div className="space-y-4">
                          {/* Learning Outcomes */}
                          {chapter.related_los &&
                            chapter.related_los.length > 0 && (
                              <div>
                                <h6 className="font-medium text-gray-900 mb-2">
                                  Learning Outcomes liên quan:
                                </h6>
                                <div className="flex flex-wrap gap-2">
                                  {chapter.related_los.map((lo, index) => (
                                    <Badge
                                      key={index}
                                      variant="outline"
                                      className="text-xs"
                                    >
                                      {lo}
                                    </Badge>
                                  ))}
                                </div>
                              </div>
                            )}

                          {/* Sections */}
                          {chapter.sections && chapter.sections.length > 0 && (
                            <div>
                              <h6 className="font-medium text-gray-900 mb-2">
                                Sections trong chương:
                              </h6>
                              <div className="space-y-2">
                                {chapter.sections.map((section) => (
                                  <div
                                    key={section.section_id}
                                    className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border border-gray-200"
                                  >
                                    <div className="flex-1">
                                      <div className="font-medium text-sm text-gray-900">
                                        {section.title}
                                      </div>
                                      {section.content && (
                                        <div className="text-xs text-gray-600 mt-1">
                                          {section.content.substring(0, 100)}
                                          {section.content.length > 100 &&
                                            "..."}
                                        </div>
                                      )}
                                    </div>
                                    <div className="flex items-center gap-2 ml-3">
                                      {section.order && (
                                        <Badge
                                          variant="outline"
                                          className="text-xs"
                                        >
                                          #{section.order}
                                        </Badge>
                                      )}
                                      {section.content_type && (
                                        <Badge
                                          variant="secondary"
                                          className="text-xs"
                                        >
                                          {section.content_type === "text"
                                            ? "Lý thuyết"
                                            : section.content_type === "video"
                                            ? "Video"
                                            : "Bài tập"}
                                        </Badge>
                                      )}
                                    </div>
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}

                          {/* Performance Stats */}
                          <div className="grid grid-cols-2 gap-4 text-sm">
                            <div>
                              <span className="text-gray-600">Câu đúng:</span>
                              <span className="font-medium ml-2">
                                {chapter.correct_answers}/
                                {chapter.total_questions}
                              </span>
                            </div>
                            <div>
                              <span className="text-gray-600">
                                Thời gian TB:
                              </span>
                              <span className="font-medium ml-2">
                                {formatTimeFromSeconds(
                                  Math.round(chapter.average_time_per_question)
                                )}
                                /câu
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
});

export default ChapterAnalysisTabs;
