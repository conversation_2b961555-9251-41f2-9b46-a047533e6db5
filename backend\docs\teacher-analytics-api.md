# Teacher Analytics API Documentation

## Tổng quan
Bộ API Teacher Analytics cung cấp các công cụ phân tích chuyên sâu cho giảng viên để đánh giá hiệu quả quiz và hiểu rõ tình hình học tập của học sinh.

## 🆕 Cập nhật mới (Latest Updates)

### ✨ NEW CHART ENDPOINTS - Biểu đồ tương tác
- **Student Groups Chart**: Bi<PERSON>u đồ cột nhóm học sinh với click để xem chi tiết
- **Learning Outcomes Chart**: <PERSON><PERSON><PERSON><PERSON> đồ cột LO với click để xem chi tiết
- **Student Group Detail**: Chi tiết nhóm học sinh khi click vào cột
- **Learning Outcome Detail**: Chi tiết LO khi click vào cột
- **Student LO Analysis**: Phân tích điểm mạnh/yếu theo LO của từng học sinh

### 🎯 Tính năng chính
- **Interactive Charts**: Click vào cột để xem chi tiết
- **Color Coding**: <PERSON><PERSON><PERSON> sắc theo performance level
- **Chart.js Ready**: Dữ liệu sẵn sàng cho Chart.js/React Charts
- **Mobile Responsive**: Hỗ trợ responsive design
- **LO Description**: Bao gồm mô tả chi tiết của Learning Outcomes

## Base URL
```
/api/teacher-analytics
```

## Authentication
Tất cả API yêu cầu authentication token và chỉ dành cho role `admin` và `teacher`.

## API Endpoints

## 📊 NEW CHART ENDPOINTS (Biểu đồ tương tác)

### 1. Student Groups Chart Data
**GET** `/quiz/:quizId/student-groups`

Lấy dữ liệu để vẽ biểu đồ cột nhóm học sinh với khả năng click để xem chi tiết.

**Response:**
```json
{
  "success": true,
  "data": {
    "chart_data": [
      {
        "group_name": "excellent",
        "display_name": "Xuất sắc",
        "student_count": 12,
        "percentage": 26.7,
        "score_range": {
          "min": 85.5,
          "max": 98.2,
          "average": 91.8
        },
        "color": "#4CAF50"
      },
      {
        "group_name": "good",
        "display_name": "Khá",
        "student_count": 18,
        "percentage": 40.0,
        "score_range": {
          "min": 70.0,
          "max": 84.9,
          "average": 77.5
        },
        "color": "#2196F3"
      },
      {
        "group_name": "average",
        "display_name": "Trung bình",
        "student_count": 10,
        "percentage": 22.2,
        "score_range": {
          "min": 50.0,
          "max": 69.9,
          "average": 59.8
        },
        "color": "#FF9800"
      },
      {
        "group_name": "weak",
        "display_name": "Yếu",
        "student_count": 5,
        "percentage": 11.1,
        "score_range": {
          "min": 20.0,
          "max": 49.9,
          "average": 35.2
        },
        "color": "#F44336"
      }
    ],
    "total_students": 45,
    "chart_config": {
      "x_axis": "group_name",
      "y_axis": "student_count",
      "tooltip_fields": ["percentage", "score_range"],
      "clickable": true
    }
  }
}
```

**Frontend Integration:**
```javascript
// Chart.js example
const chartData = {
  labels: response.data.chart_data.map(item => item.display_name),
  datasets: [{
    label: 'Số học sinh',
    data: response.data.chart_data.map(item => item.student_count),
    backgroundColor: response.data.chart_data.map(item => item.color),
    borderColor: response.data.chart_data.map(item => item.color),
    borderWidth: 1
  }]
};

// Click handler
const handleChartClick = (event, elements) => {
  if (elements.length > 0) {
    const index = elements[0].index;
    const groupName = response.data.chart_data[index].group_name;
    // Navigate to detail: /quiz/122/student-groups/excellent
    fetchStudentGroupDetail(quizId, groupName);
  }
};
```

### 2. Student Group Detail (Khi click vào cột)
**GET** `/quiz/:quizId/student-groups/:groupName`

Lấy chi tiết nhóm học sinh khi click vào cột biểu đồ.

**Parameters:**
- `groupName`: `excellent` | `good` | `average` | `weak`

**Response:**
```json
{
  "success": true,
  "data": {
    "group_info": {
      "group_name": "excellent",
      "display_name": "Xuất sắc",
      "student_count": 12,
      "average_score": 91.8,
      "average_percentage": 91.8,
      "threshold": 85
    },
    "students": [
      {
        "user_id": 120,
        "name": "Nguyễn Văn A",
        "email": "<EMAIL>",
        "score": 95.5,
        "percentage_score": 95.5,
        "completion_time": 1800,
        "average_time_per_question": 90,
        "total_questions_attempted": 20,
        "correct_answers": 19
      }
    ],
    "insights": [
      "Nhóm xuất sắc với 12 học sinh",
      "Điểm trung bình: 91.8/10 (91.8%)"
    ],
    "recommendations": [
      {
        "type": "enrichment",
        "suggestion": "Tạo thêm bài tập nâng cao cho nhóm này",
        "priority": "medium"
      }
    ]
  }
}
```

### 3. Learning Outcomes Chart Data
**GET** `/quiz/:quizId/learning-outcomes`

Lấy dữ liệu để vẽ biểu đồ cột Learning Outcomes với khả năng click để xem chi tiết.

**Response:**
```json
{
  "success": true,
  "data": {
    "chart_data": [
      {
        "lo_id": 1,
        "lo_name": "LO1",
        "short_name": "LO1",
        "accuracy": 85.5,
        "performance_level": "excellent",
        "total_questions": 5,
        "correct_answers": 42,
        "total_attempts": 50,
        "color": "#4CAF50"
      },
      {
        "lo_id": 2,
        "lo_name": "LO2",
        "short_name": "LO2",
        "accuracy": 65.2,
        "performance_level": "average",
        "total_questions": 3,
        "correct_answers": 28,
        "total_attempts": 45,
        "color": "#FF9800"
      }
    ],
    "chart_config": {
      "x_axis": "lo_name",
      "y_axis": "accuracy",
      "tooltip_fields": ["total_questions", "correct_answers", "total_attempts"],
      "clickable": true,
      "performance_thresholds": {
        "excellent": 85,
        "good": 70,
        "average": 50,
        "weak": 0
      }
    },
    "summary": {
      "total_los": 5,
      "average_accuracy": 75.8,
      "strongest_lo": {
        "lo_id": 1,
        "lo_name": "LO1",
        "accuracy": 85.5
      },
      "weakest_lo": {
        "lo_id": 3,
        "lo_name": "LO3",
        "accuracy": 45.2
      }
    }
  }
}
```

**📊 Giải thích các field trong Learning Outcomes Chart:**

- **`total_questions`**: Số câu hỏi thuộc LO này trong quiz (ví dụ: 5 câu)
- **`total_attempts`**: Tổng số lượt trả lời **ĐẦU TIÊN** của TẤT CẢ học sinh (ví dụ: 10 học sinh × 5 câu = 50 lượt)
- **`correct_answers`**: Tổng số lượt trả lời ĐÚNG **ĐẦU TIÊN** của tất cả học sinh (ví dụ: 42/50 lượt đúng)
- **`accuracy`**: Tỷ lệ đúng = (correct_answers / total_attempts) × 100 (ví dụ: 42/50 = 84%)

**🎯 Logic đánh giá:**
- **Chỉ tính lần đầu tiên** học sinh làm mỗi câu hỏi (bỏ qua retry)
- **Mục đích**: Đánh giá năng lực thực sự của học sinh, không bị ảnh hưởng bởi việc thử lại
- **Kết quả**: `correct_answers` ≤ `total_attempts` ≤ (số học sinh × `total_questions`)

**💡 Ví dụ với 10 học sinh, LO có 5 câu:**
- `total_questions = 5` (câu hỏi trong LO)
- `total_attempts = 50` (10 học sinh × 5 câu, chỉ tính lần đầu)
- `correct_answers = 42` (42/50 lượt đúng lần đầu)
- `accuracy = 84%`

### 4. Learning Outcome Detail (Khi click vào cột)
**GET** `/quiz/:quizId/learning-outcomes/:loId`

Lấy chi tiết Learning Outcome khi click vào cột biểu đồ.

**Response:**
```json
{
  "success": true,
  "data": {
    "lo_info": {
      "lo_id": 1,
      "lo_name": "LO1",
      "description": "Hiểu được các khái niệm cơ bản",
      "total_questions": 5,
      "accuracy": 85.5,
      "performance_level": "excellent"
    },
    "question_breakdown": [
      {
        "question_id": 1,
        "question_text": "Câu hỏi về khái niệm A...",
        "difficulty": "Dễ",
        "correct_count": 8,
        "total_attempts": 10,
        "accuracy": 80.0,
        "insights": ["Câu hỏi trung bình - cần ôn tập thêm"]
      }
    ],
    "student_performance": [
      {
        "performance_level": "excellent",
        "student_count": 8,
        "students": [
          {
            "user_id": 120,
            "name": "Nguyễn Văn A",
            "correct_count": 5,
            "total_count": 5,
            "accuracy": 100.0
          }
        ]
      }
    ],
    "insights": [
      "LO tốt - duy trì phương pháp hiện tại"
    ],
    "recommendations": [
      {
        "type": "content_review",
        "suggestion": "Xem xét lại nội dung câu hỏi khó",
        "priority": "medium"
      }
    ]
  }
}
```

### 5. Student LO Analysis (Chi tiết học sinh)
**GET** `/quiz/:quizId/student/:userId/lo-analysis`

Lấy phân tích chi tiết Learning Outcomes của từng học sinh - điểm mạnh/yếu theo LO.

**Response:**
```json
{
  "success": true,
  "data": {
    "student_info": {
      "user_id": 120,
      "name": "Nguyễn Văn A",
      "email": "<EMAIL>",
      "quiz_score": 8.5,
      "completion_time": 1800,
      "overall_percentage": 85.0
    },
    "quiz_info": {
      "quiz_id": 122,
      "name": "Bài tập Thiết kế web",
      "total_questions": 20
    },
    "lo_analysis": [
      {
        "lo_id": 1,
        "lo_name": "LO1",
        "lo_description": "Hiểu được các thẻ HTML cơ bản và cách sử dụng",
        "total_questions": 5,
        "attempted_questions": 5,
        "correct_answers": 5,
        "achievement_percentage": 100.0,
        "performance_level": "excellent",
        "status": "Xuất sắc",
        "color": "#4CAF50",
        "average_time_seconds": 120.5,
        "total_time_seconds": 602,
        "insights": [
          "Nắm vững hoàn toàn LO này (100.0%)"
        ],
        "recommendations": [
          "Có thể làm mentor cho bạn khác về LO này"
        ],
        "question_details": [
          {
            "question_id": 1,
            "question_text": "Thẻ HTML nào dùng để tạo tiêu đề?",
            "difficulty": "Dễ",
            "is_correct": true,
            "time_spent": 95,
            "attempted": true
          }
        ]
      },
      {
        "lo_id": 2,
        "lo_name": "LO2",
        "lo_description": "Áp dụng CSS để tạo layout và styling",
        "total_questions": 3,
        "attempted_questions": 3,
        "correct_answers": 1,
        "achievement_percentage": 33.3,
        "performance_level": "weak",
        "status": "Yếu",
        "color": "#F44336",
        "average_time_seconds": 180.2,
        "insights": [
          "Chưa nắm được LO này (33.3%)",
          "Thời gian suy nghĩ lâu - có thể chưa tự tin"
        ],
        "recommendations": [
          "Cần học lại từ đầu và được hỗ trợ thêm",
          "Luyện tập thêm để tăng tốc độ giải quyết"
        ]
      }
    ],
    "summary": {
      "total_los": 5,
      "strengths_count": 3,
      "weaknesses_count": 1,
      "needs_improvement_count": 1,
      "strongest_lo": {
        "lo_name": "LO1",
        "percentage": 100.0
      },
      "weakest_lo": {
        "lo_name": "LO2",
        "percentage": 33.3
      }
    },
    "insights": [
      "Điểm mạnh: 3 LO đạt từ 70% trở lên",
      "Điểm yếu: 1 LO dưới 50%",
      "Cần cải thiện: 1 LO từ 50-70%"
    ],
    "recommendations": [
      {
        "type": "focus",
        "suggestion": "Tập trung cải thiện LO2",
        "priority": "high"
      }
    ]
  }
}
```

## 🎨 Frontend Integration Guide

### Chart.js Integration Example

```javascript
// 1. Student Groups Chart
const createStudentGroupsChart = (data) => {
  const ctx = document.getElementById('studentGroupsChart').getContext('2d');

  return new Chart(ctx, {
    type: 'bar',
    data: {
      labels: data.chart_data.map(item => item.display_name),
      datasets: [{
        label: 'Số học sinh',
        data: data.chart_data.map(item => item.student_count),
        backgroundColor: data.chart_data.map(item => item.color),
        borderColor: data.chart_data.map(item => item.color),
        borderWidth: 1
      }]
    },
    options: {
      responsive: true,
      onClick: (event, elements) => {
        if (elements.length > 0) {
          const index = elements[0].index;
          const groupName = data.chart_data[index].group_name;
          showStudentGroupDetail(quizId, groupName);
        }
      },
      plugins: {
        tooltip: {
          callbacks: {
            afterLabel: (context) => {
              const item = data.chart_data[context.dataIndex];
              return [
                `Tỷ lệ: ${item.percentage}%`,
                `Điểm TB: ${item.score_range.average}`,
                `Khoảng: ${item.score_range.min} - ${item.score_range.max}`
              ];
            }
          }
        }
      }
    }
  });
};

// 2. Learning Outcomes Chart
const createLearningOutcomesChart = (data) => {
  const ctx = document.getElementById('learningOutcomesChart').getContext('2d');

  return new Chart(ctx, {
    type: 'bar',
    data: {
      labels: data.chart_data.map(item => item.lo_name),
      datasets: [{
        label: 'Độ chính xác (%)',
        data: data.chart_data.map(item => item.accuracy),
        backgroundColor: data.chart_data.map(item => item.color),
        borderColor: data.chart_data.map(item => item.color),
        borderWidth: 1
      }]
    },
    options: {
      responsive: true,
      scales: {
        y: {
          beginAtZero: true,
          max: 100,
          ticks: {
            callback: (value) => value + '%'
          }
        }
      },
      onClick: (event, elements) => {
        if (elements.length > 0) {
          const index = elements[0].index;
          const loId = data.chart_data[index].lo_id;
          showLearningOutcomeDetail(quizId, loId);
        }
      }
    }
  });
};

// 3. Student LO Analysis (Radar Chart)
const createStudentLORadarChart = (data) => {
  const ctx = document.getElementById('studentLOChart').getContext('2d');

  return new Chart(ctx, {
    type: 'radar',
    data: {
      labels: data.lo_analysis.map(lo => lo.lo_name),
      datasets: [{
        label: 'Phần trăm đạt được',
        data: data.lo_analysis.map(lo => lo.achievement_percentage),
        backgroundColor: 'rgba(54, 162, 235, 0.2)',
        borderColor: 'rgba(54, 162, 235, 1)',
        borderWidth: 2,
        pointBackgroundColor: data.lo_analysis.map(lo => lo.color),
        pointBorderColor: '#fff',
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: data.lo_analysis.map(lo => lo.color)
      }]
    },
    options: {
      responsive: true,
      scales: {
        r: {
          beginAtZero: true,
          max: 100,
          ticks: {
            callback: (value) => value + '%'
          }
        }
      }
    }
  });
};
```

### React Component Example

```jsx
import React, { useState, useEffect } from 'react';
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend } from 'chart.js';
import { Bar } from 'react-chartjs-2';

ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend);

const StudentGroupsChart = ({ quizId }) => {
  const [chartData, setChartData] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchStudentGroupsData();
  }, [quizId]);

  const fetchStudentGroupsData = async () => {
    try {
      const response = await fetch(`/api/teacher-analytics/quiz/${quizId}/student-groups`);
      const data = await response.json();
      setChartData(data.data);
      setLoading(false);
    } catch (error) {
      console.error('Error fetching student groups data:', error);
      setLoading(false);
    }
  };

  const handleChartClick = (event, elements) => {
    if (elements.length > 0) {
      const index = elements[0].index;
      const groupName = chartData.chart_data[index].group_name;
      // Navigate to detail page or show modal
      window.location.href = `/teacher/quiz/${quizId}/groups/${groupName}`;
    }
  };

  if (loading) return <div>Loading...</div>;
  if (!chartData) return <div>No data available</div>;

  const data = {
    labels: chartData.chart_data.map(item => item.display_name),
    datasets: [{
      label: 'Số học sinh',
      data: chartData.chart_data.map(item => item.student_count),
      backgroundColor: chartData.chart_data.map(item => item.color),
      borderColor: chartData.chart_data.map(item => item.color),
      borderWidth: 1
    }]
  };

  const options = {
    responsive: true,
    onClick: handleChartClick,
    plugins: {
      legend: {
        position: 'top'
      },
      title: {
        display: true,
        text: 'Phân bố nhóm học sinh'
      },
      tooltip: {
        callbacks: {
          afterLabel: (context) => {
            const item = chartData.chart_data[context.dataIndex];
            return [
              `Tỷ lệ: ${item.percentage}%`,
              `Điểm TB: ${item.score_range.average}`,
              `Khoảng: ${item.score_range.min} - ${item.score_range.max}`
            ];
          }
        }
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          stepSize: 1
        }
      }
    }
  };

  return (
    <div className="chart-container">
      <Bar data={data} options={options} />
      <p className="chart-note">
        💡 Click vào cột để xem chi tiết nhóm học sinh
      </p>
    </div>
  );
};

export default StudentGroupsChart;
```

## 📱 Mobile Responsive Considerations

```css
.chart-container {
  position: relative;
  height: 400px;
  width: 100%;
}

@media (max-width: 768px) {
  .chart-container {
    height: 300px;
  }

  /* Adjust chart labels for mobile */
  .chart-container canvas {
    max-height: 300px;
  }
}
```

## 🔄 API Flow Diagram

```
1. Teacher Dashboard
   ↓
2. GET /quiz/:quizId/student-groups (Chart data)
   ↓
3. Render interactive bar chart
   ↓
4. User clicks on "Excellent" column
   ↓
5. GET /quiz/:quizId/student-groups/excellent (Detail)
   ↓
6. Show student list with scores
   ↓
7. User clicks on specific student
   ↓
8. GET /quiz/:quizId/student/:userId/lo-analysis
   ↓
9. Show student's LO strengths/weaknesses
```

---

## 📊 EXISTING ENDPOINTS (Đã có từ trước)

### 8. Comprehensive Quiz Report
**GET** `/quiz/:quizId/comprehensive-report`

Lấy báo cáo tổng quan chi tiết về quiz với phân tích điểm mạnh/yếu và insights.

**Response:**
```json
{
  "success": true,
  "data": {
    "quiz_info": {
      "quiz_id": 1,
      "name": "Quiz Toán học",
      "total_questions": 20
    },
    "overall_performance": {
      "total_participants": 45,
      "average_score": 72.5,
      "completion_rate": 88.9
    },
    "learning_outcome_analysis": [
      {
        "lo_name": "Giải phương trình",
        "accuracy": 65.2,
        "performance_level": "average",
        "insights": ["LO trung bình - cần cải thiện"],
        "recommendations": ["Tăng cường luyện tập"]
      }
    ],
    "student_groups": {
      "excellent": { "count": 12, "percentage": 26.7 },
      "good": { "count": 18, "percentage": 40.0 },
      "average": { "count": 10, "percentage": 22.2 },
      "weak": { "count": 5, "percentage": 11.1 }
    },
    "teacher_insights": [
      {
        "category": "strengths",
        "message": "Học sinh nắm vững LO cơ bản",
        "priority": "maintain"
      }
    ]
  }
}
```

### 7. Student LO Analysis (Chi tiết học sinh theo LO) 🆕
**GET** `/quiz/:quizId/student/:userId/lo-analysis`

Lấy phân tích chi tiết Learning Outcomes của từng học sinh - điểm mạnh/yếu theo LO.

**Parameters:**
- `userId`: ID của học sinh cần phân tích

**Response:** *(Xem phần NEW CHART ENDPOINTS ở trên)*

---

## 📊 EXISTING ENDPOINTS (Đã có từ trước)

### 9. Quiz Comparison
**GET** `/quiz-comparison`

So sánh performance giữa các quiz.

**Query Parameters:**
- `quiz_ids`: Danh sách quiz_id cách nhau bởi dấu phẩy (ví dụ: 1,2,3)
- `subject_id`: So sánh tất cả quiz trong subject

**Response:**
```json
{
  "success": true,
  "data": {
    "quiz_comparisons": [
      {
        "quiz_id": 1,
        "quiz_name": "Quiz 1",
        "average_score": 72.5,
        "completion_rate": 88.9,
        "overall_accuracy": 65.2
      }
    ],
    "comparison_insights": [
      {
        "type": "performance_comparison",
        "message": "Quiz 1 có điểm cao nhất (72.5)"
      }
    ],
    "summary": {
      "best_performing_quiz": {...},
      "worst_performing_quiz": {...}
    }
  }
}
```

### 10. Student Detailed Analysis
**GET** `/quiz/:quizId/student/:userId/detailed-analysis`

Phân tích chi tiết cá nhân học sinh với insights từng câu hỏi.

**Response:**
```json
{
  "success": true,
  "data": {
    "student_info": {
      "user_id": 123,
      "name": "Nguyễn Văn A",
      "email": "<EMAIL>"
    },
    "performance_summary": {
      "score": 65,
      "accuracy": 65.0,
      "total_questions": 20,
      "correct_answers": 13
    },
    "overall_insights": {
      "insights": ["Kết quả trung bình - có thể cải thiện"],
      "recommendations": ["Tăng cường luyện tập"]
    },
    "question_by_question_analysis": [
      {
        "question_id": 1,
        "question_text": "2 + 2 = ?",
        "is_correct": true,
        "time_spent": 45,
        "insights": ["Trả lời nhanh và chính xác"],
        "recommendations": []
      }
    ]
  }
}
```

### 11. Teaching Insights
**GET** `/quiz/:quizId/teaching-insights`

Lấy insights và recommendations tổng hợp cho giảng viên.

**Response:**
```json
{
  "success": true,
  "data": {
    "summary_insights": {
      "overall_assessment": "mixed",
      "key_strengths": ["Điểm trung bình tốt"],
      "main_challenges": ["2 LO cần cải thiện"],
      "immediate_actions_needed": 2
    },
    "detailed_insights": {
      "curriculum_insights": [
        {
          "type": "weakness",
          "message": "2 LO có hiệu suất thấp",
          "impact": "high"
        }
      ],
      "teaching_method_insights": [...],
      "student_insights": [...],
      "action_recommendations": [
        {
          "category": "curriculum_revision",
          "action": "Xem xét điều chỉnh nội dung giảng dạy",
          "priority": "high",
          "timeline": "immediate"
        }
      ]
    }
  }
}
```

### 12. Quiz Benchmark
**GET** `/quiz/:quizId/benchmark`

Lấy benchmark và so sánh với historical data.

**Query Parameters:**
- `compare_with_subject`: So sánh với quiz khác trong subject (default: true)
- `compare_with_teacher`: So sánh với quiz của cùng giảng viên (default: true)

**Response:**
```json
{
  "success": true,
  "data": {
    "current_quiz": {
      "quiz_id": 1,
      "name": "Quiz Toán",
      "metrics": {
        "average_score": 72.5,
        "completion_rate": 88.9,
        "pass_rate": 75.6,
        "excellence_rate": 26.7
      }
    },
    "comparisons": {
      "subject_benchmark": {
        "comparison_base": "8 quiz khác trong subject",
        "subject_average": {
          "average_score": 68.2,
          "completion_rate": 82.1
        },
        "current_vs_average": {
          "score_difference": 4.3,
          "completion_difference": 6.8
        }
      }
    },
    "performance_ranking": {
      "current_rank": 2,
      "total_quizzes": 9,
      "percentile": 88.9,
      "ranking_insights": "Top performer"
    },
    "insights": [
      {
        "type": "positive",
        "category": "performance",
        "message": "Quiz này có điểm cao hơn 4.3 điểm so với trung bình"
      }
    ],
    "recommendations": [
      {
        "category": "improvement",
        "suggestion": "Duy trì phương pháp hiện tại",
        "priority": "medium"
      }
    ]
  }
}
```

## Error Responses

Tất cả API trả về error theo format:
```json
{
  "success": false,
  "error": "Mô tả lỗi",
  "details": "Chi tiết lỗi (nếu có)"
}
```

## Status Codes
- `200`: Success
- `400`: Bad Request (tham số không hợp lệ)
- `401`: Unauthorized (chưa đăng nhập)
- `403`: Forbidden (không có quyền truy cập)
- `404`: Not Found (không tìm thấy resource)
- `500`: Internal Server Error

## Use Cases

### 1. Đánh giá tổng quan quiz
```javascript
// Lấy báo cáo tổng quan
const response = await fetch('/api/teacher-analytics/quiz/123/comprehensive-report');
const data = await response.json();
```

### 2. Vẽ biểu đồ cột nhóm học sinh
```javascript
// Lấy dữ liệu cho biểu đồ cột nhóm học sinh
const response = await fetch('/api/teacher-analytics/quiz/123/student-groups');
const chartData = await response.json();

// Sử dụng với Chart.js hoặc thư viện biểu đồ khác
const chart = new Chart(ctx, {
  type: 'bar',
  data: {
    labels: chartData.data.chart_data.map(item => item.display_name),
    datasets: [{
      data: chartData.data.chart_data.map(item => item.student_count),
      backgroundColor: chartData.data.chart_data.map(item => item.color)
    }]
  },
  options: {
    onClick: (event, elements) => {
      if (elements.length > 0) {
        const index = elements[0].index;
        const groupName = chartData.data.chart_data[index].group_name;
        // Gọi API chi tiết nhóm
        loadGroupDetail(groupName);
      }
    }
  }
});
```

### 3. Chi tiết nhóm học sinh khi click cột
```javascript
// Xem chi tiết nhóm học sinh khi click vào cột biểu đồ
async function loadGroupDetail(groupType) {
  const response = await fetch(`/api/teacher-analytics/quiz/123/student-groups/${groupType}`);
  const groupDetail = await response.json();

  // Hiển thị danh sách sinh viên và kết quả theo LO
  displayStudentList(groupDetail.data.students);
}
```

### 4. Vẽ biểu đồ cột Learning Outcomes
```javascript
// Lấy dữ liệu cho biểu đồ cột LO
const response = await fetch('/api/teacher-analytics/quiz/123/learning-outcomes');
const loData = await response.json();

// Vẽ biểu đồ cột LO
const loChart = new Chart(loCtx, {
  type: 'bar',
  data: {
    labels: loData.data.chart_data.map(item => item.short_name),
    datasets: [{
      label: 'Độ chính xác (%)',
      data: loData.data.chart_data.map(item => item.accuracy),
      backgroundColor: loData.data.chart_data.map(item => item.color)
    }]
  },
  options: {
    onClick: (event, elements) => {
      if (elements.length > 0) {
        const index = elements[0].index;
        const loId = loData.data.chart_data[index].lo_id;
        // Gọi API chi tiết LO
        loadLODetail(loId);
      }
    }
  }
});
```

### 5. Chi tiết Learning Outcome khi click cột
```javascript
// Xem chi tiết LO khi click vào cột biểu đồ
async function loadLODetail(loId) {
  const response = await fetch(`/api/teacher-analytics/quiz/123/learning-outcomes/${loId}`);
  const loDetail = await response.json();

  // Hiển thị phân tích chi tiết LO
  displayLOAnalysis(loDetail.data);
}
```

### 6. So sánh với quiz khác
```javascript
// So sánh với các quiz trong subject
const response = await fetch('/api/teacher-analytics/quiz-comparison?subject_id=5');
const comparison = await response.json();
```

### 7. Lấy insights giảng dạy
```javascript
// Lấy recommendations cho giảng viên
const response = await fetch('/api/teacher-analytics/quiz/123/teaching-insights');
const insights = await response.json();
```
