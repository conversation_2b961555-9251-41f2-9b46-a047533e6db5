# Quiz Racing API Documentation

## Overview
Real-time Quiz Racing system with WebSocket integration, energy mechanics, and skill activation system. This system enables competitive quiz gameplay with 17 different skills across 5 categories.

## Architecture Components

### 1. Backend Services
- **QuizRacingService**: Core racing logic, energy system, skill execution
- **QuizRacingController**: WebSocket event handlers and real-time communication
- **SkillService**: Skill management and execution logic (from Task 4.1)

### 2. Database Integration
- Uses existing Skills system tables from Task 4.1
- Redis caching for real-time session data
- PostgreSQL for persistent skill usage history

### 3. Real-time Communication
- Socket.IO for WebSocket events
- Room-based communication (`quiz:{sessionId}`)
- Personal channels (`quiz:{sessionId}:{userId}`)

---

## REST API Endpoints

### Session Management

#### POST /api/quiz-racing/initialize
Initialize a new quiz racing session.

**Authentication**: Required (Student+)

**Request Body**:
```json
{
  "quiz_id": 1,
  "participants": [
    { "user_id": 1, "username": "student1" },
    { "user_id": 2, "username": "student2" }
  ]
}
```

**Response**:
```json
{
  "success": true,
  "message": "Quiz racing session initialized successfully",
  "data": {
    "session_id": "racing_1_1640995200000",
    "quiz_id": 1,
    "participants": [...],
    "total_questions": 10
  }
}
```

#### GET /api/quiz-racing/session/:sessionId
Get current session data and participant status.

**Authentication**: Required (Student+)

**Response**:
```json
{
  "success": true,
  "data": {
    "quiz_session_id": "racing_1_1640995200000",
    "participants": [
      {
        "user_id": 1,
        "username": "student1",
        "current_score": 250,
        "current_streak": 3,
        "energy_percent": 75,
        "position": 1,
        "skills_used": [1, 3],
        "active_effects": [...]
      }
    ],
    "current_question_index": 5,
    "round_number": 1
  }
}
```

### Skill Loadout Management

#### POST /api/quiz-racing/loadout
Set skill loadout for racing session (4 skills required).

**Authentication**: Required (Student+)

**Request Body**:
```json
{
  "quiz_session_id": "racing_1_1640995200000",
  "skill_ids": [1, 5, 9, 13]
}
```

**Response**:
```json
{
  "success": true,
  "message": "Loadout created successfully",
  "data": {
    "loadout_id": 123,
    "skills": [
      {
        "skill_id": 1,
        "skill_name": "Blackhole",
        "category": "ATTACK",
        "cost_type": "SYNCOIN"
      }
    ]
  }
}
```

#### GET /api/quiz-racing/loadout/:sessionId
Get user's current loadout for specific session.

**Authentication**: Required (Student+)

### Statistics

#### GET /api/quiz-racing/stats/:sessionId
Get comprehensive racing session statistics.

**Authentication**: Required (Student+)

**Response**:
```json
{
  "success": true,
  "data": {
    "session_id": "racing_1_1640995200000",
    "total_participants": 3,
    "current_round": 1,
    "session_duration": 180000,
    "leaderboard": [
      {
        "position": 1,
        "user_id": 1,
        "username": "student1",
        "score": 450,
        "streak": 5,
        "energy": 80,
        "skills_used": 2
      }
    ]
  }
}
```

---

## WebSocket Events

### Client → Server Events

#### join-quiz-racing
Join a racing session room.

**Payload**:
```json
{
  "quiz_session_id": "racing_1_1640995200000",
  "user_id": 1,
  "username": "student1"
}
```

#### submit-racing-answer
Submit answer with racing mechanics.

**Payload**:
```json
{
  "quiz_session_id": "racing_1_1640995200000",
  "user_id": 1,
  "question_id": 5,
  "answer_id": 2,
  "response_time": 3500
}
```

#### use-skill
Execute a skill during racing.

**Payload**:
```json
{
  "quiz_session_id": "racing_1_1640995200000",
  "user_id": 1,
  "skill_id": 3,
  "target_user_id": 2
}
```

#### skip-question
Skip current question (0 points, reset streak).

**Payload**:
```json
{
  "quiz_session_id": "racing_1_1640995200000",
  "user_id": 1,
  "question_id": 5
}
```

#### get-random-skill
Request random skill when energy = 100%.

**Payload**:
```json
{
  "quiz_session_id": "racing_1_1640995200000",
  "user_id": 1
}
```

#### player-ready
Signal readiness for next round.

**Payload**:
```json
{
  "quiz_session_id": "racing_1_1640995200000",
  "user_id": 1
}
```

### Server → Client Events

#### quiz-racing-initialized
Session successfully initialized.

**Payload**:
```json
{
  "session_id": "racing_1_1640995200000",
  "participants": [...],
  "total_questions": 10,
  "timestamp": 1640995200000
}
```

#### energy-update
Player energy changed.

**Payload**:
```json
{
  "user_id": 1,
  "energy_percent": 85,
  "energy_gain": 25,
  "skill_available": true,
  "timestamp": 1640995200000
}
```

#### skill-available
Random skill selected for player.

**Payload**:
```json
{
  "user_id": 1,
  "skill": {
    "skill_id": 5,
    "skill_name": "Double",
    "category": "BURST"
  },
  "energy_percent": 100,
  "timestamp": 1640995200000
}
```

#### skill-executed
Skill was executed by a player.

**Payload**:
```json
{
  "executor_id": 1,
  "skill": {...},
  "target_id": 2,
  "effect_data": {
    "effect": "blackhole",
    "duration": 3
  },
  "message": "Blackhole executed successfully",
  "timestamp": 1640995200000
}
```

#### racing-answer-result
Result of answer submission.

**Payload**:
```json
{
  "question_id": 5,
  "is_correct": true,
  "points_earned": 120,
  "total_score": 450,
  "current_streak": 3,
  "energy_percent": 85,
  "speed_bonus": 20,
  "streak_bonus": 10,
  "skill_available": false,
  "timestamp": 1640995200000
}
```

#### leaderboard-update
Real-time leaderboard update.

**Payload**:
```json
{
  "session_id": "racing_1_1640995200000",
  "leaderboard": [
    {
      "position": 1,
      "user_id": 1,
      "username": "student1",
      "current_score": 450,
      "current_streak": 3,
      "energy_percent": 85
    }
  ],
  "timestamp": 1640995200000
}
```

---

## Energy System

### Energy Gain Rules
- **Base gain**: +20% for correct answers
- **Speed bonus**: +10% for fast answers (< 5 seconds)
- **Streak bonus**: +5% for streak ≥ 3
- **No energy loss**: Wrong answers don't reduce energy
- **Skill trigger**: 100% energy allows skill selection

### Energy Reset
- Energy resets to 0% after skill usage
- Energy persists across questions until skill is used

---

## Skill Categories & Effects

### Attack Skills (4)
1. **Blackhole**: Target leader, 0 points for 3 questions
2. **Steal**: Target player above, steal 50% points (10% risk)
3. **Break**: Reset highest streak (≥3)
4. **Slow**: Reduce speed bonus for all others (2 questions)

### Defense Skills (3)
1. **Shield**: Immunity to attacks (45 seconds)
2. **Lock**: Protect streak from reset (4 questions, need streak ≥2)
3. **Cleanse**: Remove debuffs + restore 50% energy

### Burst Skills (5)
1. **Double**: ×2 points next question (25% risk)
2. **Lucky**: 60% chance wrong answer still scores
3. **Triple**: ×3 points next question (40% risk + streak reset)
4. **Perfect**: Guaranteed correct answer next question
5. **Quintuple**: ×5 points next question (50% risk + streak reset)

### Special Skills (3)
1. **Swap**: Exchange total points with random player
2. **Dice**: Random benefit (1-6 roll)
3. **Energy**: Instant +100% energy (combo potential)

### Ultimate Skills (2)
1. **King**: ×2 points + immunity + guaranteed correct (3 questions)
2. **Phoenix**: Bottom 3 → Top 3 jump OR lose 30% points

---

## Testing

### Prerequisites
1. Server running on localhost:3000
2. At least 3 test user accounts
3. Skills purchased and available in user inventories
4. Redis server running for session caching

### Test Script
Run the comprehensive test script:
```bash
cd doc_for_devgame/task4
node test_quiz_racing_api.js
```

### Manual Testing with Postman
1. Import the API endpoints
2. Set up authentication tokens
3. Test session initialization
4. Test loadout management
5. Use WebSocket client for real-time events

---

## Integration Notes

### Frontend Integration
- Connect to WebSocket on quiz start
- Handle real-time events for UI updates
- Implement skill selection UI when energy = 100%
- Show active effects and timers
- Display real-time leaderboard

### Performance Considerations
- Redis caching for session data (2-hour TTL)
- Efficient participant lookup with user ID indexing
- Batch WebSocket emissions for multiple participants
- Active effect cleanup on session end

### Security
- Authentication required for all endpoints
- Session validation for skill usage
- Skill ownership verification
- Rate limiting on skill execution
