# QL_CTDT System Architecture Document

## Table of Contents

- [QL_CTDT System Architecture Document](#table-of-contents)
  - [Document Information](./document-information.md)
    - [Change Log](./document-information.md#change-log)
  - [Executive Summary](./executive-summary.md)
  - [Technology Stack](./technology-stack.md)
    - [Core Technologies](./technology-stack.md#core-technologies)
    - [Development Tools](./technology-stack.md#development-tools)
  - [System Architecture](./system-architecture.md)
    - [High-Level Architecture](./system-architecture.md#high-level-architecture)
    - [Deployment Architecture](./system-architecture.md#deployment-architecture)
  - [Project Structure](./project-structure.md)
    - [Monorepo Organization](./project-structure.md#monorepo-organization)
  - [Data Architecture](./data-architecture.md)
    - [Database Design](./data-architecture.md#database-design)
    - [Caching Strategy](./data-architecture.md#caching-strategy)
    - [File Storage](./data-architecture.md#file-storage)
  - [Security Architecture](./security-architecture.md)
    - [Authentication & Authorization](./security-architecture.md#authentication-authorization)
    - [API Security](./security-architecture.md#api-security)
  - [Real-time Architecture](./real-time-architecture.md)
    - [Socket.IO Implementation](./real-time-architecture.md#socketio-implementation)
    - [Real-time Data Flow](./real-time-architecture.md#real-time-data-flow)
  - [Performance Considerations](./performance-considerations.md)
    - [Frontend Performance](./performance-considerations.md#frontend-performance)
    - [Backend Performance](./performance-considerations.md#backend-performance)
  - [Testing Strategy](./testing-strategy.md)
    - [Project Testing Policy](./testing-strategy.md#project-testing-policy)
  - [Deployment Strategy](./deployment-strategy.md)
    - [Development Environment](./deployment-strategy.md#development-environment)
    - [Production Deployment](./deployment-strategy.md#production-deployment)
  - [Technical Debt & Known Issues](./technical-debt-known-issues.md)
    - [Critical Issues](./technical-debt-known-issues.md#critical-issues)
    - [Performance Issues](./technical-debt-known-issues.md#performance-issues)
  - [Future Considerations](./future-considerations.md)
    - [Scalability Improvements](./future-considerations.md#scalability-improvements)
    - [Architecture Evolution](./future-considerations.md#architecture-evolution)
  - [API Architecture](./api-architecture.md)
    - [REST API Design](./api-architecture.md#rest-api-design)
      - [Core API Endpoints](./api-architecture.md#core-api-endpoints)
    - [API Response Format](./api-architecture.md#api-response-format)
    - [Real-time Events (Socket.IO)](./api-architecture.md#real-time-events-socketio)
  - [Integration Architecture](./integration-architecture.md)
    - [External Integrations](./integration-architecture.md#external-integrations)
    - [Internal Service Communication](./integration-architecture.md#internal-service-communication)
  - [Monitoring & Observability](./monitoring-observability.md)
    - [Current Monitoring](./monitoring-observability.md#current-monitoring)
    - [Recommended Monitoring](./monitoring-observability.md#recommended-monitoring)
