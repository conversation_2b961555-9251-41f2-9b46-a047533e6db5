'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
    class Emoji extends Model {
        static associate(models) {
            // Associations
            Emoji.hasMany(models.UserInventory, { 
                foreignKey: 'item_id',
                scope: { item_type: 'EMOJI' },
                as: 'UserInventories' 
            });
        }

        /**
         * Get all available emojis
         * @returns {Array<Emoji>}
         */
        static async getAvailableEmojis() {
            return await Emoji.findAll({
                where: { is_active: true },
                order: [['category', 'ASC'], ['sort_order', 'ASC']]
            });
        }

        /**
         * Get emojis by category
         * @param {string} category - Emoji category
         * @returns {Array<Emoji>}
         */
        static async getEmojisByCategory(category) {
            return await Emoji.findAll({
                where: { 
                    category: category,
                    is_active: true 
                },
                order: [['sort_order', 'ASC']]
            });
        }

        /**
         * Get emojis by unlock type
         * @param {string} unlockType - Unlock type (DEFAULT, LEVEL, EGG, etc.)
         * @returns {Array<Emoji>}
         */
        static async getEmojisByUnlockType(unlockType) {
            return await Emoji.findAll({
                where: { 
                    unlock_type: unlockType,
                    is_active: true 
                },
                order: [['category', 'ASC'], ['sort_order', 'ASC']]
            });
        }

        /**
         * Get emojis by rarity
         * @param {string} rarity - Rarity level
         * @returns {Array<Emoji>}
         */
        static async getEmojisByRarity(rarity) {
            return await Emoji.findAll({
                where: { 
                    rarity: rarity,
                    is_active: true 
                },
                order: [['category', 'ASC'], ['sort_order', 'ASC']]
            });
        }

        /**
         * Get default emojis
         * @returns {Array<Emoji>}
         */
        static async getDefaultEmojis() {
            return await Emoji.findAll({
                where: { 
                    is_default: true,
                    is_active: true 
                },
                order: [['category', 'ASC'], ['sort_order', 'ASC']]
            });
        }

        /**
         * Get emojis unlockable by level
         * @param {number} userLevel - User's current level
         * @returns {Array<Emoji>}
         */
        static async getUnlockableEmojisByLevel(userLevel) {
            return await Emoji.findAll({
                where: { 
                    unlock_type: 'LEVEL',
                    is_active: true,
                    unlock_condition: {
                        required_level: {
                            [sequelize.Sequelize.Op.lte]: userLevel
                        }
                    }
                },
                order: [['category', 'ASC'], ['sort_order', 'ASC']]
            });
        }

        /**
         * Get emojis from specific egg types
         * @param {Array<string>} eggTypes - Array of egg type names
         * @returns {Array<Emoji>}
         */
        static async getEmojisFromEggTypes(eggTypes) {
            return await Emoji.findAll({
                where: { 
                    unlock_type: 'EGG',
                    is_active: true,
                    [sequelize.Sequelize.Op.or]: eggTypes.map(eggType => ({
                        unlock_condition: {
                            egg_types: {
                                [sequelize.Sequelize.Op.contains]: [eggType]
                            }
                        }
                    }))
                },
                order: [['rarity', 'DESC'], ['category', 'ASC'], ['sort_order', 'ASC']]
            });
        }

        /**
         * Search emojis by name or code
         * @param {string} searchTerm - Search term
         * @returns {Array<Emoji>}
         */
        static async searchEmojis(searchTerm) {
            return await Emoji.findAll({
                where: { 
                    is_active: true,
                    [sequelize.Sequelize.Op.or]: [
                        {
                            emoji_name: {
                                [sequelize.Sequelize.Op.iLike]: `%${searchTerm}%`
                            }
                        },
                        {
                            emoji_code: {
                                [sequelize.Sequelize.Op.iLike]: `%${searchTerm}%`
                            }
                        }
                    ]
                },
                order: [['category', 'ASC'], ['sort_order', 'ASC']]
            });
        }

        /**
         * Check if emoji can be unlocked by user
         * @param {number} userLevel - User's current level
         * @param {Array<string>} userAchievements - User's achievements
         * @returns {boolean}
         */
        canBeUnlockedBy(userLevel, userAchievements = []) {
            if (!this.is_active) return false;

            switch (this.unlock_type) {
                case 'DEFAULT':
                    return true;
                
                case 'LEVEL':
                    const requiredLevel = this.unlock_condition?.required_level || 0;
                    return userLevel >= requiredLevel;
                
                case 'ACHIEVEMENT':
                    const requiredAchievements = this.unlock_condition?.required_achievements || [];
                    return requiredAchievements.every(achievement => 
                        userAchievements.includes(achievement)
                    );
                
                case 'EGG':
                case 'SHOP':
                case 'SPECIAL':
                default:
                    return false; // These require special unlock methods
            }
        }

        /**
         * Get unlock description
         * @returns {string}
         */
        getUnlockDescription() {
            switch (this.unlock_type) {
                case 'DEFAULT':
                    return 'Có sẵn từ đầu';
                
                case 'LEVEL':
                    const requiredLevel = this.unlock_condition?.required_level || 0;
                    return `Mở khóa ở cấp độ ${requiredLevel}`;
                
                case 'EGG':
                    const eggTypes = this.unlock_condition?.egg_types || [];
                    return `Có thể nhận từ: ${eggTypes.join(', ')}`;
                
                case 'SHOP':
                    return 'Có thể mua trong cửa hàng';
                
                case 'ACHIEVEMENT':
                    return 'Mở khóa qua thành tích';
                
                case 'SPECIAL':
                    return 'Emoji đặc biệt';
                
                default:
                    return 'Cách mở khóa không xác định';
            }
        }

        /**
         * Get rarity color
         * @returns {string}
         */
        getRarityColor() {
            const rarityColors = {
                'COMMON': '#9ca3af',      // Gray
                'UNCOMMON': '#10b981',    // Green
                'RARE': '#3b82f6',       // Blue
                'EPIC': '#8b5cf6',       // Purple
                'LEGENDARY': '#f59e0b'   // Orange/Gold
            };
            return rarityColors[this.rarity] || rarityColors['COMMON'];
        }

        /**
         * Get rarity display name
         * @returns {string}
         */
        getRarityDisplayName() {
            const rarityNames = {
                'COMMON': 'Phổ Biến',
                'UNCOMMON': 'Không Phổ Biến',
                'RARE': 'Hiếm',
                'EPIC': 'Sử Thi',
                'LEGENDARY': 'Huyền Thoại'
            };
            return rarityNames[this.rarity] || 'Không Xác Định';
        }

        /**
         * Get category display name
         * @returns {string}
         */
        getCategoryDisplayName() {
            const categoryNames = {
                'GENERAL': 'Tổng Quát',
                'HAPPY': 'Vui Vẻ',
                'SAD': 'Buồn',
                'ANGRY': 'Tức Giận',
                'SURPRISED': 'Ngạc Nhiên',
                'LOVE': 'Yêu Thương',
                'CELEBRATION': 'Chúc Mừng',
                'ANIMALS': 'Động Vật',
                'SPECIAL': 'Đặc Biệt'
            };
            return categoryNames[this.category] || 'Không Xác Định';
        }

        /**
         * Get emoji display (unicode or image)
         * @returns {string}
         */
        getEmojiDisplay() {
            return this.emoji_unicode || this.emoji_image || '❓';
        }

        /**
         * Get decomposition value in Kristal
         * @returns {number}
         */
        getDecompositionValue() {
            const decompositionValues = {
                'COMMON': 1,
                'UNCOMMON': 3,
                'RARE': 10,
                'EPIC': 30,
                'LEGENDARY': 100
            };
            return decompositionValues[this.rarity] || 0;
        }

        /**
         * Check if this is a premium emoji
         * @returns {boolean}
         */
        isPremium() {
            return ['EPIC', 'LEGENDARY'].includes(this.rarity);
        }

        /**
         * Check if this is an animated emoji
         * @returns {boolean}
         */
        isAnimated() {
            return this.emoji_image && this.emoji_image.includes('.gif');
        }

        /**
         * Get formatted emoji info
         * @returns {Object}
         */
        getFormattedInfo() {
            return {
                emoji_id: this.emoji_id,
                emoji_name: this.emoji_name,
                emoji_code: this.emoji_code,
                emoji_unicode: this.emoji_unicode,
                emoji_image: this.emoji_image,
                emoji_display: this.getEmojiDisplay(),
                description: this.description,
                rarity: this.rarity,
                rarity_display: this.getRarityDisplayName(),
                rarity_color: this.getRarityColor(),
                unlock_type: this.unlock_type,
                unlock_description: this.getUnlockDescription(),
                category: this.category,
                category_display: this.getCategoryDisplayName(),
                decomposition_value: this.getDecompositionValue(),
                is_premium: this.isPremium(),
                is_animated: this.isAnimated(),
                is_default: this.is_default,
                sort_order: this.sort_order
            };
        }
    }

    Emoji.init(
        {
            emoji_id: {
                type: DataTypes.INTEGER,
                primaryKey: true,
                autoIncrement: true,
                autoIncrementIdentity: true
            },
            emoji_name: {
                type: DataTypes.STRING(50),
                allowNull: false,
                comment: 'Tên emoji'
            },
            emoji_code: {
                type: DataTypes.STRING(20),
                allowNull: false,
                unique: true,
                comment: 'Mã định danh emoji'
            },
            emoji_unicode: {
                type: DataTypes.STRING(20),
                allowNull: true,
                comment: 'Unicode của emoji'
            },
            emoji_image: {
                type: DataTypes.STRING(255),
                allowNull: true,
                comment: 'Đường dẫn hình ảnh emoji'
            },
            description: {
                type: DataTypes.TEXT,
                allowNull: true,
                comment: 'Mô tả emoji'
            },
            rarity: {
                type: DataTypes.STRING(20),
                allowNull: false,
                defaultValue: 'COMMON',
                validate: {
                    isIn: [['COMMON', 'UNCOMMON', 'RARE', 'EPIC', 'LEGENDARY']]
                },
                comment: 'Độ hiếm của emoji'
            },
            unlock_type: {
                type: DataTypes.STRING(30),
                allowNull: false,
                defaultValue: 'DEFAULT',
                validate: {
                    isIn: [['DEFAULT', 'LEVEL', 'EGG', 'SHOP', 'ACHIEVEMENT', 'SPECIAL']]
                },
                comment: 'Cách thức mở khóa emoji'
            },
            unlock_condition: {
                type: DataTypes.JSONB,
                allowNull: false,
                defaultValue: {},
                comment: 'Điều kiện mở khóa dạng JSON'
            },
            category: {
                type: DataTypes.STRING(30),
                allowNull: false,
                defaultValue: 'GENERAL',
                validate: {
                    isIn: [['GENERAL', 'HAPPY', 'SAD', 'ANGRY', 'SURPRISED', 'LOVE', 'CELEBRATION', 'ANIMALS', 'SPECIAL']]
                },
                comment: 'Danh mục emoji'
            },
            is_default: {
                type: DataTypes.BOOLEAN,
                allowNull: false,
                defaultValue: false,
                comment: 'Emoji mặc định'
            },
            is_active: {
                type: DataTypes.BOOLEAN,
                allowNull: false,
                defaultValue: true,
                comment: 'Trạng thái hoạt động'
            },
            sort_order: {
                type: DataTypes.INTEGER,
                allowNull: false,
                defaultValue: 0,
                comment: 'Thứ tự sắp xếp'
            }
        },
        {
            sequelize,
            modelName: 'Emoji',
            tableName: 'Emojis',
            timestamps: true,
            createdAt: 'created_at',
            updatedAt: 'updated_at',
            indexes: [
                {
                    fields: ['rarity']
                },
                {
                    fields: ['category']
                },
                {
                    fields: ['unlock_type']
                },
                {
                    fields: ['is_active']
                },
                {
                    fields: ['is_default']
                },
                {
                    fields: ['sort_order']
                },
                {
                    fields: ['emoji_code'],
                    unique: true
                }
            ]
        }
    );

    return Emoji;
};
