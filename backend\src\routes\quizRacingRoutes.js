// backend/src/routes/quizRacingRoutes.js
// Routes for Quiz Racing System

const express = require('express');
const router = express.Router();
const { authenticateToken, authorize } = require('../middleware/authMiddleware');
const QuizRacingService = require('../services/quizRacingService');
const { Quiz, User, QuizSkillLoadout, Skill, UserSkill } = require('../models');

// =====================================================
// QUIZ RACING SESSION MANAGEMENT
// =====================================================

/**
 * @route POST /api/quiz-racing/initialize
 * @desc Initialize a new quiz racing session
 * @access Private (Student+)
 */
router.post('/initialize', authenticateToken, authorize(['student', 'teacher', 'admin']), async (req, res) => {
    try {
        const { quiz_id, participants } = req.body;

        // Validate input
        if (!quiz_id || !participants || !Array.isArray(participants)) {
            return res.status(400).json({
                success: false,
                message: 'Quiz ID and participants array are required'
            });
        }

        // Get quiz details
        const quiz = await Quiz.findByPk(quiz_id);
        if (!quiz) {
            return res.status(404).json({
                success: false,
                message: 'Quiz not found'
            });
        }

        // Validate participants
        const validParticipants = [];
        for (const participant of participants) {
            const user = await User.findByPk(participant.user_id);
            if (user) {
                validParticipants.push({
                    user_id: user.user_id,
                    username: user.username
                });
            }
        }

        if (validParticipants.length < 2) {
            return res.status(400).json({
                success: false,
                message: 'At least 2 valid participants required for racing'
            });
        }

        // Generate session ID
        const quizSessionId = `racing_${quiz_id}_${Date.now()}`;

        // Initialize racing session
        const quizRacingService = new QuizRacingService(req.app.get('io'));
        const result = await quizRacingService.initializeQuizRacing(
            quizSessionId,
            validParticipants,
            quiz.total_questions || 10
        );

        if (result.success) {
            res.status(201).json({
                success: true,
                message: 'Quiz racing session initialized successfully',
                data: {
                    session_id: quizSessionId,
                    quiz_id: quiz_id,
                    participants: validParticipants,
                    total_questions: quiz.total_questions || 10
                }
            });
        } else {
            res.status(500).json({
                success: false,
                message: 'Failed to initialize quiz racing session',
                error: result.error
            });
        }
    } catch (error) {
        console.error('Error initializing quiz racing:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error.message
        });
    }
});

/**
 * @route GET /api/quiz-racing/session/:sessionId
 * @desc Get quiz racing session data
 * @access Private (Student+)
 */
router.get('/session/:sessionId', authenticateToken, authorize(['student', 'teacher', 'admin']), async (req, res) => {
    try {
        const { sessionId } = req.params;

        const quizRacingService = new QuizRacingService(req.app.get('io'));
        const sessionData = await quizRacingService.getSessionData(sessionId);

        if (!sessionData) {
            return res.status(404).json({
                success: false,
                message: 'Quiz racing session not found'
            });
        }

        res.json({
            success: true,
            message: 'Session data retrieved successfully',
            data: sessionData
        });
    } catch (error) {
        console.error('Error getting session data:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error.message
        });
    }
});

// =====================================================
// SKILL LOADOUT MANAGEMENT FOR RACING
// =====================================================

/**
 * @route POST /api/quiz-racing/loadout
 * @desc Set skill loadout for quiz racing session
 * @access Private (Student+)
 */
router.post('/loadout', authenticateToken, authorize(['student', 'teacher', 'admin']), async (req, res) => {
    try {
        const { quiz_session_id, skill_ids } = req.body;
        const userId = req.user.user_id;

        // Validate input
        if (!quiz_session_id || !skill_ids || !Array.isArray(skill_ids) || skill_ids.length !== 4) {
            return res.status(400).json({
                success: false,
                message: 'Quiz session ID and exactly 4 skill IDs are required'
            });
        }

        // Check for duplicates
        const uniqueSkills = [...new Set(skill_ids)];
        if (uniqueSkills.length !== 4) {
            return res.status(400).json({
                success: false,
                message: 'All 4 skills must be different'
            });
        }

        // Validate skill ownership
        const ownedSkills = await UserSkill.findAll({
            where: {
                user_id: userId,
                skill_id: skill_ids
            },
            include: [{ model: Skill, as: 'Skill' }]
        });

        if (ownedSkills.length !== 4) {
            return res.status(400).json({
                success: false,
                message: 'You do not own all selected skills'
            });
        }

        // Create or update loadout
        const [loadout, created] = await QuizSkillLoadout.findOrCreate({
            where: {
                user_id: userId,
                quiz_session_id: quiz_session_id
            },
            defaults: {
                skill_1_id: skill_ids[0],
                skill_2_id: skill_ids[1],
                skill_3_id: skill_ids[2],
                skill_4_id: skill_ids[3]
            }
        });

        if (!created) {
            // Update existing loadout
            await loadout.update({
                skill_1_id: skill_ids[0],
                skill_2_id: skill_ids[1],
                skill_3_id: skill_ids[2],
                skill_4_id: skill_ids[3]
            });
        }

        // Get full loadout with skill details
        const fullLoadout = await QuizSkillLoadout.findByPk(loadout.loadout_id, {
            include: [
                { model: Skill, as: 'Skill1' },
                { model: Skill, as: 'Skill2' },
                { model: Skill, as: 'Skill3' },
                { model: Skill, as: 'Skill4' }
            ]
        });

        res.status(created ? 201 : 200).json({
            success: true,
            message: created ? 'Loadout created successfully' : 'Loadout updated successfully',
            data: {
                loadout_id: fullLoadout.loadout_id,
                skills: [
                    fullLoadout.Skill1,
                    fullLoadout.Skill2,
                    fullLoadout.Skill3,
                    fullLoadout.Skill4
                ]
            }
        });
    } catch (error) {
        console.error('Error setting quiz racing loadout:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error.message
        });
    }
});

/**
 * @route GET /api/quiz-racing/loadout/:sessionId
 * @desc Get user's loadout for specific racing session
 * @access Private (Student+)
 */
router.get('/loadout/:sessionId', authenticateToken, authorize(['student', 'teacher', 'admin']), async (req, res) => {
    try {
        const { sessionId } = req.params;
        const userId = req.user.user_id;

        const loadout = await QuizSkillLoadout.findOne({
            where: {
                user_id: userId,
                quiz_session_id: sessionId
            },
            include: [
                { model: Skill, as: 'Skill1' },
                { model: Skill, as: 'Skill2' },
                { model: Skill, as: 'Skill3' },
                { model: Skill, as: 'Skill4' }
            ]
        });

        if (!loadout) {
            return res.status(404).json({
                success: false,
                message: 'No loadout found for this session'
            });
        }

        res.json({
            success: true,
            message: 'Loadout retrieved successfully',
            data: {
                loadout_id: loadout.loadout_id,
                skills: [
                    loadout.Skill1,
                    loadout.Skill2,
                    loadout.Skill3,
                    loadout.Skill4
                ].filter(skill => skill !== null)
            }
        });
    } catch (error) {
        console.error('Error getting quiz racing loadout:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error.message
        });
    }
});

// =====================================================
// RACING STATISTICS
// =====================================================

/**
 * @route GET /api/quiz-racing/stats/:sessionId
 * @desc Get racing session statistics
 * @access Private (Student+)
 */
router.get('/stats/:sessionId', authenticateToken, authorize(['student', 'teacher', 'admin']), async (req, res) => {
    try {
        const { sessionId } = req.params;

        const quizRacingService = new QuizRacingService(req.app.get('io'));
        const sessionData = await quizRacingService.getSessionData(sessionId);

        if (!sessionData) {
            return res.status(404).json({
                success: false,
                message: 'Session not found'
            });
        }

        // Calculate statistics
        const stats = {
            session_id: sessionId,
            total_participants: sessionData.participants.length,
            current_round: sessionData.round_number,
            total_questions: sessionData.total_questions,
            session_duration: Date.now() - sessionData.session_start_time,
            leaderboard: sessionData.participants
                .sort((a, b) => b.current_score - a.current_score)
                .map((p, index) => ({
                    position: index + 1,
                    user_id: p.user_id,
                    username: p.username,
                    score: p.current_score,
                    streak: p.current_streak,
                    energy: p.energy_percent,
                    skills_used: p.skills_used.length
                }))
        };

        res.json({
            success: true,
            message: 'Session statistics retrieved successfully',
            data: stats
        });
    } catch (error) {
        console.error('Error getting racing statistics:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error.message
        });
    }
});

module.exports = router;
