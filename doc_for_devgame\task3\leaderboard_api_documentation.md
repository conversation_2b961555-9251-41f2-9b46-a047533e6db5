# LEADERBOARD SYSTEM API DOCUMENTATION
## Task 3.2: <PERSON><PERSON><PERSON> xếp hạng và leaderboard

### Base URL: `/api/leaderboard`

---

## 📊 PUBLIC LEADERBOARD ENDPOINTS

### 1. Get Global Leaderboard
**GET** `/api/leaderboard/global`

<PERSON><PERSON><PERSON> bảng xếp hạng toàn cầu theo tiêu chí chỉ định.

**Query Parameters:**
- `criteria` (string, optional): <PERSON>i<PERSON><PERSON> chí xếp hạng - `TOTAL_XP`, `LEVEL`, `QUIZ_SCORE`, `WIN_RATE`, `STREAK`, `SOCIAL_SCORE` (default: `TOTAL_XP`)
- `limit` (number, optional): Số lượng entries trả về (default: 50, max: 100)
- `offset` (number, optional): Offset cho pagination (default: 0)

**Example Request:**
```
GET /api/leaderboard/global?criteria=TOTAL_XP&limit=20&offset=0
```

**Example Response:**
```json
{
    "success": true,
    "message": "Global leaderboard retrieved successfully",
    "data": {
        "leaderboard_type": "GLOBAL",
        "ranking_criteria": "TOTAL_XP",
        "entries": [
            {
                "entry_id": 1,
                "user_id": 123,
                "current_rank": 1,
                "previous_rank": 2,
                "rank_change": 1,
                "score_value": 15000,
                "last_updated": "2024-01-15T10:30:00Z",
                "user": {
                    "user_id": 123,
                    "username": "player1",
                    "full_name": "Nguyễn Văn A",
                    "avatar_url": "/avatars/player1.jpg",
                    "current_level": 25,
                    "current_tier": "GOLD",
                    "current_xp": 15000
                }
            }
        ],
        "pagination": {
            "limit": 20,
            "offset": 0,
            "total_entries": 1
        }
    }
}
```

### 2. Get Tier-Based Leaderboard
**GET** `/api/leaderboard/tier`

Lấy bảng xếp hạng theo tier cụ thể.

**Query Parameters:**
- `tier` (string, required): Tier filter - `WOOD`, `BRONZE`, `SILVER`, `GOLD`, `PLATINUM`, `ONYX`, `SAPPHIRE`, `RUBY`, `AMETHYST`, `MASTER`
- `criteria` (string, optional): Tiêu chí xếp hạng (default: `TOTAL_XP`)
- `limit` (number, optional): Số lượng entries (default: 50)
- `offset` (number, optional): Offset cho pagination (default: 0)

**Example Request:**
```
GET /api/leaderboard/tier?tier=GOLD&criteria=LEVEL&limit=10
```

### 3. Get Time-Based Leaderboard
**GET** `/api/leaderboard/time`

Lấy bảng xếp hạng theo thời gian (hàng ngày, tuần, tháng).

**Query Parameters:**
- `time_type` (string, required): Loại thời gian - `DAILY`, `WEEKLY`, `MONTHLY`
- `criteria` (string, optional): Tiêu chí xếp hạng (default: `QUIZ_SCORE`)
- `limit` (number, optional): Số lượng entries (default: 50)
- `offset` (number, optional): Offset cho pagination (default: 0)
- `date` (string, optional): Ngày cụ thể (format: YYYY-MM-DD)

**Example Request:**
```
GET /api/leaderboard/time?time_type=WEEKLY&criteria=QUIZ_SCORE&limit=20
```

### 4. Get Top Performers
**GET** `/api/leaderboard/top-performers`

Lấy danh sách người chơi xuất sắc nhất theo tiêu chí hiệu suất.

**Query Parameters:**
- `criteria` (string, optional): Tiêu chí hiệu suất - `accuracy_rate`, `average_score`, `highest_score`, `first_place_finishes`, `longest_streak` (default: `accuracy_rate`)
- `time_period` (string, optional): Khoảng thời gian - `DAILY`, `WEEKLY`, `MONTHLY`, `ALL_TIME` (default: `ALL_TIME`)
- `limit` (number, optional): Số lượng performers (default: 10)
- `tier` (string, optional): Tier filter

**Example Request:**
```
GET /api/leaderboard/top-performers?criteria=accuracy_rate&time_period=WEEKLY&limit=10
```

### 5. Get Top Movers
**GET** `/api/leaderboard/top-movers`

Lấy danh sách người chơi có thay đổi thứ hạng lớn nhất.

**Query Parameters:**
- `leaderboard_type` (string, optional): Loại leaderboard (default: `GLOBAL`)
- `criteria` (string, optional): Tiêu chí xếp hạng (default: `TOTAL_XP`)
- `direction` (string, optional): Hướng di chuyển - `up`, `down` (default: `up`)
- `limit` (number, optional): Số lượng movers (default: 10)
- `tier` (string, optional): Tier filter

**Example Request:**
```
GET /api/leaderboard/top-movers?direction=up&limit=5
```

### 6. Get Leaderboard Statistics
**GET** `/api/leaderboard/stats`

Lấy thống kê tổng quan về leaderboard.

**Query Parameters:**
- `leaderboard_type` (string, optional): Loại leaderboard (default: `GLOBAL`)
- `criteria` (string, optional): Tiêu chí xếp hạng (default: `TOTAL_XP`)
- `tier` (string, optional): Tier filter

**Example Response:**
```json
{
    "success": true,
    "message": "Leaderboard statistics retrieved successfully",
    "data": {
        "leaderboard_type": "GLOBAL",
        "criteria": "TOTAL_XP",
        "statistics": {
            "total_participants": 1250,
            "average_score": 8500.75,
            "highest_score": 25000,
            "lowest_score": 100,
            "movers_up": 320,
            "movers_down": 280,
            "no_change": 650
        }
    }
}
```

---

## 👤 AUTHENTICATED USER ENDPOINTS

### 7. Get My Rank
**GET** `/api/leaderboard/my-rank`
**Authentication:** Required

Lấy thứ hạng của user hiện tại trong leaderboard chỉ định.

**Query Parameters:**
- `leaderboard_type` (string, optional): Loại leaderboard (default: `GLOBAL`)
- `criteria` (string, optional): Tiêu chí xếp hạng (default: `TOTAL_XP`)
- `tier` (string, optional): Tier filter

**Example Response:**
```json
{
    "success": true,
    "message": "User rank retrieved successfully",
    "data": {
        "user_id": 123,
        "current_rank": 45,
        "previous_rank": 52,
        "rank_change": 7,
        "score_value": 12500,
        "total_participants": 1250,
        "percentile": 96,
        "user": {
            "username": "player1",
            "full_name": "Nguyễn Văn A",
            "avatar_url": "/avatars/player1.jpg",
            "current_level": 22,
            "current_tier": "SILVER"
        }
    }
}
```

### 8. Get My Rankings
**GET** `/api/leaderboard/my-rankings`
**Authentication:** Required

Lấy tất cả thứ hạng của user hiện tại trên các leaderboard khác nhau.

**Example Response:**
```json
{
    "success": true,
    "message": "User rankings retrieved successfully",
    "data": {
        "global_xp": {
            "current_rank": 45,
            "score_value": 12500,
            "percentile": 96
        },
        "global_level": {
            "current_rank": 38,
            "score_value": 22,
            "percentile": 97
        },
        "global_quiz_score": {
            "current_rank": 67,
            "score_value": 8750,
            "percentile": 94
        },
        "tier_based": {
            "current_rank": 12,
            "score_value": 12500,
            "percentile": 88,
            "tier_filter": "SILVER"
        }
    }
}
```

### 9. Get My Performance Stats
**GET** `/api/leaderboard/my-performance`
**Authentication:** Required

Lấy thống kê hiệu suất chi tiết của user hiện tại.

**Query Parameters:**
- `time_period` (string, optional): Khoảng thời gian - `DAILY`, `WEEKLY`, `MONTHLY`, `ALL_TIME` (default: `ALL_TIME`)
- `period_date` (string, optional): Ngày cụ thể (format: YYYY-MM-DD)

**Example Response:**
```json
{
    "success": true,
    "message": "User performance stats retrieved successfully",
    "data": {
        "stats_id": 456,
        "user_id": 123,
        "time_period": "ALL_TIME",
        "total_quizzes_played": 85,
        "total_questions_answered": 1275,
        "total_correct_answers": 1020,
        "accuracy_rate": 80.00,
        "average_score": 145.50,
        "highest_score": 280,
        "total_score_earned": 12367,
        "first_place_finishes": 12,
        "top_3_finishes": 34,
        "top_5_finishes": 52,
        "average_rank": 4.2,
        "best_rank": 1,
        "longest_streak": 15,
        "total_streaks_achieved": 67,
        "average_answer_time": 3.250,
        "fastest_answer_time": 1.100,
        "speed_bonus_earned": 2340,
        "syncoin_earned": 8500,
        "kristal_earned": 125,
        "xp_earned": 12500,
        "eggs_received": 23,
        "emojis_used": 156,
        "social_interactions_sent": 89,
        "social_interactions_received": 134,
        "performance_grade": "B+",
        "win_rate": 14.12,
        "top_3_rate": 40.00,
        "average_xp_per_quiz": 147.06,
        "social_engagement_score": 5.06
    }
}
```

### 10. Compare Users
**GET** `/api/leaderboard/compare`
**Authentication:** Required

So sánh hiệu suất của user hiện tại với user khác.

**Query Parameters:**
- `compare_user_id` (number, required): ID của user để so sánh
- `time_period` (string, optional): Khoảng thời gian (default: `ALL_TIME`)

**Example Request:**
```
GET /api/leaderboard/compare?compare_user_id=456&time_period=MONTHLY
```

---

## 🔧 ADMIN ENDPOINTS

### 11. Initialize User Leaderboards
**POST** `/api/leaderboard/initialize-user`
**Authentication:** Required (Admin only)

Khởi tạo entries leaderboard cho một user.

**Request Body:**
```json
{
    "user_id": 123
}
```

### 12. Update User Score
**POST** `/api/leaderboard/update-score`
**Authentication:** Required (Admin only)

Cập nhật điểm số của user trong leaderboard.

**Request Body:**
```json
{
    "user_id": 123,
    "criteria": "TOTAL_XP",
    "score": 15000,
    "leaderboard_type": "GLOBAL",
    "tier": "GOLD"
}
```

---

## 📋 SYSTEM INFO ENDPOINT

### 13. Get System Information
**GET** `/api/leaderboard/info`

Lấy thông tin về các tùy chọn có sẵn trong hệ thống leaderboard.

**Example Response:**
```json
{
    "success": true,
    "message": "Leaderboard system information",
    "data": {
        "leaderboard_types": [
            {
                "type": "GLOBAL",
                "description": "Global leaderboard across all users",
                "endpoint": "/api/leaderboard/global"
            },
            {
                "type": "TIER_BASED",
                "description": "Leaderboard filtered by user tier",
                "endpoint": "/api/leaderboard/tier"
            },
            {
                "type": "TIME_BASED",
                "description": "Time-based leaderboards (daily, weekly, monthly)",
                "endpoint": "/api/leaderboard/time"
            }
        ],
        "ranking_criteria": [
            {
                "criteria": "TOTAL_XP",
                "description": "Total experience points earned"
            },
            {
                "criteria": "LEVEL",
                "description": "Current user level"
            },
            {
                "criteria": "QUIZ_SCORE",
                "description": "Quiz performance score"
            }
        ],
        "tiers": [
            "WOOD", "BRONZE", "SILVER", "GOLD", "PLATINUM", 
            "ONYX", "SAPPHIRE", "RUBY", "AMETHYST", "MASTER"
        ],
        "time_periods": [
            "DAILY", "WEEKLY", "MONTHLY", "ALL_TIME"
        ],
        "performance_criteria": [
            "accuracy_rate", "average_score", "highest_score", 
            "first_place_finishes", "longest_streak"
        ]
    }
}
```

---

## 🚨 ERROR RESPONSES

### Common Error Codes:
- **400 Bad Request**: Invalid parameters or missing required fields
- **401 Unauthorized**: Authentication required
- **403 Forbidden**: Insufficient permissions
- **404 Not Found**: Resource not found
- **500 Internal Server Error**: Server error

### Example Error Response:
```json
{
    "success": false,
    "message": "Invalid ranking criteria",
    "valid_criteria": ["TOTAL_XP", "LEVEL", "QUIZ_SCORE", "WIN_RATE", "STREAK", "SOCIAL_SCORE"]
}
```

---

## 📊 INTEGRATION NOTES

### Automatic Updates:
- Leaderboard entries được cập nhật tự động sau mỗi quiz completion
- Performance stats được tính toán real-time
- Ranking changes được track trong RankingHistory table

### Caching Strategy:
- Global leaderboards: Cache 5 phút
- User rankings: Cache 1 phút  
- Performance stats: Cache 10 phút
- Leaderboard stats: Cache 15 phút

### Rate Limiting:
- Public endpoints: 100 requests/minute
- Authenticated endpoints: 200 requests/minute
- Admin endpoints: 50 requests/minute
