"use client";

import React, { useState, useMemo } from "react";
import {
  <PERSON>,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/layout";
import { <PERSON><PERSON> } from "@/components/ui/forms";
import { Badge } from "@/components/ui/feedback";
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/feedback";
import { EmptyState } from "@/components/ui/feedback";
import { Input } from "@/components/ui/forms";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/forms";
import { useGamification } from "@/lib/hooks/use-gamification";
import { cn } from "@/lib/utils";
import {
  getTierIconFromLevel,
  getVietnameseTierName,
  getTierCardStyle,
  TIER_NAMES,
} from "@/lib/utils/tier-assets";
import { TierIcon } from "@/lib/hooks/use-tier-icon";
import { UserTitleData } from "@/lib/types/gamification";
import { Search, Crown, Lock, Check } from "lucide-react";

// Utility function for search optimization
const createSearchMatcher = (query: string) => {
  const lowerQuery = query.toLowerCase();
  return (title: UserTitleData) => {
    const searchableFields = [
      title.Title.title_name,
      title.Title.title_display,
      getVietnameseTierName(title.Title.tier_name),
    ];
    return searchableFields.some((field) =>
      field.toLowerCase().includes(lowerQuery)
    );
  };
};

interface TitleGalleryProps {
  className?: string;
  variant?: "default" | "compact";
  showStats?: boolean;
}

export const TitleGallery: React.FC<TitleGalleryProps> = ({
  className,
  variant = "default",
  showStats = true,
}) => {
  const { userTitles, userGamification, isLoading, error } = useGamification();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedTier, setSelectedTier] = useState<string>("all");
  const [selectedStatus, setSelectedStatus] = useState<string>("all");

  // Get current user level for determining locked titles
  const currentLevel = userGamification?.current_level || 1;

  // Note: getTierCardStyle is now imported from tier-assets utility

  // Filter and search titles with defensive programming
  const filteredTitles = useMemo(() => {
    if (!userTitles || !Array.isArray(userTitles) || userTitles.length === 0)
      return [];
    let filtered = userTitles.filter((title) => title && title.Title); // Filter out invalid entries

    // Search filter
    if (searchQuery) {
      const searchMatcher = createSearchMatcher(searchQuery);
      filtered = filtered.filter(searchMatcher);
    }

    // Tier filter
    if (selectedTier !== "all") {
      filtered = filtered.filter(
        (title) =>
          title.Title.tier_name.toLowerCase() === selectedTier.toLowerCase()
      );
    }

    // Status filter
    if (selectedStatus !== "all") {
      if (selectedStatus === "unlocked") {
        filtered = filtered.filter((title) => title.unlocked_at);
      } else if (selectedStatus === "locked") {
        filtered = filtered.filter((title) => !title.unlocked_at);
      } else if (selectedStatus === "active") {
        filtered = filtered.filter((title) => title.is_active);
      }
    }

    return filtered;
  }, [userTitles, searchQuery, selectedTier, selectedStatus]);

  // Get active title
  const activeTitle = userTitles?.find((title) => title.is_active);

  // Handle title selection (set as active)
  const handleTitleSelect = async (titleId: number) => {
    try {
      // TODO: Implement API call to set active title
      console.log("Setting active title:", titleId);
    } catch (error) {
      console.error("Failed to set active title:", error);
    }
  };

  if (isLoading) {
    return (
      <Card className={cn("w-full", className)}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Crown className="h-5 w-5" />
            Bộ Sưu Tập Danh Hiệu
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={cn("w-full", className)}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Crown className="h-5 w-5" />
            Bộ Sưu Tập Danh Hiệu
          </CardTitle>
        </CardHeader>
        <CardContent>
          <EmptyState
            title="Lỗi tải dữ liệu"
            description="Không thể tải danh sách danh hiệu. Vui lòng thử lại sau."
            icon="Search"
          />
        </CardContent>
      </Card>
    );
  }

  return (
    <TooltipProvider>
      <Card className={cn("w-full", className)}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Crown className="h-5 w-5" />
            Bộ Sưu Tập Danh Hiệu
          </CardTitle>

          {showStats && userGamification && (
            <div className="flex flex-wrap gap-2 mt-2">
              <Badge variant="secondary">
                Đã mở khóa:{" "}
                {userTitles?.filter((t) => t.unlocked_at).length || 0}/
                {userTitles?.length || 0}
              </Badge>
              {activeTitle && (
                <Badge variant="default" className="flex items-center gap-1">
                  <Check className="h-3 w-3" />
                  Đang sử dụng: {activeTitle.Title.title_display}
                </Badge>
              )}
            </div>
          )}
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Search and Filter Controls */}
          <div className="flex flex-col sm:flex-row gap-3">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Tìm kiếm danh hiệu..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>

            <Select value={selectedTier} onValueChange={setSelectedTier}>
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="Chọn hạng" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tất cả hạng</SelectItem>
                {TIER_NAMES.map((tier) => (
                  <SelectItem key={tier} value={tier}>
                    {getVietnameseTierName(tier)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="Trạng thái" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tất cả</SelectItem>
                <SelectItem value="unlocked">Đã mở khóa</SelectItem>
                <SelectItem value="locked">Chưa mở khóa</SelectItem>
                <SelectItem value="active">Đang sử dụng</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Title Grid */}
          {filteredTitles.length === 0 ? (
            <EmptyState
              title="Không tìm thấy danh hiệu"
              description="Thử thay đổi bộ lọc hoặc từ khóa tìm kiếm."
              icon="Search"
            />
          ) : (
            <div
              className={cn(
                "grid gap-3",
                variant === "compact"
                  ? "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3"
                  : "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"
              )}
            >
              {filteredTitles.map((title) => (
                <TitleCard
                  key={title.user_title_id}
                  title={title}
                  currentLevel={currentLevel}
                  isActive={title.is_active}
                  onSelect={() => handleTitleSelect(title.title_id)}
                  getTierCardStyle={getTierCardStyle}
                  variant={variant}
                />
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </TooltipProvider>
  );
};

// Title Card Component
interface TitleCardProps {
  title: UserTitleData;
  currentLevel: number;
  isActive: boolean;
  onSelect: () => void;
  getTierCardStyle: (tierName: string) => string;
  variant: "default" | "compact";
}

const TitleCard: React.FC<TitleCardProps> = ({
  title,
  currentLevel,
  isActive,
  onSelect,
  getTierCardStyle,
  variant,
}) => {
  const isUnlocked = !!title.unlocked_at;
  const isLocked = currentLevel < title.Title.unlock_level;
  const tierColorClasses = getTierCardStyle(title.Title.tier_name);

  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <Card
          className={cn(
            "cursor-pointer transition-all duration-200 hover:shadow-md",
            isActive && "ring-2 ring-primary ring-offset-2",
            isLocked && "opacity-60",
            tierColorClasses.split(" ")[2] // border color
          )}
          onClick={isUnlocked ? onSelect : undefined}
        >
          <CardContent className={cn("p-4", variant === "compact" && "p-3")}>
            <div className="flex items-center gap-3">
              {/* Tier Icon */}
              <div className="relative">
                <TierIcon
                  level={title.Title.unlock_level}
                  size={variant === "compact" ? "sm" : "md"}
                  tierName={title.Title.tier_name}
                  levelInTier={1}
                />
                {isLocked && (
                  <div className="absolute inset-0 flex items-center justify-center bg-black/50 rounded-full">
                    <Lock className="h-3 w-3 text-white" />
                  </div>
                )}
                {isActive && (
                  <div className="absolute -top-1 -right-1 bg-primary rounded-full p-1">
                    <Check className="h-2 w-2 text-primary-foreground" />
                  </div>
                )}
              </div>

              {/* Title Info */}
              <div className="flex-1 min-w-0">
                <h4
                  className={cn(
                    "font-semibold truncate",
                    variant === "compact" ? "text-sm" : "text-base",
                    tierColorClasses.split(" ")[0] // text color
                  )}
                >
                  {title.Title.title_display}
                </h4>
                <p
                  className={cn(
                    "text-muted-foreground truncate",
                    variant === "compact" ? "text-xs" : "text-sm"
                  )}
                >
                  {getVietnameseTierName(title.Title.tier_name)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </TooltipTrigger>

      <TooltipContent side="bottom" className="max-w-xs">
        <div className="space-y-2">
          <div>
            <p className="font-semibold">{title.Title.title_name}</p>
            <p className="text-sm text-muted-foreground">
              {title.Title.title_display}
            </p>
          </div>
          <div className="text-xs space-y-1">
            <p>Hạng: {getVietnameseTierName(title.Title.tier_name)}</p>
            <p>Yêu cầu: Level {title.Title.unlock_level}</p>
            {isUnlocked && title.unlocked_at && (
              <p>
                Mở khóa:{" "}
                {new Date(title.unlocked_at).toLocaleDateString("vi-VN")}
              </p>
            )}
            {isLocked && (
              <p className="text-yellow-600">
                Cần Level {title.Title.unlock_level} để mở khóa
              </p>
            )}
            {isActive && (
              <p className="text-primary font-medium">Đang sử dụng</p>
            )}
          </div>
        </div>
      </TooltipContent>
    </Tooltip>
  );
};

export default TitleGallery;
