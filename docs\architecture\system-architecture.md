# System Architecture

## High-Level Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     Client      │    │     Nginx       │    │   Load Balancer │
│   (Browser)     │◄──►│  Reverse Proxy  │◄──►│   (Optional)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                        Application Layer                        │
├─────────────────┬─────────────────┬─────────────────────────────┤
│   Frontend      │    Backend      │       Real-time             │
│   Next.js 15    │   Express 5     │      Socket.IO              │
│   React 19      │   Node.js 18+   │                             │
│   TypeScript    │   JavaScript    │                             │
└─────────────────┴─────────────────┴─────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                         Data Layer                              │
├─────────────────┬─────────────────┬─────────────────────────────┤
│   PostgreSQL    │     Redis       │      File Storage           │
│   Primary DB    │   Cache/Session │    Local uploads/           │
│   Port 5433     │   Port 6379     │                             │
└─────────────────┴─────────────────┴─────────────────────────────┘
```

## Deployment Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                      Docker Compose                             │
├─────────────────┬─────────────────┬─────────────────────────────┤
│   nginx:latest  │  frontend:3000  │     backend:8888            │
│   SSL/Proxy     │   Next.js App   │    Express API              │
└─────────────────┴─────────────────┴─────────────────────────────┘
├─────────────────┬─────────────────────────────────────────────────┤
│ postgres:15     │              redis:7                           │
│ Database        │              Cache                             │
│ Port 5433       │              Port 6379                         │
└─────────────────┴─────────────────────────────────────────────────┘
```
