{"info": {"_postman_id": "frame-shop-test-collection", "name": "Frame Shop Test Collection", "description": "Test collection for Frame Shop functionality with Kristal purchases", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"exec": ["// Auto-login if no token exists", "if (!pm.environment.get('auth_token')) {", "    console.log('No auth token found, attempting auto-login...');", "    ", "    const loginRequest = {", "        url: pm.environment.get('base_url') + '/api/users/login',", "        method: 'POST',", "        header: {", "            'Content-Type': 'application/json'", "        },", "        body: {", "            mode: 'raw',", "            raw: JSON.stringify({", "                email: pm.environment.get('test_email') || '<EMAIL>',", "                password: pm.environment.get('test_password') || 'password123'", "            })", "        }", "    };", "    ", "    pm.sendRequest(loginRequest, function (err, response) {", "        if (err) {", "            console.log('<PERSON><PERSON> failed:', err);", "        } else if (response.code === 200) {", "            const responseJson = response.json();", "            if (responseJson.success && responseJson.data && responseJson.data.token) {", "                pm.environment.set('auth_token', responseJson.data.token);", "                pm.environment.set('user_id', responseJson.data.user.user_id);", "                console.log('Auto-login successful, token set');", "            }", "        } else {", "            console.log('<PERSON><PERSON> failed with status:', response.code);", "        }", "    });", "}"], "type": "text/javascript"}}], "variable": [{"key": "base_url", "value": "http://localhost:8888", "type": "string"}], "item": [{"name": "1. Get Frame Shop", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has success true', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.success).to.be.true;", "});", "", "pm.test('Response contains shop data', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.data).to.have.property('user_info');", "    pm.expect(responseJson.data).to.have.property('available_frames');", "    pm.expect(responseJson.data).to.have.property('locked_frames');", "    pm.expect(responseJson.data.user_info).to.have.property('kristal_balance');", "});", "", "pm.test('Available frames have shop data', function () {", "    const responseJson = pm.response.json();", "    if (responseJson.data.available_frames.length > 0) {", "        const frame = responseJson.data.available_frames[0];", "        pm.expect(frame).to.have.property('shop_data');", "        pm.expect(frame.shop_data).to.have.property('kristal_price');", "        pm.expect(frame.shop_data).to.have.property('required_tier');", "        pm.expect(frame.shop_data).to.have.property('min_level');", "    }", "});", "", "// Store first available frame for purchase test", "const responseJson = pm.response.json();", "if (responseJson.data.available_frames.length > 0) {", "    const firstFrame = responseJson.data.available_frames[0];", "    pm.environment.set('test_frame_id', firstFrame.frame_id);", "    pm.environment.set('test_frame_price', firstFrame.shop_data.kristal_price);", "    pm.environment.set('test_frame_name', firstFrame.frame_name);", "    console.log('Set test frame:', firstFrame.frame_name, 'Price:', firstFrame.shop_data.kristal_price);", "}"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/avatar/frames/shop", "host": ["{{base_url}}"], "path": ["api", "avatar", "frames", "shop"]}}, "response": []}, {"name": "2. <PERSON> User <PERSON>", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has currency data', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.success).to.be.true;", "    pm.expect(responseJson.data).to.have.property('kristal_balance');", "});", "", "// Store current balance", "const responseJson = pm.response.json();", "pm.environment.set('current_kristal_balance', responseJson.data.kristal_balance);", "console.log('Current Kristal balance:', responseJson.data.kristal_balance);"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/currency/balance", "host": ["{{base_url}}"], "path": ["api", "currency", "balance"]}}, "response": []}, {"name": "3. <PERSON><PERSON> (if needed)", "event": [{"listen": "prerequest", "script": {"exec": ["// Check if user has enough Kris<PERSON>", "const currentBalance = parseInt(pm.environment.get('current_kristal_balance') || '0');", "const framePrice = parseInt(pm.environment.get('test_frame_price') || '100');", "", "if (currentBalance < framePrice) {", "    console.log('Need to add <PERSON><PERSON>. Current:', currentBalance, 'Required:', framePrice);", "    pm.environment.set('kristal_to_add', framePrice - currentBalance + 50); // Add extra", "} else {", "    console.log('Sufficient Kristal balance');", "    pm.environment.set('kristal_to_add', 0);", "}"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["const kristalToAdd = parseInt(pm.environment.get('kristal_to_add') || '0');", "", "if (kristalToAdd > 0) {", "    pm.test('Status code is 200', function () {", "        pm.response.to.have.status(200);", "    });", "    ", "    pm.test('<PERSON><PERSON> added successfully', function () {", "        const responseJson = pm.response.json();", "        pm.expect(responseJson.success).to.be.true;", "    });", "} else {", "    pm.test('Skipped - sufficient balance', function () {", "        pm.expect(true).to.be.true;", "    });", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"currency_code\": \"KRIS\",\n    \"amount\": {{kristal_to_add}},\n    \"source_type\": \"ADMIN_GRANT\",\n    \"description\": \"Test Kristal for frame purchase\"\n}"}, "url": {"raw": "{{base_url}}/api/currency/award", "host": ["{{base_url}}"], "path": ["api", "currency", "award"]}}, "response": []}, {"name": "4. <PERSON><PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Frame purchased successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.success).to.be.true;", "    pm.expect(responseJson.message).to.include('Successfully purchased');", "});", "", "pm.test('Response contains purchase data', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.data).to.have.property('frame');", "    pm.expect(responseJson.data).to.have.property('cost');", "    pm.expect(responseJson.data).to.have.property('remaining_balance');", "    pm.expect(responseJson.data).to.have.property('inventory_item');", "});", "", "// Store purchased frame info", "const responseJson = pm.response.json();", "pm.environment.set('purchased_frame_id', responseJson.data.frame.frame_id);", "console.log('Purchased frame:', responseJson.data.frame.frame_name);"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"frame_id\": {{test_frame_id}}\n}"}, "url": {"raw": "{{base_url}}/api/avatar/frames/purchase", "host": ["{{base_url}}"], "path": ["api", "avatar", "frames", "purchase"]}}, "response": []}, {"name": "5. <PERSON><PERSON><PERSON> in Inventory", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('<PERSON>ame appears in owned items', function () {", "    const responseJson = pm.response.json();", "    const purchasedFrameId = parseInt(pm.environment.get('purchased_frame_id'));", "    ", "    pm.expect(responseJson.success).to.be.true;", "    pm.expect(responseJson.data).to.have.property('frames');", "    ", "    const ownedFrames = responseJson.data.frames.owned || [];", "    const frameFound = ownedFrames.some(frame => frame.frame_id === purchasedFrameId);", "    ", "    pm.expect(frameFound).to.be.true;", "});", "", "pm.test('Frame no longer in shop category', function () {", "    const responseJson = pm.response.json();", "    const purchasedFrameId = parseInt(pm.environment.get('purchased_frame_id'));", "    ", "    const shopFrames = responseJson.data.frames.shop || [];", "    const frameInShop = shopFrames.some(frame => frame.frame_id === purchasedFrameId);", "    ", "    pm.expect(frameInShop).to.be.false;", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/avatar/available-items", "host": ["{{base_url}}"], "path": ["api", "avatar", "available-items"]}}, "response": []}, {"name": "6. Test Duplicate Purchase (Should Fail)", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 400 (Bad Request)', function () {", "    pm.response.to.have.status(400);", "});", "", "pm.test('Error message indicates already owned', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.success).to.be.false;", "    pm.expect(responseJson.message).to.include('already owns');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"frame_id\": {{test_frame_id}}\n}"}, "url": {"raw": "{{base_url}}/api/avatar/frames/purchase", "host": ["{{base_url}}"], "path": ["api", "avatar", "frames", "purchase"]}}, "response": []}]}