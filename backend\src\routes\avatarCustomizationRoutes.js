'use strict';

const express = require('express');
const router = express.Router();
const AvatarCustomizationController = require('../controllers/avatarCustomizationController');
const FrameShopController = require('../controllers/frameShopController');
const { authenticateToken, authorize } = require('../middleware/authMiddleware');

// =====================================================
// AVATAR CUSTOMIZATION ROUTES
// =====================================================



router.post('/initialize',
    authenticateToken,
    AvatarCustomizationController.initializeAvatarSystem
);



router.get('/my-data',
    authenticateToken,
    AvatarCustomizationController.getUserAvatarData
);


router.get('/available-items',
    authenticateToken,
    AvatarCustomizationController.getAvailableItems
);



router.get('/inventory/:itemType',
    authenticateToken,
    AvatarCustomizationController.getUserInventoryByType
);



router.post('/equip',
    authenticateToken,
    AvatarCustomizationController.equipItem
);



router.post('/unequip',
    authenticateToken,
    AvatarCustomizationController.unequipItem
);



router.get('/customization',
    authenticateToken,
    AvatarCustomizationController.getUserCustomization
);



router.put('/customization',
    authenticateToken,
    AvatarCustomizationController.updateCustomizationSettings
);



// Route for getting display info for a specific user
router.get('/display-info/:userId',
    authenticateToken,
    AvatarCustomizationController.getUserDisplayInfo
);

// Route for getting display info for current user (no userId parameter)
router.get('/display-info',
    authenticateToken,
    AvatarCustomizationController.getUserDisplayInfo
);



router.get('/collection-progress',
    authenticateToken,
    AvatarCustomizationController.getCollectionProgress
);

// =====================================================
// BROWSING ROUTES (Public/Semi-Public)
// =====================================================



router.get('/avatars',
    authenticateToken,
    AvatarCustomizationController.getAllAvatars
);



router.get('/frames',
    authenticateToken,
    AvatarCustomizationController.getAllFrames
);



router.get('/emojis',
    authenticateToken,
    AvatarCustomizationController.getAllEmojis
);

// =====================================================
// FRAME SHOP ROUTES
// =====================================================

/**
 * @route   GET /api/avatar/frames/shop
 * @desc    Get frame shop data (purchasable frames for user's tier)
 * @access  Private
 */
router.get('/frames/shop',
    authenticateToken,
    FrameShopController.getFrameShop
);

/**
 * @route   POST /api/avatar/frames/purchase
 * @desc    Purchase frame with Kristal
 * @access  Private
 * @body    { frame_id }
 */
router.post('/frames/purchase',
    authenticateToken,
    FrameShopController.purchaseFrame
);


module.exports = router;
