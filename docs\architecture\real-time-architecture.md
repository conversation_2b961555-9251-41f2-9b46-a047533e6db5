# Real-time Architecture

## Socket.IO Implementation

**Server-side** (`backend/src/socket.js`):

- **Room Management**: Quiz-specific rooms
- **Event Handling**: Question distribution, answer collection
- **State Synchronization**: Redis-backed state management

**Client-side** (`frontend/src/lib/services/socket/`):

- **<PERSON>ton Pattern**: Single connection instance
- **Event Listeners**: Real-time quiz updates
- **Reconnection**: Manual reconnection logic

## Real-time Data Flow

```
Student → Frontend → Socket.IO → Backend → Redis → Database
   ↑                                ↓
   ←─────── Real-time Updates ←─────┘
```
