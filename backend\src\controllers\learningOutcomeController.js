const {
    QuizResult,
    UserQuestionHistory,
    Question,
    LO,
    Level,
    Quiz,
    Subject,
    User,
    Chapter,
    ChapterLO,
    ChapterSection
} = require('../models');
const { Op } = require('sequelize');
const {
    analyzeLOCompletionPercentage,
    createPersonalizedStudyPlan
} = require('../utils/learningAnalysisHelpers');

/**
 * LEARNING OUTCOME CONTROLLER
 * Chuyên xử lý phân tích Learning Outcomes theo % hoàn thành
 */

/**
 * API: Phân tích chi tiết LO theo % hoàn thành
 * GET /api/learning-outcomes/completion-analysis/:subject_id/:user_id
 */
const getLOCompletionAnalysis = async (req, res) => {
    try {
        const { subject_id, user_id } = req.params;
        const requestingUserId = req.user.user_id;
        const userRole = req.user.role;

        // Kiểm tra quyền truy cập
        if (userRole === 'student' && parseInt(user_id) !== requestingUserId) {
            return res.status(403).json({
                success: false,
                message: '<PERSON>ạn chỉ có thể xem phân tích của chính mình'
            });
        }

        // Lấy thông tin môn học
        const subject = await Subject.findByPk(subject_id);
        if (!subject) {
            return res.status(404).json({
                success: false,
                message: 'Không tìm thấy môn học'
            });
        }

        // Lấy thông tin sinh viên
        const student = await User.findByPk(user_id);
        if (!student) {
            return res.status(404).json({
                success: false,
                message: 'Không tìm thấy sinh viên'
            });
        }

        // Lấy tất cả quiz results của user trong môn học này
        const quizResults = await QuizResult.findAll({
            where: { user_id: user_id },
            include: [
                {
                    model: Quiz,
                    as: 'Quiz',
                    where: { subject_id: subject_id },
                    attributes: ['quiz_id', 'name', 'subject_id']
                }
            ]
        });

        if (quizResults.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'Không tìm thấy kết quả quiz nào cho môn học này'
            });
        }

        // Lấy quiz IDs
        const quizIds = quizResults.map(result => result.Quiz.quiz_id);

        // Lấy lịch sử trả lời câu hỏi chi tiết
        const questionHistory = await UserQuestionHistory.findAll({
            where: {
                user_id: user_id,
                quiz_id: { [Op.in]: quizIds }
            },
            include: [
                {
                    model: Question,
                    as: 'Question',
                    include: [
                        {
                            model: LO,
                            as: 'LO',
                            attributes: ['lo_id', 'name', 'description']
                        },
                        {
                            model: Level,
                            attributes: ['level_id', 'name']
                        }
                    ]
                }
            ]
        });

        if (questionHistory.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'Không tìm thấy lịch sử trả lời câu hỏi'
            });
        }

        // Phân tích LO theo % hoàn thành
        const loAnalysis = await analyzeLOCompletionPercentage(
            questionHistory, 
            subject_id, 
            60 // threshold 60%
        );

        // Tạo gợi ý học tập cá nhân hóa
        const learningRecommendations = createPersonalizedStudyPlan(loAnalysis);

        // Chuẩn bị response
        const response = {
            success: true,
            data: {
                subject_info: {
                    subject_id: subject.subject_id,
                    subject_name: subject.name,
                    description: subject.description || ''
                },
                student_info: {
                    user_id: student.user_id,
                    name: student.name
                },
                lo_analysis: loAnalysis,
                learning_recommendations: learningRecommendations,
                generated_at: new Date().toISOString()
            }
        };

        res.json(response);

    } catch (error) {
        console.error('Error in getLOCompletionAnalysis:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi server khi phân tích LO completion',
            error: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
};

/**
 * API: Lấy danh sách LO của một môn học
 * GET /api/learning-outcomes/subject/:subject_id
 */
const getLOsBySubject = async (req, res) => {
    try {
        const { subject_id } = req.params;

        // Lấy các LO thông qua Chapter
        const los = await LO.findAll({
            include: [
                {
                    model: Chapter,
                    as: 'Chapters',
                    through: { model: ChapterLO },
                    include: [
                        {
                            model: Subject,
                            as: 'Subject',
                            where: { subject_id: subject_id }
                        }
                    ]
                }
            ],
            distinct: true
        });

        res.json({
            success: true,
            data: {
                subject_id: parseInt(subject_id),
                learning_outcomes: los.map(lo => ({
                    lo_id: lo.lo_id,
                    name: lo.name,
                    description: lo.description || '',
                    related_chapters: lo.Chapters?.map(chapter => ({
                        chapter_id: chapter.chapter_id,
                        chapter_name: chapter.name
                    })) || []
                }))
            }
        });

    } catch (error) {
        console.error('Error in getLOsBySubject:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi server khi lấy danh sách LO',
            error: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
};

/**
 * API: Lấy chi tiết một LO cụ thể
 * GET /api/learning-outcomes/:lo_id/details
 */
const getLODetails = async (req, res) => {
    try {
        const { lo_id } = req.params;

        const lo = await LO.findByPk(lo_id, {
            include: [
                {
                    model: Chapter,
                    as: 'Chapters',
                    through: { model: ChapterLO },
                    include: [
                        {
                            model: ChapterSection,
                            as: 'Sections',
                            attributes: ['section_id', 'title', 'content', 'order']
                        },
                        {
                            model: Subject,
                            as: 'Subject',
                            attributes: ['subject_id', 'name']
                        }
                    ]
                }
            ]
        });

        if (!lo) {
            return res.status(404).json({
                success: false,
                message: 'Không tìm thấy Learning Outcome'
            });
        }

        res.json({
            success: true,
            data: {
                lo_id: lo.lo_id,
                name: lo.name,
                description: lo.description || '',
                related_chapters: lo.Chapters?.map(chapter => ({
                    chapter_id: chapter.chapter_id,
                    chapter_name: chapter.name,
                    chapter_description: chapter.description || '',
                    subject: chapter.Subject ? {
                        subject_id: chapter.Subject.subject_id,
                        subject_name: chapter.Subject.name
                    } : null,
                    sections: chapter.Sections?.map(section => ({
                        section_id: section.section_id,
                        section_name: section.title,
                        content_preview: section.content ? 
                            section.content.substring(0, 200) + '...' : '',
                        order: section.order
                    })).sort((a, b) => a.order - b.order) || []
                })) || []
            }
        });

    } catch (error) {
        console.error('Error in getLODetails:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi server khi lấy chi tiết LO',
            error: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
};

module.exports = {
    getLOCompletionAnalysis,
    getLOsBySubject,
    getLODetails
};
