-- =====================================================
-- UPDATE FRAMES TO MATCH FRONTEND REQUIREMENTS
-- =====================================================
-- 2 loại frame riêng biệt theo yêu cầu frontend:
-- 1. avatar-frame-pack: 6 Premium Frames (mua bằng Kristal)
-- 2. vector-ranks-pack: 10 Tier Rank Frames (auto unlock theo tier)

-- Clear existing frame data
DELETE FROM "UserInventory" WHERE item_type = 'FRAME';
DELETE FROM "AvatarFrames";

-- Reset frame sequence
ALTER SEQUENCE "AvatarFrames_frame_id_seq" RESTART WITH 1;

-- =====================================================
-- LOẠI 1: TIER RANK FRAMES (10 frames - auto unlock)
-- =====================================================
-- Từ vector-ranks-pack - tự động unlock theo tier progression

INSERT INTO "AvatarFrames" ("frame_name", "frame_code", "description", "image_path", "rarity", "unlock_type", "unlock_condition", "tier_name", "is_default", "sort_order") VALUES

-- Tier progression frames (auto unlock by level)
('Khung Gỗ', 'WOOD_FRAME', 'Khung gỗ cơ bản cho người mới', '/vector-ranks-pack/wood/diamond-wood-1.png', 'COMMON', 'DEFAULT', '{}', 'Wood', true, 1),
('Khung Đồng', 'BRONZE_FRAME', 'Khung đồng cho người chơi tích cực', '/vector-ranks-pack/bronze/diamond-bronze-1.png', 'COMMON', 'TIER', '{"level_range": [11, 20]}', 'Bronze', false, 2),
('Khung Bạc', 'SILVER_FRAME', 'Khung bạc cho người chơi giỏi', '/vector-ranks-pack/silver/diamond-silver-1.png', 'UNCOMMON', 'TIER', '{"level_range": [21, 30]}', 'Silver', false, 3),
('Khung Vàng', 'GOLD_FRAME', 'Khung vàng cho người chơi xuất sắc', '/vector-ranks-pack/gold/diamond-gold-1.png', 'UNCOMMON', 'TIER', '{"level_range": [31, 40]}', 'Gold', false, 4),
('Khung Bạch Kim', 'PLATINUM_FRAME', 'Khung bạch kim cho cao thủ', '/vector-ranks-pack/platinum/diamond-platinum-1.png', 'RARE', 'TIER', '{"level_range": [41, 50]}', 'Platinum', false, 5),
('Khung Onyx', 'ONYX_FRAME', 'Khung onyx cho chuyên gia', '/vector-ranks-pack/onyx/diamond-onyx-1.png', 'RARE', 'TIER', '{"level_range": [51, 60]}', 'Onyx', false, 6),
('Khung Sapphire', 'SAPPHIRE_FRAME', 'Khung sapphire cho bậc thầy', '/vector-ranks-pack/sapphire/diamond-sapphire-1.png', 'EPIC', 'TIER', '{"level_range": [61, 70]}', 'Sapphire', false, 7),
('Khung Ruby', 'RUBY_FRAME', 'Khung ruby cho siêu sao', '/vector-ranks-pack/ruby/diamond-ruby-1.png', 'EPIC', 'TIER', '{"level_range": [71, 80]}', 'Ruby', false, 8),
('Khung Amethyst', 'AMETHYST_FRAME', 'Khung amethyst cho huyền thoại', '/vector-ranks-pack/amethyst/diamond-amethyst-1.png', 'LEGENDARY', 'TIER', '{"level_range": [81, 90]}', 'Amethyst', false, 9),
('Khung Master', 'MASTER_FRAME', 'Khung master cho người vĩ đại nhất', '/vector-ranks-pack/master/diamond-master-1.png', 'LEGENDARY', 'TIER', '{"level_range": [91, 999]}', 'Master', false, 10);

-- =====================================================
-- LOẠI 2: PREMIUM FRAMES (6 frames - mua bằng Kristal)
-- =====================================================
-- Từ avatar-frame-pack - mua với Kristal + tier requirement

INSERT INTO "AvatarFrames" ("frame_name", "frame_code", "description", "image_path", "rarity", "unlock_type", "unlock_condition", "tier_name", "is_default", "sort_order") VALUES

-- Premium frames theo tier requirement + Kristal cost (theo game-mechanics docs)
('Khung Đại Dương', 'OCEAN_SONG', 'Khung bài ca đại dương - 120 Kristal', '/avatar-frame-pack/ocean-song-frame.png', 'RARE', 'SHOP', '{"kristal_price": 120, "required_tier": "Silver", "min_level": 25}', 'Silver', false, 11),
('Khung Lễ Hội', 'DRUMALONG_FEST', 'Khung lễ hội sôi động - 100 Kristal', '/avatar-frame-pack/drumalong-festival-frame.png', 'EPIC', 'SHOP', '{"kristal_price": 100, "required_tier": "Gold", "min_level": 37}', 'Gold', false, 12),
('Khung Sao Tím', 'VIOLET_STAR', 'Khung ánh sao tím - 250 Kristal', '/avatar-frame-pack/violet-starlight-frame.png', 'LEGENDARY', 'SHOP', '{"kristal_price": 250, "required_tier": "Platinum", "min_level": 49}', 'Platinum', false, 13),
('Khung Cyber', 'CYBER_GLITCH', 'Khung cyber tương lai - 150 Kristal', '/avatar-frame-pack/cyber-glitch-frame.png', 'LEGENDARY', 'SHOP', '{"kristal_price": 150, "required_tier": "Onyx", "min_level": 61}', 'Onyx', false, 14),
('Khung Lửa', 'NATION_PYRO', 'Khung ngọn lửa mạnh mẽ - 180 Kristal', '/avatar-frame-pack/nation-of-pyro-frame.png', 'EPIC', 'SHOP', '{"kristal_price": 180, "required_tier": "Sapphire", "min_level": 73}', 'Sapphire', false, 15),
('Khung Phượng Hoàng', 'CRIMSON_PHOENIX', 'Khung phượng hoàng đỏ rực - 200 Kristal', '/avatar-frame-pack/crimson-phoenix-frame.png', 'LEGENDARY', 'SHOP', '{"kristal_price": 200, "required_tier": "Ruby", "min_level": 85}', 'Ruby', false, 16);

-- =====================================================
-- VERIFICATION
-- =====================================================

-- Verify frame counts
SELECT 
    'FRAME UPDATE COMPLETED' as status,
    COUNT(*) as total_frames,
    COUNT(CASE WHEN unlock_type = 'TIER' OR unlock_type = 'DEFAULT' THEN 1 END) as tier_frames,
    COUNT(CASE WHEN unlock_type = 'SHOP' THEN 1 END) as premium_frames
FROM "AvatarFrames";

-- Show frame breakdown by type
SELECT 
    unlock_type,
    COUNT(*) as count,
    STRING_AGG(frame_name, ', ') as frame_names
FROM "AvatarFrames"
GROUP BY unlock_type
ORDER BY unlock_type;

-- Show tier progression frames
SELECT
    tier_name,
    unlock_condition,
    frame_name,
    image_path,
    unlock_type
FROM "AvatarFrames"
WHERE unlock_type IN ('TIER', 'DEFAULT')
ORDER BY sort_order;

-- Show premium shop frames
SELECT
    frame_name,
    tier_name as required_tier,
    unlock_condition,
    image_path,
    rarity
FROM "AvatarFrames"
WHERE unlock_type = 'SHOP'
ORDER BY sort_order;
