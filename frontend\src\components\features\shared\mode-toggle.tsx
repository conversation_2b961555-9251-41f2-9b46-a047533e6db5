import { <PERSON>, Sun } from "lucide-react";
import { useTheme } from "next-themes";

import { Button } from "@/components/ui/forms";

export function ModeToggle() {
  const { theme, setTheme } = useTheme();

  return (
    <Button
      variant="ghost"
      size="icon"
      onClick={() => setTheme(theme === "light" ? "dark" : "light")}
      className="rounded-md h-9 w-9 hover:bg-primary/5 hover:text-primary cursor-pointer"
    >
      <Sun className="h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
      <Moon className="absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
      <span className="sr-only"><PERSON>y<PERSON>n đ<PERSON><PERSON> giao di<PERSON>n</span>
    </Button>
  );
}
