import api from "./client";

export const subjectService = {
  // <PERSON><PERSON><PERSON> danh sách môn học
  getSubjects: async () => {
    const response = await api.get("/subjects");
    return response.data;
  },

  // L<PERSON><PERSON> chi tiết môn học
  getSubjectById: async (subjectId: number) => {
    const response = await api.get(`/subjects/${subjectId}`);
    return response.data;
  },

  // L<PERSON>y danh sách mục tiêu học tập theo môn học
  getLOsBySubject: async (subjectId: number) => {
    const response = await api.get(`/los/subject/${subjectId}`);
    return response.data;
  },
};

export default subjectService;
