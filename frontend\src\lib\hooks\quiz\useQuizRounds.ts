import { useState, useCallback, useEffect } from "react";
import {
  Question,
  QuestionRoundState,
  QuizRound,
  QuestionWrongHistory,
} from "@/lib/types/quiz";

interface UseQuizRoundsProps {
  questions: Question[];
  onQuizComplete: (finalScore: number) => void;
  quizId: number;
  onStateChange?: (state: {
    currentRound: number;
    questionsInCurrentRound: number[];
    questionStates: Record<number, QuestionRoundState>;
    questionWrongHistory: Record<number, QuestionWrongHistory>;
    roundHistory: Array<{
      round: number;
      questionsAttempted: number[];
      correctAnswers: number;
      incorrectAnswers: number;
    }>;
    showRoundTransition: boolean;
    canNavigateBack: boolean;
  }) => void;
  initialState?: {
    currentRound?: number;
    questionsInCurrentRound?: number[];
    questionStates?: Record<number, QuestionRoundState>;
    questionWrongHistory?: Record<number, QuestionWrongHistory>;
    roundHistory?: Array<{
      round: number;
      questionsAttempted: number[];
      correctAnswers: number;
      incorrectAnswers: number;
    }>;
    showRoundTransition?: boolean;
    canNavigateBack?: boolean;
  };
}

interface UseQuizRoundsReturn {
  // Round state
  currentRound: number;
  questionsInCurrentRound: number[];
  questionStates: Record<number, QuestionRoundState>;
  roundHistory: Array<{
    round: number;
    questionsAttempted: number[];
    correctAnswers: number;
    incorrectAnswers: number;
  }>;
  showRoundTransition: boolean;
  canNavigateBack: boolean;

  // Round management
  getRoundConfig: (round: number) => QuizRound;
  moveToNextRound: (
    updatedQuestionStates?: Record<number, QuestionRoundState>
  ) => void;
  handleMoveToNextRound: () => void;
  isCurrentRoundCompleted: () => boolean;
  checkQuizCompletion: () => boolean;

  // Question state management
  updateQuestionState: (
    questionIndex: number,
    selectedAnswer: number,
    isCorrect: boolean,
    currentRound: number
  ) => void;

  // UI state
  setShowRoundTransition: (show: boolean) => void;
}

export const useQuizRounds = ({
  questions,
  onQuizComplete,
  onStateChange,
  initialState,
}: UseQuizRoundsProps): UseQuizRoundsReturn => {
  // State cho hệ thống vòng với khả năng khôi phục từ initialState
  const [currentRound, setCurrentRound] = useState<number>(
    initialState?.currentRound ?? 1
  );
  const [questionsInCurrentRound, setQuestionsInCurrentRound] = useState<
    number[]
  >(initialState?.questionsInCurrentRound ?? []);

  // State mới: theo dõi lịch sử lựa chọn sai của từng câu hỏi
  const [questionWrongHistory, setQuestionWrongHistory] = useState<
    Record<number, QuestionWrongHistory>
  >(initialState?.questionWrongHistory ?? {});

  const [questionStates, setQuestionStates] = useState<
    Record<number, QuestionRoundState>
  >(initialState?.questionStates ?? {});
  const [roundHistory, setRoundHistory] = useState<
    Array<{
      round: number;
      questionsAttempted: number[];
      correctAnswers: number;
      incorrectAnswers: number;
    }>
  >(initialState?.roundHistory ?? []);
  const [showRoundTransition, setShowRoundTransition] = useState<boolean>(
    initialState?.showRoundTransition ?? false
  );
  const [canNavigateBack, setCanNavigateBack] = useState<boolean>(
    initialState?.canNavigateBack ?? true
  );

  // Helper functions cho round system
  const getRoundConfig = useCallback((round: number): QuizRound => {
    if (round === 1) {
      return {
        round: 1,
        name: "Vòng 1",
        description: "Làm tất cả câu hỏi, không thể quay lại",
        allowBackNavigation: false,
        trackIncorrectAnswers: true,
      };
    } else if (round === 2) {
      return {
        round: 2,
        name: "Vòng 2",
        description: "Làm lại câu sai và câu chưa làm (reset trạng thái)",
        allowBackNavigation: false,
        trackIncorrectAnswers: false,
      };
    } else {
      return {
        round,
        name: `Vòng ${round}`,
        description: "Làm lại câu chưa làm (reset trạng thái)",
        allowBackNavigation: false,
        trackIncorrectAnswers: false,
      };
    }
  }, []);

  const moveToNextRound = useCallback(
    (updatedQuestionStates?: Record<number, QuestionRoundState>) => {
      console.log("=== MOVING TO NEXT ROUND ===");
      console.log("Current round:", currentRound);
      console.log("Current round questions:", questionsInCurrentRound);

      // Sử dụng state được truyền vào hoặc state hiện tại
      const stateToUse = updatedQuestionStates || questionStates;
      console.log("Question states:", stateToUse);

      // Nếu có updatedQuestionStates, cập nhật state hiện tại trước
      if (updatedQuestionStates) {
        setQuestionStates(updatedQuestionStates);
      }

      // Lưu lịch sử vòng hiện tại
      const currentRoundQuestions = questionsInCurrentRound;
      const correctCount = currentRoundQuestions.filter(
        (index) => stateToUse[index]?.isCorrect === true
      ).length;
      const incorrectCount = currentRoundQuestions.filter(
        (index) => stateToUse[index]?.isCorrect === false
      ).length;

      setRoundHistory((prev) => [
        ...prev,
        {
          round: currentRound,
          questionsAttempted: currentRoundQuestions,
          correctAnswers: correctCount,
          incorrectAnswers: incorrectCount,
        },
      ]);

      // Tính toán câu hỏi cho vòng tiếp theo với logic mới
      const nextRound = currentRound + 1;
      const questionsForNewRound: number[] = [];

      // Cập nhật lịch sử lựa chọn sai TRƯỚC khi tính toán vòng tiếp theo
      const updatedWrongHistory = { ...questionWrongHistory };

      // Duyệt qua các câu hỏi trong vòng hiện tại để cập nhật lịch sử
      currentRoundQuestions.forEach((questionIndex) => {
        const state = stateToUse[questionIndex];
        if (
          state &&
          state.isAnswered &&
          state.isCorrect === false &&
          state.selectedAnswer !== null
        ) {
          const currentHistory = updatedWrongHistory[questionIndex] || {
            questionIndex,
            wrongAnswers: new Set(),
            lastWrongRound: 0,
          };

          // Thêm đáp án sai vào lịch sử
          const newWrongAnswers = new Set(currentHistory.wrongAnswers);
          newWrongAnswers.add(state.selectedAnswer);

          updatedWrongHistory[questionIndex] = {
            questionIndex,
            wrongAnswers: newWrongAnswers,
            lastWrongRound: currentRound,
          };

          console.log(
            `Updated wrong history for question ${questionIndex}: added answer ${state.selectedAnswer}`
          );
        }
      });

      // Duyệt qua tất cả câu hỏi để tìm câu cần hiển thị ở vòng tiếp theo
      questions.forEach((_, questionIndex) => {
        const state = stateToUse[questionIndex];
        const wrongHistory = questionWrongHistory[questionIndex]; // Sử dụng lịch sử CŨ (trước khi cập nhật)

        // Nếu câu hỏi chưa được trả lời
        if (!state || !state.isAnswered) {
          questionsForNewRound.push(questionIndex);
          return;
        }

        // Nếu câu hỏi đã trả lời đúng, không cần hiển thị lại
        if (state.isCorrect === true) {
          return;
        }

        // Nếu câu hỏi trả lời sai
        if (state.isCorrect === false && state.selectedAnswer !== null) {
          // Kiểm tra xem đáp án sai này đã từng chọn trước đó chưa (trong lịch sử CŨ)
          const hasTriedThisAnswerBefore =
            wrongHistory && wrongHistory.wrongAnswers.has(state.selectedAnswer);

          if (!hasTriedThisAnswerBefore) {
            // Đây là lựa chọn sai mới, cần hiển thị lại ở vòng tiếp theo
            questionsForNewRound.push(questionIndex);
            console.log(
              `Question ${questionIndex}: New wrong answer ${state.selectedAnswer}, will show in next round`
            );
          } else {
            console.log(
              `Question ${questionIndex}: Already tried wrong answer ${state.selectedAnswer} before, will NOT show in next round`
            );
          }
        }
      });

      // Cập nhật lịch sử lựa chọn sai
      setQuestionWrongHistory(updatedWrongHistory);

      console.log(`Questions for round ${nextRound}:`, questionsForNewRound);

      // Kiểm tra xem có câu hỏi nào cho vòng tiếp theo không
      if (questionsForNewRound.length === 0) {
        console.log("No questions for next round - completing quiz");
        // Tất cả câu hỏi đã được hoàn thành
        const totalCorrectAnswers = Object.values(stateToUse).filter(
          (state) => state.isCorrect === true
        ).length;
        const finalScorePercent = Math.round(
          (totalCorrectAnswers / questions.length) * 100
        );
        console.log("Quiz completed! Final score:", finalScorePercent);
        onQuizComplete(finalScorePercent);
        return;
      }

      // Chuyển sang vòng tiếp theo
      setCurrentRound(nextRound);
      setShowRoundTransition(true);

      // Khởi tạo vòng mới sau animation
      setTimeout(() => {
        console.log(
          "Setting up new round with questions:",
          questionsForNewRound
        );

        setQuestionsInCurrentRound(questionsForNewRound);
        setCanNavigateBack(false); // Tất cả vòng đều không cho phép quay lại

        // Reset trạng thái trả lời cho các câu hỏi trong vòng mới
        if (nextRound > 1) {
          setQuestionStates((prev) => {
            const newStates = { ...prev };
            questionsForNewRound.forEach((index) => {
              // Reset trạng thái trả lời cho tất cả câu hỏi trong vòng mới
              // Nhưng giữ nguyên lịch sử lựa chọn sai
              newStates[index] = {
                questionIndex: index,
                isAnswered: false,
                isCorrect: null,
                selectedAnswer: null,
                roundAnswered: 0,
              };
            });
            console.log("Reset question states for round", nextRound);
            return newStates;
          });
        }

        setShowRoundTransition(false);
      }, 2000);
    },
    [
      currentRound,
      questionsInCurrentRound,
      questionStates,
      questionWrongHistory,
      questions,
    ]
  );

  // Kiểm tra hoàn thành toàn bộ quiz
  const checkQuizCompletion = useCallback(() => {
    if (questions.length === 0) return false;

    // Kiểm tra xem tất cả câu hỏi đã được trả lời ĐÚNG chưa
    const allQuestionsCorrect = questions.every(
      (_, index) => questionStates[index]?.isCorrect === true
    );

    console.log("Checking quiz completion:");
    console.log("All questions correct:", allQuestionsCorrect);
    console.log(
      "Question states:",
      Object.fromEntries(
        Object.entries(questionStates).map(([key, value]) => [
          key,
          { isAnswered: value.isAnswered, isCorrect: value.isCorrect },
        ])
      )
    );

    if (allQuestionsCorrect) {
      const totalCorrectAnswers = Object.values(questionStates).filter(
        (state) => state.isCorrect === true
      ).length;
      const finalScorePercent = Math.round(
        (totalCorrectAnswers / questions.length) * 100
      );
      console.log(
        "Quiz completed! All questions correct. Final score:",
        finalScorePercent
      );
      onQuizComplete(finalScorePercent);
      return true;
    }

    return false;
  }, [questions, questionStates, onQuizComplete]);

  // Kiểm tra xem vòng hiện tại đã hoàn thành chưa
  const isCurrentRoundCompleted = useCallback(() => {
    if (questionsInCurrentRound.length === 0) return false;

    return questionsInCurrentRound.every(
      (index) => questionStates[index]?.isAnswered === true
    );
  }, [questionsInCurrentRound, questionStates]);

  // Chuẩn bị vòng tiếp theo với logic mới - đơn giản hóa
  const prepareNextRound = useCallback(() => {
    console.log("Preparing next round from round", currentRound);
    console.log("Current round questions:", questionsInCurrentRound);
    console.log("Question states:", questionStates);
    console.log("Question wrong history:", questionWrongHistory);

    // Tìm các câu hỏi cần hiển thị ở vòng tiếp theo
    const questionsForNewRound: number[] = [];

    questions.forEach((_, questionIndex) => {
      const state = questionStates[questionIndex];
      const wrongHistory = questionWrongHistory[questionIndex];

      // Nếu câu hỏi chưa được trả lời
      if (!state || !state.isAnswered) {
        questionsForNewRound.push(questionIndex);
        return;
      }

      // Nếu câu hỏi đã trả lời đúng, không cần hiển thị lại
      if (state.isCorrect === true) {
        return;
      }

      // Nếu câu hỏi trả lời sai
      if (state.isCorrect === false && state.selectedAnswer !== null) {
        // Kiểm tra xem đáp án sai này đã từng chọn trước đó chưa
        if (
          !wrongHistory ||
          !wrongHistory.wrongAnswers.has(state.selectedAnswer)
        ) {
          // Đây là lựa chọn sai mới, cần hiển thị lại ở vòng tiếp theo
          questionsForNewRound.push(questionIndex);
        }
      }
    });

    console.log("Questions for next round:", questionsForNewRound);
    return questionsForNewRound.length > 0;
  }, [
    currentRound,
    questionsInCurrentRound,
    questionStates,
    questionWrongHistory,
    questions,
  ]);

  // Xử lý chuyển vòng
  const handleMoveToNextRound = useCallback(() => {
    // Kiểm tra xem có thể hoàn thành quiz không
    if (checkQuizCompletion()) {
      return;
    }

    // Chuẩn bị vòng tiếp theo
    const hasMoreQuestions = prepareNextRound();

    if (hasMoreQuestions) {
      moveToNextRound();
    } else {
      // Hoàn thành toàn bộ quiz
      const totalCorrectAnswers = Object.values(questionStates).filter(
        (state) => state.isCorrect === true
      ).length;
      const finalScorePercent = Math.round(
        (totalCorrectAnswers / questions.length) * 100
      );
      onQuizComplete(finalScorePercent);
    }
  }, [
    checkQuizCompletion,
    prepareNextRound,
    moveToNextRound,
    questionStates,
    questions.length,
    onQuizComplete,
  ]);

  // Cập nhật trạng thái câu hỏi (không cập nhật lịch sử ở đây nữa)
  const updateQuestionState = useCallback(
    (
      questionIndex: number,
      selectedAnswer: number,
      isCorrect: boolean,
      currentRound: number
    ) => {
      // Chỉ cập nhật trạng thái câu hỏi
      setQuestionStates((prev) => ({
        ...prev,
        [questionIndex]: {
          questionIndex,
          isAnswered: true,
          isCorrect,
          selectedAnswer,
          roundAnswered: currentRound,
        },
      }));

      console.log(
        `Question ${questionIndex}: Updated state - answer: ${selectedAnswer}, correct: ${isCorrect}`
      );
    },
    []
  );

  // Gọi onStateChange khi state thay đổi để lưu vào localStorage
  useEffect(() => {
    if (onStateChange) {
      onStateChange({
        currentRound,
        questionsInCurrentRound,
        questionStates,
        questionWrongHistory,
        roundHistory,
        showRoundTransition,
        canNavigateBack,
      });
    }
  }, [
    currentRound,
    questionsInCurrentRound,
    questionStates,
    questionWrongHistory,
    roundHistory,
    showRoundTransition,
    canNavigateBack,
    onStateChange,
  ]);

  // Khởi tạo vòng 1 khi questions được load (chỉ khi chưa có initialState)
  useEffect(() => {
    if (
      questions.length > 0 &&
      currentRound === 1 &&
      questionsInCurrentRound.length === 0 &&
      !initialState?.questionsInCurrentRound?.length
    ) {
      console.log("Initializing round 1 with", questions.length, "questions");

      // Vòng 1: tất cả câu hỏi
      const allQuestions = Array.from(
        { length: questions.length },
        (_, i) => i
      );
      setQuestionsInCurrentRound(allQuestions);

      // Khởi tạo question states
      const initialStates: Record<number, QuestionRoundState> = {};
      allQuestions.forEach((index) => {
        initialStates[index] = {
          questionIndex: index,
          isAnswered: false,
          isCorrect: null,
          selectedAnswer: null,
          roundAnswered: 0,
        };
      });
      setQuestionStates(initialStates);

      // Khởi tạo lịch sử lựa chọn sai rỗng
      setQuestionWrongHistory({});

      setCanNavigateBack(false); // Vòng 1 không cho phép quay lại
    }
  }, [
    questions.length,
    currentRound,
    questionsInCurrentRound.length,
    initialState,
  ]);

  return {
    // Round state
    currentRound,
    questionsInCurrentRound,
    questionStates,
    roundHistory,
    showRoundTransition,
    canNavigateBack,

    // Round management
    getRoundConfig,
    moveToNextRound,
    handleMoveToNextRound,
    isCurrentRoundCompleted,
    checkQuizCompletion,

    // Question state management
    updateQuestionState,

    // UI state
    setShowRoundTransition,
  };
};
