const { Avatar<PERSON>rame, UserInventory, UserCurrency, CurrencyTransaction, User, LevelRequirement } = require('../models');

class FrameShopService {
    // =====================================================
    // FRAME SHOP METHODS
    // =====================================================

    /**
     * Get frame shop data for user
     * @param {number} userId - User ID
     * @returns {Object} Shop data with purchasable frames
     */
    static async getFrameShop(userId) {
        try {
            // Get user level and tier
            const user = await User.findByPk(userId, {
                attributes: ['current_level', 'total_points']
            });

            if (!user) {
                return {
                    success: false,
                    message: 'User not found'
                };
            }

            // Calculate tier from level
            const levelInfo = await LevelRequirement.calculateLevelFromXP(user.total_points);
            const currentTier = levelInfo.tier_info ? levelInfo.tier_info.tier_name : 'Wood';
            const currentLevel = user.current_level;

            // Get user's current Kristal balance
            const userCurrency = await UserCurrency.findOne({
                where: { user_id: userId }
            });

            const kristalBalance = userCurrency ? userCurrency.kristal_balance : 0;

            // Get all shop frames
            const shopFrames = await AvatarFrame.findAll({
                where: { 
                    unlock_type: 'SHOP',
                    is_active: true 
                },
                order: [['sort_order', 'ASC']]
            });

            // Get user's owned frames
            const ownedFrames = await UserInventory.findAll({
                where: {
                    user_id: userId,
                    item_type: 'FRAME'
                },
                attributes: ['item_id']
            });

            const ownedFrameIds = ownedFrames.map(frame => frame.item_id);

            // Process shop frames
            const availableFrames = [];
            const lockedFrames = [];

            for (const frame of shopFrames) {
                const frameData = frame.getFormattedInfo();
                const unlockCondition = frame.unlock_condition || {};
                
                // Extract shop data from unlock_condition
                const kristalPrice = unlockCondition.kristal_price || 0;
                const requiredTier = unlockCondition.required_tier || 'Wood';
                const minLevel = unlockCondition.min_level || 1;

                // Check if user already owns this frame
                const isOwned = ownedFrameIds.includes(frame.frame_id);

                // Check if user meets requirements
                const meetsLevelReq = currentLevel >= minLevel;
                const meetsTierReq = this.compareTiers(currentTier, requiredTier) >= 0;
                const canAfford = kristalBalance >= kristalPrice;

                const shopFrameData = {
                    ...frameData,
                    shop_data: {
                        kristal_price: kristalPrice,
                        required_tier: requiredTier,
                        min_level: minLevel,
                        is_owned: isOwned,
                        can_purchase: !isOwned && meetsLevelReq && meetsTierReq && canAfford,
                        requirements_met: {
                            level: meetsLevelReq,
                            tier: meetsTierReq,
                            balance: canAfford
                        }
                    }
                };

                if (isOwned || (!meetsLevelReq || !meetsTierReq)) {
                    lockedFrames.push(shopFrameData);
                } else {
                    availableFrames.push(shopFrameData);
                }
            }

            return {
                success: true,
                message: 'Frame shop data retrieved successfully',
                data: {
                    user_info: {
                        current_level: currentLevel,
                        current_tier: currentTier,
                        kristal_balance: kristalBalance
                    },
                    available_frames: availableFrames,
                    locked_frames: lockedFrames,
                    total_frames: shopFrames.length
                }
            };
        } catch (error) {
            console.error('Error getting frame shop:', error);
            return {
                success: false,
                message: 'Failed to get frame shop data',
                error: error.message
            };
        }
    }

    /**
     * Purchase frame with Kristal
     * @param {number} userId - User ID
     * @param {number} frameId - Frame ID to purchase
     * @returns {Object} Purchase result
     */
    static async purchaseFrameWithKristal(userId, frameId) {
        try {
            // Get frame details
            const frame = await AvatarFrame.findByPk(frameId);

            if (!frame || frame.unlock_type !== 'SHOP' || !frame.is_active) {
                return {
                    success: false,
                    message: 'Frame is not available for purchase'
                };
            }

            // Check if user already owns this frame
            const existingFrame = await UserInventory.findOne({
                where: {
                    user_id: userId,
                    item_type: 'FRAME',
                    item_id: frameId
                }
            });

            if (existingFrame) {
                return {
                    success: false,
                    message: 'User already owns this frame'
                };
            }

            // Get unlock condition data
            const unlockCondition = frame.unlock_condition || {};
            const kristalPrice = unlockCondition.kristal_price || 0;
            const requiredTier = unlockCondition.required_tier || 'Wood';
            const minLevel = unlockCondition.min_level || 1;

            if (kristalPrice <= 0) {
                return {
                    success: false,
                    message: 'Frame price not configured'
                };
            }

            // Get user data
            const user = await User.findByPk(userId, {
                attributes: ['current_level', 'total_points']
            });

            if (!user) {
                return {
                    success: false,
                    message: 'User not found'
                };
            }

            // Check level requirement
            if (user.current_level < minLevel) {
                return {
                    success: false,
                    message: `Requires level ${minLevel} or higher`
                };
            }

            // Check tier requirement
            const levelInfo = await LevelRequirement.calculateLevelFromXP(user.total_points);
            const currentTier = levelInfo.tier_info ? levelInfo.tier_info.tier_name : 'Wood';
            
            if (this.compareTiers(currentTier, requiredTier) < 0) {
                return {
                    success: false,
                    message: `Requires ${requiredTier} tier or higher`
                };
            }

            // Check user's Kristal balance
            const userCurrency = await UserCurrency.findOne({
                where: { user_id: userId }
            });

            if (!userCurrency || userCurrency.kristal_balance < kristalPrice) {
                return {
                    success: false,
                    message: 'Insufficient Kristal balance'
                };
            }

            // Deduct Kristal and add frame to inventory
            await userCurrency.decrement('kristal_balance', { by: kristalPrice });

            // Add frame to user inventory
            const userInventoryItem = await UserInventory.create({
                user_id: userId,
                item_type: 'FRAME',
                item_id: frameId,
                obtained_from: 'KRISTAL_PURCHASE',
                obtained_at: new Date()
            });

            // Record transaction
            await CurrencyTransaction.create({
                user_id: userId,
                transaction_type: 'SPEND',
                currency_type: 'KRISTAL',
                amount: kristalPrice,
                balance_after: userCurrency.kristal_balance - kristalPrice,
                source: 'FRAME_PURCHASE',
                metadata: {
                    frame_id: frameId,
                    frame_name: frame.frame_name,
                    frame_code: frame.frame_code
                }
            });

            return {
                success: true,
                message: `Successfully purchased ${frame.frame_name}`,
                data: {
                    frame: frame.getFormattedInfo(),
                    cost: kristalPrice,
                    remaining_balance: userCurrency.kristal_balance - kristalPrice,
                    inventory_item: {
                        inventory_id: userInventoryItem.inventory_id,
                        obtained_from: userInventoryItem.obtained_from,
                        obtained_at: userInventoryItem.obtained_at
                    }
                }
            };
        } catch (error) {
            console.error('Error purchasing frame with Kristal:', error);
            return {
                success: false,
                message: 'Failed to purchase frame',
                error: error.message
            };
        }
    }

    /**
     * Compare tier levels (helper method)
     * @param {string} userTier - User's current tier
     * @param {string} requiredTier - Required tier
     * @returns {number} -1 if user tier is lower, 0 if equal, 1 if higher
     */
    static compareTiers(userTier, requiredTier) {
        const tierOrder = ['Wood', 'Bronze', 'Silver', 'Gold', 'Platinum', 'Onyx', 'Sapphire', 'Ruby', 'Amethyst', 'Master'];
        const userIndex = tierOrder.indexOf(userTier);
        const requiredIndex = tierOrder.indexOf(requiredTier);
        
        if (userIndex === -1 || requiredIndex === -1) return 0;
        
        return userIndex - requiredIndex;
    }
}

module.exports = FrameShopService;
