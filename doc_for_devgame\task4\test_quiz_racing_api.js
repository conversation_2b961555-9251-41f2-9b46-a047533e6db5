// doc_for_devgame/task4/test_quiz_racing_api.js
// Test script for Quiz Racing API endpoints

const axios = require('axios');
const io = require('socket.io-client');

const BASE_URL = 'http://localhost:3000/api';
const SOCKET_URL = 'http://localhost:3000';

// Test credentials
const testUsers = [
    { username: 'student1', password: 'password123' },
    { username: 'student2', password: 'password123' },
    { username: 'student3', password: 'password123' }
];

let authTokens = {};
let testQuizId = null;
let testSessionId = null;

// =====================================================
// AUTHENTICATION FUNCTIONS
// =====================================================

async function loginUser(username, password) {
    try {
        const response = await axios.post(`${BASE_URL}/auth/login`, {
            username,
            password
        });
        
        if (response.data.success) {
            console.log(`✅ Login successful for ${username}`);
            return response.data.data.token;
        } else {
            console.log(`❌ Login failed for ${username}:`, response.data.message);
            return null;
        }
    } catch (error) {
        console.log(`❌ Login error for ${username}:`, error.response?.data?.message || error.message);
        return null;
    }
}

async function loginAllUsers() {
    console.log('\n🔐 === AUTHENTICATION TESTS ===');
    
    for (const user of testUsers) {
        const token = await loginUser(user.username, user.password);
        if (token) {
            authTokens[user.username] = token;
        }
    }
    
    console.log(`\n📊 Logged in users: ${Object.keys(authTokens).length}/${testUsers.length}`);
    return Object.keys(authTokens).length >= 2; // Need at least 2 users for racing
}

// =====================================================
// QUIZ RACING API TESTS
// =====================================================

async function testInitializeQuizRacing() {
    console.log('\n🏁 === INITIALIZE QUIZ RACING TEST ===');
    
    try {
        // Get available quiz (assuming quiz ID 1 exists)
        testQuizId = 1;
        
        // Prepare participants
        const participants = Object.keys(authTokens).slice(0, 3).map((username, index) => ({
            user_id: index + 1, // Assuming user IDs 1, 2, 3
            username: username
        }));
        
        const token = authTokens[Object.keys(authTokens)[0]];
        
        const response = await axios.post(`${BASE_URL}/quiz-racing/initialize`, {
            quiz_id: testQuizId,
            participants: participants
        }, {
            headers: { Authorization: `Bearer ${token}` }
        });
        
        if (response.data.success) {
            testSessionId = response.data.data.session_id;
            console.log('✅ Quiz racing session initialized successfully');
            console.log(`📋 Session ID: ${testSessionId}`);
            console.log(`👥 Participants: ${participants.length}`);
            return true;
        } else {
            console.log('❌ Failed to initialize quiz racing:', response.data.message);
            return false;
        }
    } catch (error) {
        console.log('❌ Initialize quiz racing error:', error.response?.data?.message || error.message);
        return false;
    }
}

async function testGetSessionData() {
    console.log('\n📊 === GET SESSION DATA TEST ===');
    
    if (!testSessionId) {
        console.log('❌ No session ID available');
        return false;
    }
    
    try {
        const token = authTokens[Object.keys(authTokens)[0]];
        
        const response = await axios.get(`${BASE_URL}/quiz-racing/session/${testSessionId}`, {
            headers: { Authorization: `Bearer ${token}` }
        });
        
        if (response.data.success) {
            console.log('✅ Session data retrieved successfully');
            console.log(`📋 Participants: ${response.data.data.participants.length}`);
            console.log(`🎯 Total questions: ${response.data.data.total_questions}`);
            return true;
        } else {
            console.log('❌ Failed to get session data:', response.data.message);
            return false;
        }
    } catch (error) {
        console.log('❌ Get session data error:', error.response?.data?.message || error.message);
        return false;
    }
}

async function testSetSkillLoadout() {
    console.log('\n⚔️ === SET SKILL LOADOUT TEST ===');
    
    if (!testSessionId) {
        console.log('❌ No session ID available');
        return false;
    }
    
    try {
        const token = authTokens[Object.keys(authTokens)[0]];
        
        // Test with skill IDs 1, 2, 3, 4 (assuming they exist)
        const skillIds = [1, 2, 3, 4];
        
        const response = await axios.post(`${BASE_URL}/quiz-racing/loadout`, {
            quiz_session_id: testSessionId,
            skill_ids: skillIds
        }, {
            headers: { Authorization: `Bearer ${token}` }
        });
        
        if (response.data.success) {
            console.log('✅ Skill loadout set successfully');
            console.log(`⚔️ Skills in loadout: ${response.data.data.skills.length}`);
            response.data.data.skills.forEach((skill, index) => {
                console.log(`   ${index + 1}. ${skill.skill_name} (${skill.category})`);
            });
            return true;
        } else {
            console.log('❌ Failed to set skill loadout:', response.data.message);
            return false;
        }
    } catch (error) {
        console.log('❌ Set skill loadout error:', error.response?.data?.message || error.message);
        return false;
    }
}

async function testGetSkillLoadout() {
    console.log('\n🎒 === GET SKILL LOADOUT TEST ===');
    
    if (!testSessionId) {
        console.log('❌ No session ID available');
        return false;
    }
    
    try {
        const token = authTokens[Object.keys(authTokens)[0]];
        
        const response = await axios.get(`${BASE_URL}/quiz-racing/loadout/${testSessionId}`, {
            headers: { Authorization: `Bearer ${token}` }
        });
        
        if (response.data.success) {
            console.log('✅ Skill loadout retrieved successfully');
            console.log(`⚔️ Skills in loadout: ${response.data.data.skills.length}`);
            return true;
        } else {
            console.log('❌ Failed to get skill loadout:', response.data.message);
            return false;
        }
    } catch (error) {
        console.log('❌ Get skill loadout error:', error.response?.data?.message || error.message);
        return false;
    }
}

async function testGetRacingStats() {
    console.log('\n📈 === GET RACING STATISTICS TEST ===');
    
    if (!testSessionId) {
        console.log('❌ No session ID available');
        return false;
    }
    
    try {
        const token = authTokens[Object.keys(authTokens)[0]];
        
        const response = await axios.get(`${BASE_URL}/quiz-racing/stats/${testSessionId}`, {
            headers: { Authorization: `Bearer ${token}` }
        });
        
        if (response.data.success) {
            console.log('✅ Racing statistics retrieved successfully');
            console.log(`📊 Total participants: ${response.data.data.total_participants}`);
            console.log(`🏆 Leaderboard entries: ${response.data.data.leaderboard.length}`);
            return true;
        } else {
            console.log('❌ Failed to get racing statistics:', response.data.message);
            return false;
        }
    } catch (error) {
        console.log('❌ Get racing statistics error:', error.response?.data?.message || error.message);
        return false;
    }
}

// =====================================================
// WEBSOCKET TESTS
// =====================================================

async function testWebSocketConnection() {
    console.log('\n🔌 === WEBSOCKET CONNECTION TEST ===');
    
    return new Promise((resolve) => {
        const socket = io(SOCKET_URL, {
            transports: ['websocket']
        });
        
        socket.on('connect', () => {
            console.log('✅ WebSocket connected successfully');
            console.log(`🔗 Socket ID: ${socket.id}`);
            
            // Test join quiz racing
            socket.emit('join-quiz-racing', {
                quiz_session_id: testSessionId,
                user_id: 1,
                username: 'student1'
            });
            
            socket.on('quiz-racing-joined', (data) => {
                console.log('✅ Successfully joined quiz racing session');
                console.log(`📋 Session ID: ${data.session_id}`);
                socket.disconnect();
                resolve(true);
            });
            
            socket.on('error', (error) => {
                console.log('❌ WebSocket error:', error.message);
                socket.disconnect();
                resolve(false);
            });
            
            // Timeout after 5 seconds
            setTimeout(() => {
                console.log('⏰ WebSocket test timeout');
                socket.disconnect();
                resolve(false);
            }, 5000);
        });
        
        socket.on('connect_error', (error) => {
            console.log('❌ WebSocket connection error:', error.message);
            resolve(false);
        });
    });
}

// =====================================================
// MAIN TEST RUNNER
// =====================================================

async function runAllTests() {
    console.log('🚀 Starting Quiz Racing API Tests...\n');
    
    const results = {
        authentication: false,
        initializeRacing: false,
        getSessionData: false,
        setLoadout: false,
        getLoadout: false,
        getRacingStats: false,
        websocketConnection: false
    };
    
    // Run tests in sequence
    results.authentication = await loginAllUsers();
    
    if (results.authentication) {
        results.initializeRacing = await testInitializeQuizRacing();
        results.getSessionData = await testGetSessionData();
        results.setLoadout = await testSetSkillLoadout();
        results.getLoadout = await testGetSkillLoadout();
        results.getRacingStats = await testGetRacingStats();
        results.websocketConnection = await testWebSocketConnection();
    }
    
    // Print summary
    console.log('\n📋 === TEST SUMMARY ===');
    const totalTests = Object.keys(results).length;
    const passedTests = Object.values(results).filter(result => result === true).length;
    
    Object.entries(results).forEach(([test, passed]) => {
        console.log(`${passed ? '✅' : '❌'} ${test}: ${passed ? 'PASSED' : 'FAILED'}`);
    });
    
    console.log(`\n🎯 Overall: ${passedTests}/${totalTests} tests passed`);
    
    if (passedTests === totalTests) {
        console.log('🎉 All Quiz Racing API tests passed!');
    } else {
        console.log('⚠️ Some tests failed. Check the logs above for details.');
    }
}

// Run tests
runAllTests().catch(console.error);
