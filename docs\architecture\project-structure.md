# Project Structure

## Monorepo Organization

```
QL_CTDT_FRONT/
├── frontend/                    # Next.js 15 Application
│   ├── src/app/                # App Router (Route Handlers)
│   │   ├── (auth)/             # Authentication routes
│   │   ├── dashboard/          # Main dashboard
│   │   ├── quiz-live/          # Real-time quiz interface
│   │   ├── quiz-monitor/       # Teacher monitoring
│   │   └── quiz-waiting-room/  # Pre-quiz lobby
│   ├── src/components/         # React Components (Restructured)
│   │   ├── ui/                 # Categorized UI Components
│   │   │   ├── forms/          # Form components
│   │   │   ├── navigation/     # Navigation components
│   │   │   ├── layout/         # Layout components
│   │   │   ├── feedback/       # Feedback components
│   │   │   └── display/        # Display components
│   │   └── features/           # Feature-based Components
│   │       ├── auth/           # Authentication features
│   │       ├── quiz/           # Quiz management
│   │       ├── charts/         # Analytics & visualization
│   │       ├── gamification/   # Gamification features
│   │       ├── navigation/     # App navigation
│   │       ├── learning/       # Learning features
│   │       ├── subject/        # Subject management
│   │       └── shared/         # Shared components
│   └── src/lib/                # Centralized Utilities
│       ├── auth/               # Authentication utilities
│       ├── hooks/              # Custom React hooks
│       ├── services/           # API & Socket services
│       ├── types/              # TypeScript definitions
│       ├── utils/              # Utility functions
│       ├── constants/          # Application constants
│       └── validations/        # Validation schemas
├── backend/                    # Node.js/Express API
│   ├── src/controllers/        # Business logic (35+ controllers)
│   ├── src/models/             # Sequelize models (25+ models)
│   ├── src/routes/             # API route definitions
│   ├── src/services/           # Business services
│   ├── src/middleware/         # Express middleware
│   ├── src/migrations/         # Database migrations
│   ├── src/redis/              # Redis utilities
│   └── src/uploads/            # File upload storage
├── docker-compose.yml          # Multi-service deployment
├── nginx.conf                  # Reverse proxy configuration
└── pnpm-workspace.yaml         # Workspace configuration
```
