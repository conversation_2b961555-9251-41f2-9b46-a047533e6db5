-- doc_for_devgame/task4/quiz_racing_schema_updates.sql
-- Additional database schema updates for Quiz Racing System

-- =====================================================
-- QUIZ RACING SESSION TRACKING
-- =====================================================

-- Table to track quiz racing sessions (optional - mainly using Redis cache)
CREATE TABLE IF NOT EXISTS "QuizRacingSessions" (
    "session_id" VARCHAR(100) PRIMARY KEY,
    "quiz_id" INTEGER REFERENCES "Quizzes"("quiz_id") ON DELETE CASCADE,
    "session_status" VARCHAR(20) DEFAULT 'ACTIVE' CHECK ("session_status" IN ('ACTIVE', 'COMPLETED', 'CANCELLED')),
    "total_participants" INTEGER NOT NULL,
    "total_questions" INTEGER NOT NULL,
    "session_start_time" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "session_end_time" TIMESTAMP NULL,
    "winner_user_id" INTEGER REFERENCES "Users"("user_id") ON DELETE SET NULL,
    "created_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Index for performance
CREATE INDEX IF NOT EXISTS "idx_quiz_racing_sessions_quiz_id" ON "QuizRacingSessions"("quiz_id");
CREATE INDEX IF NOT EXISTS "idx_quiz_racing_sessions_status" ON "QuizRacingSessions"("session_status");
CREATE INDEX IF NOT EXISTS "idx_quiz_racing_sessions_start_time" ON "QuizRacingSessions"("session_start_time");

-- =====================================================
-- QUIZ RACING PARTICIPANT RESULTS
-- =====================================================

-- Table to store final results for each participant in racing sessions
CREATE TABLE IF NOT EXISTS "QuizRacingResults" (
    "result_id" SERIAL PRIMARY KEY,
    "session_id" VARCHAR(100) REFERENCES "QuizRacingSessions"("session_id") ON DELETE CASCADE,
    "user_id" INTEGER REFERENCES "Users"("user_id") ON DELETE CASCADE,
    "final_score" INTEGER DEFAULT 0,
    "final_position" INTEGER NOT NULL,
    "max_streak" INTEGER DEFAULT 0,
    "total_skills_used" INTEGER DEFAULT 0,
    "total_questions_answered" INTEGER DEFAULT 0,
    "total_correct_answers" INTEGER DEFAULT 0,
    "average_response_time" DECIMAL(8,2) DEFAULT 0.00,
    "energy_efficiency" DECIMAL(5,2) DEFAULT 0.00, -- Energy gained per correct answer
    "skill_effectiveness" DECIMAL(5,2) DEFAULT 0.00, -- Points gained from skills
    "created_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS "idx_quiz_racing_results_session" ON "QuizRacingResults"("session_id");
CREATE INDEX IF NOT EXISTS "idx_quiz_racing_results_user" ON "QuizRacingResults"("user_id");
CREATE INDEX IF NOT EXISTS "idx_quiz_racing_results_position" ON "QuizRacingResults"("final_position");

-- =====================================================
-- QUIZ RACING QUESTION RESPONSES
-- =====================================================

-- Table to track individual question responses in racing context
CREATE TABLE IF NOT EXISTS "QuizRacingResponses" (
    "response_id" SERIAL PRIMARY KEY,
    "session_id" VARCHAR(100) REFERENCES "QuizRacingSessions"("session_id") ON DELETE CASCADE,
    "user_id" INTEGER REFERENCES "Users"("user_id") ON DELETE CASCADE,
    "question_id" INTEGER REFERENCES "Questions"("question_id") ON DELETE CASCADE,
    "answer_id" INTEGER REFERENCES "Answers"("answer_id") ON DELETE SET NULL,
    "is_correct" BOOLEAN NOT NULL,
    "response_time_ms" INTEGER NOT NULL, -- Response time in milliseconds
    "points_earned" INTEGER DEFAULT 0,
    "energy_gained" INTEGER DEFAULT 0,
    "streak_at_time" INTEGER DEFAULT 0,
    "position_at_time" INTEGER DEFAULT 0,
    "speed_bonus" INTEGER DEFAULT 0,
    "streak_bonus" INTEGER DEFAULT 0,
    "skill_effects_active" JSONB DEFAULT '[]', -- Active effects during this question
    "question_order" INTEGER NOT NULL, -- Order of question in the session
    "answered_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for performance and analytics
CREATE INDEX IF NOT EXISTS "idx_quiz_racing_responses_session" ON "QuizRacingResponses"("session_id");
CREATE INDEX IF NOT EXISTS "idx_quiz_racing_responses_user" ON "QuizRacingResponses"("user_id");
CREATE INDEX IF NOT EXISTS "idx_quiz_racing_responses_question" ON "QuizRacingResponses"("question_id");
CREATE INDEX IF NOT EXISTS "idx_quiz_racing_responses_order" ON "QuizRacingResponses"("session_id", "question_order");

-- =====================================================
-- SKILL COMBO TRACKING
-- =====================================================

-- Table to track skill combinations and their effectiveness
CREATE TABLE IF NOT EXISTS "SkillCombos" (
    "combo_id" SERIAL PRIMARY KEY,
    "session_id" VARCHAR(100) REFERENCES "QuizRacingSessions"("session_id") ON DELETE CASCADE,
    "user_id" INTEGER REFERENCES "Users"("user_id") ON DELETE CASCADE,
    "skill_sequence" JSONB NOT NULL, -- Array of skill IDs used in sequence
    "combo_effectiveness" DECIMAL(8,2) DEFAULT 0.00, -- Points gained from combo
    "time_between_skills" INTEGER DEFAULT 0, -- Milliseconds between skill uses
    "combo_type" VARCHAR(50) DEFAULT 'MANUAL', -- MANUAL, ENERGY_BOOST, ULTIMATE
    "created_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Index for combo analysis
CREATE INDEX IF NOT EXISTS "idx_skill_combos_session" ON "SkillCombos"("session_id");
CREATE INDEX IF NOT EXISTS "idx_skill_combos_user" ON "SkillCombos"("user_id");
CREATE INDEX IF NOT EXISTS "idx_skill_combos_type" ON "SkillCombos"("combo_type");

-- =====================================================
-- RACING ACHIEVEMENTS
-- =====================================================

-- Table for racing-specific achievements
CREATE TABLE IF NOT EXISTS "RacingAchievements" (
    "achievement_id" SERIAL PRIMARY KEY,
    "achievement_code" VARCHAR(50) NOT NULL UNIQUE,
    "achievement_name" VARCHAR(100) NOT NULL,
    "achievement_description" TEXT NOT NULL,
    "achievement_category" VARCHAR(30) NOT NULL CHECK ("achievement_category" IN ('RACING', 'SKILLS', 'STREAKS', 'SPEED', 'COMBOS')),
    "unlock_condition" JSONB NOT NULL, -- Conditions to unlock this achievement
    "reward_syncoin" INTEGER DEFAULT 0,
    "reward_kristal" INTEGER DEFAULT 0,
    "reward_title_id" INTEGER REFERENCES "Titles"("title_id") ON DELETE SET NULL,
    "is_active" BOOLEAN DEFAULT true,
    "created_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- User racing achievements
CREATE TABLE IF NOT EXISTS "UserRacingAchievements" (
    "user_achievement_id" SERIAL PRIMARY KEY,
    "user_id" INTEGER REFERENCES "Users"("user_id") ON DELETE CASCADE,
    "achievement_id" INTEGER REFERENCES "RacingAchievements"("achievement_id") ON DELETE CASCADE,
    "session_id" VARCHAR(100) REFERENCES "QuizRacingSessions"("session_id") ON DELETE SET NULL,
    "unlocked_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "progress_data" JSONB DEFAULT '{}', -- Progress towards achievement
    UNIQUE("user_id", "achievement_id")
);

-- Indexes
CREATE INDEX IF NOT EXISTS "idx_racing_achievements_category" ON "RacingAchievements"("achievement_category");
CREATE INDEX IF NOT EXISTS "idx_user_racing_achievements_user" ON "UserRacingAchievements"("user_id");

-- =====================================================
-- INSERT RACING ACHIEVEMENTS DATA
-- =====================================================

INSERT INTO "RacingAchievements" ("achievement_code", "achievement_name", "achievement_description", "achievement_category", "unlock_condition", "reward_syncoin", "reward_kristal") VALUES
-- Racing achievements
('first_win', 'First Victory', 'Win your first quiz racing session', 'RACING', '{"condition": "win_race", "count": 1}', 100, 0),
('speed_demon', 'Speed Demon', 'Answer 10 questions in under 3 seconds each', 'SPEED', '{"condition": "fast_answers", "time_limit": 3000, "count": 10}', 150, 0),
('comeback_king', 'Comeback King', 'Win a race after being in last place', 'RACING', '{"condition": "comeback_victory", "from_position": "last"}', 200, 50),
('perfect_streak', 'Perfect Streak', 'Achieve a 10-question streak in racing', 'STREAKS', '{"condition": "streak_length", "count": 10}', 150, 30),
('skill_master', 'Skill Master', 'Use all 4 skills in a single racing session', 'SKILLS', '{"condition": "use_all_loadout_skills", "count": 4}', 100, 20),

-- Skill-specific achievements
('blackhole_master', 'Blackhole Master', 'Successfully use Blackhole skill 5 times', 'SKILLS', '{"condition": "skill_usage", "skill_code": "blackhole", "count": 5}', 80, 0),
('phoenix_rise', 'Phoenix Rise', 'Successfully trigger Phoenix skill comeback', 'SKILLS', '{"condition": "skill_success", "skill_code": "phoenix", "outcome": "success"}', 300, 100),
('king_mode', 'King Mode', 'Use King skill and maintain lead for full duration', 'SKILLS', '{"condition": "skill_dominance", "skill_code": "king", "maintain_lead": true}', 250, 75),
('combo_master', 'Combo Master', 'Use Energy skill followed by Ultimate skill', 'COMBOS', '{"condition": "skill_combo", "sequence": ["energy", "ultimate"]}', 200, 50),
('lucky_seven', 'Lucky Seven', 'Get 7 consecutive Lucky skill successes', 'SKILLS', '{"condition": "skill_streak", "skill_code": "lucky", "count": 7}', 150, 40);

-- =====================================================
-- STORED PROCEDURES FOR RACING ANALYTICS
-- =====================================================

-- Function to calculate racing session statistics
CREATE OR REPLACE FUNCTION calculate_racing_session_stats(session_id_param VARCHAR(100))
RETURNS TABLE (
    total_participants INTEGER,
    total_questions INTEGER,
    average_score DECIMAL(8,2),
    total_skills_used INTEGER,
    most_used_skill VARCHAR(100),
    fastest_average_time DECIMAL(8,2),
    highest_streak INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(DISTINCT qrr.user_id)::INTEGER as total_participants,
        MAX(qrr.total_questions_answered)::INTEGER as total_questions,
        AVG(qrr.final_score)::DECIMAL(8,2) as average_score,
        SUM(qrr.total_skills_used)::INTEGER as total_skills_used,
        (SELECT s.skill_name 
         FROM "SkillUsageHistory" suh 
         JOIN "Skills" s ON suh.skill_id = s.skill_id 
         WHERE suh.quiz_session_id = session_id_param 
         GROUP BY s.skill_name 
         ORDER BY COUNT(*) DESC 
         LIMIT 1) as most_used_skill,
        MIN(qrr.average_response_time)::DECIMAL(8,2) as fastest_average_time,
        MAX(qrr.max_streak)::INTEGER as highest_streak
    FROM "QuizRacingResults" qrr
    WHERE qrr.session_id = session_id_param;
END;
$$ LANGUAGE plpgsql;

-- Function to get user racing performance summary
CREATE OR REPLACE FUNCTION get_user_racing_performance(user_id_param INTEGER)
RETURNS TABLE (
    total_races INTEGER,
    wins INTEGER,
    win_rate DECIMAL(5,2),
    average_position DECIMAL(5,2),
    best_score INTEGER,
    total_skills_used INTEGER,
    favorite_skill VARCHAR(100),
    average_streak DECIMAL(5,2)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*)::INTEGER as total_races,
        COUNT(CASE WHEN qrr.final_position = 1 THEN 1 END)::INTEGER as wins,
        (COUNT(CASE WHEN qrr.final_position = 1 THEN 1 END) * 100.0 / COUNT(*))::DECIMAL(5,2) as win_rate,
        AVG(qrr.final_position)::DECIMAL(5,2) as average_position,
        MAX(qrr.final_score)::INTEGER as best_score,
        SUM(qrr.total_skills_used)::INTEGER as total_skills_used,
        (SELECT s.skill_name 
         FROM "SkillUsageHistory" suh 
         JOIN "Skills" s ON suh.skill_id = s.skill_id 
         WHERE suh.user_id = user_id_param 
         GROUP BY s.skill_name 
         ORDER BY COUNT(*) DESC 
         LIMIT 1) as favorite_skill,
        AVG(qrr.max_streak)::DECIMAL(5,2) as average_streak
    FROM "QuizRacingResults" qrr
    WHERE qrr.user_id = user_id_param;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- TRIGGERS FOR AUTOMATIC UPDATES
-- =====================================================

-- Trigger to update session end time when status changes to COMPLETED
CREATE OR REPLACE FUNCTION update_session_end_time()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.session_status = 'COMPLETED' AND OLD.session_status != 'COMPLETED' THEN
        NEW.session_end_time = CURRENT_TIMESTAMP;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_session_end_time
    BEFORE UPDATE ON "QuizRacingSessions"
    FOR EACH ROW
    EXECUTE FUNCTION update_session_end_time();

-- =====================================================
-- VIEWS FOR COMMON QUERIES
-- =====================================================

-- View for racing leaderboards
CREATE OR REPLACE VIEW racing_leaderboards AS
SELECT 
    qrs.session_id,
    qrs.quiz_id,
    qrr.user_id,
    u.username,
    qrr.final_score,
    qrr.final_position,
    qrr.max_streak,
    qrr.total_skills_used,
    qrs.session_start_time,
    qrs.session_status
FROM "QuizRacingSessions" qrs
JOIN "QuizRacingResults" qrr ON qrs.session_id = qrr.session_id
JOIN "Users" u ON qrr.user_id = u.user_id
ORDER BY qrs.session_start_time DESC, qrr.final_position ASC;

-- View for skill usage analytics
CREATE OR REPLACE VIEW skill_usage_analytics AS
SELECT 
    s.skill_name,
    s.category,
    COUNT(suh.usage_id) as total_uses,
    COUNT(CASE WHEN suh.success = true THEN 1 END) as successful_uses,
    (COUNT(CASE WHEN suh.success = true THEN 1 END) * 100.0 / COUNT(suh.usage_id))::DECIMAL(5,2) as success_rate,
    COUNT(DISTINCT suh.user_id) as unique_users,
    COUNT(DISTINCT suh.quiz_session_id) as sessions_used
FROM "Skills" s
LEFT JOIN "SkillUsageHistory" suh ON s.skill_id = suh.skill_id
GROUP BY s.skill_id, s.skill_name, s.category
ORDER BY total_uses DESC;

-- =====================================================
-- COMMENTS AND DOCUMENTATION
-- =====================================================

COMMENT ON TABLE "QuizRacingSessions" IS 'Tracks quiz racing sessions with metadata and status';
COMMENT ON TABLE "QuizRacingResults" IS 'Final results for each participant in racing sessions';
COMMENT ON TABLE "QuizRacingResponses" IS 'Individual question responses in racing context with timing data';
COMMENT ON TABLE "SkillCombos" IS 'Tracks skill combinations and their effectiveness';
COMMENT ON TABLE "RacingAchievements" IS 'Racing-specific achievements and rewards';
COMMENT ON TABLE "UserRacingAchievements" IS 'User progress on racing achievements';

COMMENT ON FUNCTION calculate_racing_session_stats(VARCHAR) IS 'Calculate comprehensive statistics for a racing session';
COMMENT ON FUNCTION get_user_racing_performance(INTEGER) IS 'Get user performance summary across all racing sessions';

COMMENT ON VIEW racing_leaderboards IS 'Combined view of racing sessions and results for leaderboard display';
COMMENT ON VIEW skill_usage_analytics IS 'Analytics view for skill usage patterns and success rates';
