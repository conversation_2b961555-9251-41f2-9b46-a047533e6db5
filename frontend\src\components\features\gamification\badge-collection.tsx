"use client";

import React, { useState, useMemo } from "react";
import {
  <PERSON>,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/layout";
import { Button } from "@/components/ui/forms";
import { Badge } from "@/components/ui/feedback";
import {
  <PERSON>lt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/feedback";
import { EmptyState } from "@/components/ui/feedback";
import { Input } from "@/components/ui/forms";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/forms";
import { useGamification } from "@/lib/hooks/use-gamification";
import { cn } from "@/lib/utils";
import {
  getTierIconFromLevel,
  getVietnameseTierName,
  getTierColor,
  TIER_NAMES,
} from "@/lib/utils/tier-assets";
import { TierIcon } from "@/lib/hooks/use-tier-icon";
import { UserBadgeData, BadgeRarity } from "@/lib/types/gamification";
import { Search, Award, Lock, Star, Sparkles, Crown, Gem } from "lucide-react";
import BadgeDetailsModal from "./badge-details-modal";

// Utility function for search optimization
const createBadgeSearchMatcher = (query: string) => {
  const lowerQuery = query.toLowerCase();
  return (badge: UserBadgeData) => {
    const searchableFields = [
      badge.Badge.badge_name,
      badge.Badge.description,
      getVietnameseTierName(badge.Badge.tier_name),
    ];
    return searchableFields.some((field) =>
      field.toLowerCase().includes(lowerQuery)
    );
  };
};

interface BadgeCollectionProps {
  className?: string;
  variant?: "default" | "compact";
  showStats?: boolean;
}

export const BadgeCollection: React.FC<BadgeCollectionProps> = ({
  className,
  variant = "default",
  showStats = true,
}) => {
  const { userBadges, userGamification, isLoading, error } = useGamification();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedTier, setSelectedTier] = useState<string>("all");
  const [selectedRarity, setSelectedRarity] = useState<string>("all");
  const [selectedStatus, setSelectedStatus] = useState<string>("all");

  // Get current user level for determining locked badges
  const currentLevel = userGamification?.current_level || 1;

  // Get rarity color and icon
  const getRarityConfig = (rarity: BadgeRarity) => {
    const rarityConfigs = {
      common: {
        color: "text-gray-600 bg-gray-50 border-gray-200",
        icon: Star,
        label: "Thường",
        gradient: "from-gray-400 to-gray-600",
      },
      rare: {
        color: "text-blue-600 bg-blue-50 border-blue-200",
        icon: Sparkles,
        label: "Hiếm",
        gradient: "from-blue-400 to-blue-600",
      },
      epic: {
        color: "text-purple-600 bg-purple-50 border-purple-200",
        icon: Crown,
        label: "Sử Thi",
        gradient: "from-purple-400 to-purple-600",
      },
      legendary: {
        color: "text-yellow-600 bg-yellow-50 border-yellow-200",
        icon: Gem,
        label: "Huyền Thoại",
        gradient: "from-yellow-400 to-orange-500",
      },
    };
    return rarityConfigs[rarity] || rarityConfigs.common;
  };

  // Note: getTierColor is now imported from tier-assets utility

  // Filter and search badges with defensive programming
  const filteredBadges = useMemo(() => {
    if (!userBadges || !Array.isArray(userBadges) || userBadges.length === 0)
      return [];
    let filtered = userBadges.filter((badge) => badge && badge.Badge); // Filter out invalid entries

    // Search filter
    if (searchQuery) {
      const searchMatcher = createBadgeSearchMatcher(searchQuery);
      filtered = filtered.filter(searchMatcher);
    }

    // Tier filter
    if (selectedTier !== "all") {
      filtered = filtered.filter(
        (badge) =>
          badge.Badge.tier_name.toLowerCase() === selectedTier.toLowerCase()
      );
    }

    // Rarity filter
    if (selectedRarity !== "all") {
      filtered = filtered.filter(
        (badge) => badge.Badge.rarity === selectedRarity
      );
    }

    // Status filter
    if (selectedStatus !== "all") {
      if (selectedStatus === "unlocked") {
        filtered = filtered.filter((badge) => badge.unlocked_at);
      } else if (selectedStatus === "locked") {
        filtered = filtered.filter((badge) => !badge.unlocked_at);
      }
    }

    return filtered;
  }, [userBadges, searchQuery, selectedTier, selectedRarity, selectedStatus]);

  // Get rarity stats
  const rarityStats = useMemo(() => {
    const stats = {
      common: { unlocked: 0, total: 0 },
      rare: { unlocked: 0, total: 0 },
      epic: { unlocked: 0, total: 0 },
      legendary: { unlocked: 0, total: 0 },
    };

    if (!userBadges || userBadges.length === 0) return stats;

    userBadges.forEach((badge) => {
      const rarity = badge.Badge.rarity;
      stats[rarity].total++;
      if (badge.unlocked_at) {
        stats[rarity].unlocked++;
      }
    });

    return stats;
  }, [userBadges]);

  if (isLoading) {
    return (
      <Card className={cn("w-full", className)}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Award className="h-5 w-5" />
            Bộ Sưu Tập Huy Hiệu
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={cn("w-full", className)}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Award className="h-5 w-5" />
            Bộ Sưu Tập Huy Hiệu
          </CardTitle>
        </CardHeader>
        <CardContent>
          <EmptyState
            title="Lỗi tải dữ liệu"
            description="Không thể tải danh sách huy hiệu. Vui lòng thử lại sau."
            icon="Search"
          />
        </CardContent>
      </Card>
    );
  }

  return (
    <TooltipProvider>
      <Card className={cn("w-full", className)}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Award className="h-5 w-5" />
            Bộ Sưu Tập Huy Hiệu
          </CardTitle>

          {showStats && (
            <div className="space-y-2 mt-2">
              <div className="flex flex-wrap gap-2">
                <Badge variant="secondary">
                  Tổng: {userBadges?.filter((b) => b.unlocked_at).length || 0}/
                  {userBadges?.length || 0}
                </Badge>
              </div>

              {/* Rarity breakdown */}
              <div className="flex flex-wrap gap-2">
                {Object.entries(rarityStats).map(([rarity, stats]) => {
                  const config = getRarityConfig(rarity as BadgeRarity);
                  return (
                    <Badge
                      key={rarity}
                      variant="outline"
                      className={cn("text-xs", config.color)}
                    >
                      {config.label}: {stats.unlocked}/{stats.total}
                    </Badge>
                  );
                })}
              </div>
            </div>
          )}
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Search and Filter Controls */}
          <div className="flex flex-col sm:flex-row gap-3">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Tìm kiếm huy hiệu..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>

            <Select value={selectedTier} onValueChange={setSelectedTier}>
              <SelectTrigger className="w-full sm:w-[140px]">
                <SelectValue placeholder="Hạng" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tất cả hạng</SelectItem>
                {TIER_NAMES.map((tier) => (
                  <SelectItem key={tier} value={tier}>
                    {getVietnameseTierName(tier)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={selectedRarity} onValueChange={setSelectedRarity}>
              <SelectTrigger className="w-full sm:w-[140px]">
                <SelectValue placeholder="Độ hiếm" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tất cả</SelectItem>
                <SelectItem value="common">Thường</SelectItem>
                <SelectItem value="rare">Hiếm</SelectItem>
                <SelectItem value="epic">Sử Thi</SelectItem>
                <SelectItem value="legendary">Huyền Thoại</SelectItem>
              </SelectContent>
            </Select>

            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger className="w-full sm:w-[140px]">
                <SelectValue placeholder="Trạng thái" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tất cả</SelectItem>
                <SelectItem value="unlocked">Đã có</SelectItem>
                <SelectItem value="locked">Chưa có</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Badge Grid */}
          {filteredBadges.length === 0 ? (
            <EmptyState
              title="Không tìm thấy huy hiệu"
              description="Thử thay đổi bộ lọc hoặc từ khóa tìm kiếm."
              icon="Search"
            />
          ) : (
            <div
              className={cn(
                "grid gap-3",
                variant === "compact"
                  ? "grid-cols-2 sm:grid-cols-3 lg:grid-cols-4"
                  : "grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5"
              )}
            >
              {filteredBadges.map((badge) => (
                <BadgeCard
                  key={badge.user_badge_id}
                  badge={badge}
                  currentLevel={currentLevel}
                  getRarityConfig={getRarityConfig}
                  getTierColor={getTierColor}
                  variant={variant}
                />
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </TooltipProvider>
  );
};

// Badge Card Component
interface BadgeCardProps {
  badge: UserBadgeData;
  currentLevel: number;
  getRarityConfig: (rarity: BadgeRarity) => any;
  getTierColor: (tierName: string) => string;
  variant: "default" | "compact";
}

const BadgeCard: React.FC<BadgeCardProps> = ({
  badge,
  currentLevel,
  getRarityConfig,
  getTierColor,
  variant,
}) => {
  const isUnlocked = !!badge.unlocked_at;
  const isLocked = currentLevel < badge.Badge.unlock_level;
  const rarityConfig = getRarityConfig(badge.Badge.rarity);

  return (
    <BadgeDetailsModal
      badge={badge}
      currentLevel={currentLevel}
      trigger={
        <Card
          className={cn(
            "cursor-pointer transition-all duration-200 hover:shadow-md relative overflow-hidden",
            isLocked && "opacity-60",
            rarityConfig.color.split(" ")[2] // border color
          )}
        >
          {/* Rarity gradient background */}
          <div
            className={cn(
              "absolute inset-0 opacity-10 bg-gradient-to-br",
              rarityConfig.gradient
            )}
          />

          <CardContent
            className={cn("p-3 relative", variant === "compact" && "p-2")}
          >
            <div className="flex flex-col items-center text-center space-y-2">
              {/* Badge Icon/Tier Icon */}
              <div className="relative">
                {badge.Badge.icon_path ? (
                  <div className="w-12 h-12 bg-muted rounded-full flex items-center justify-center">
                    {/* TODO: Add actual badge icon when available */}
                    <rarityConfig.icon className="h-6 w-6" />
                  </div>
                ) : (
                  <TierIcon
                    level={badge.Badge.unlock_level}
                    size={variant === "compact" ? "sm" : "md"}
                    tierName={badge.Badge.tier_name}
                    levelInTier={1}
                  />
                )}

                {isLocked && (
                  <div className="absolute inset-0 flex items-center justify-center bg-black/50 rounded-full">
                    <Lock className="h-3 w-3 text-white" />
                  </div>
                )}
              </div>

              {/* Badge Info */}
              <div className="space-y-1">
                <h4
                  className={cn(
                    "font-semibold text-center line-clamp-2",
                    variant === "compact" ? "text-xs" : "text-sm",
                    rarityConfig.color.split(" ")[0] // text color
                  )}
                >
                  {badge.Badge.badge_name}
                </h4>

                {/* Rarity Badge */}
                <Badge
                  variant="outline"
                  className={cn("text-xs px-1 py-0", rarityConfig.color)}
                >
                  {rarityConfig.label}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      }
    />
  );
};

export default BadgeCollection;
