# Data Architecture

## Database Design

**Primary Database**: PostgreSQL 15

- **Models**: 25+ Sequelize models với complex relationships
- **Key Entities**: Users, Programs, Courses, Subjects, Chapters, Quizzes, Questions, Answers
- **Relationships**: Many-to-many relationships cho program-course-subject hierarchy
- **Constraints**: Foreign key constraints, unique constraints, check constraints

## Caching Strategy

**Redis 7 Implementation**:

- **Session Storage**: User sessions với TTL
- **Quiz State**: Real-time quiz data caching
- **Leaderboards**: Sorted sets cho performance
- **Pub/Sub**: Real-time event broadcasting

## File Storage

**Local File System**:

- **Location**: `backend/src/uploads/`
- **Types**: Excel files, images, documents
- **Processing**: xlsx library cho Excel import/export
- **Issue**: No cleanup mechanism (technical debt)
