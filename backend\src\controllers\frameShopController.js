const FrameShopService = require('../services/frameShopService');

class FrameShopController {
    /**
     * Get frame shop data
     * GET /api/frames/shop
     */
    static async getFrameShop(req, res) {
        try {
            const userId = req.user.user_id;

            const result = await FrameShopService.getFrameShop(userId);

            if (result.success) {
                res.status(200).json({
                    success: true,
                    message: result.message,
                    data: result.data
                });
            } else {
                res.status(400).json({
                    success: false,
                    message: result.message,
                    error: result.error
                });
            }
        } catch (error) {
            console.error('Error in getFrameShop:', error);
            res.status(500).json({
                success: false,
                message: 'Internal server error',
                error: error.message
            });
        }
    }

    /**
     * Purchase frame with Kristal
     * POST /api/frames/purchase
     */
    static async purchaseFrame(req, res) {
        try {
            const userId = req.user.user_id;
            const { frame_id } = req.body;

            if (!frame_id) {
                return res.status(400).json({
                    success: false,
                    message: 'Frame ID is required'
                });
            }

            const result = await FrameShopService.purchaseFrameWithKristal(userId, frame_id);

            if (result.success) {
                res.status(200).json({
                    success: true,
                    message: result.message,
                    data: result.data
                });
            } else {
                res.status(400).json({
                    success: false,
                    message: result.message,
                    error: result.error
                });
            }
        } catch (error) {
            console.error('Error in purchaseFrame:', error);
            res.status(500).json({
                success: false,
                message: 'Internal server error',
                error: error.message
            });
        }
    }
}

module.exports = FrameShopController;
