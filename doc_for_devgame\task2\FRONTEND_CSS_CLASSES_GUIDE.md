# Frontend CSS Classes Guide - Name Effects System (Updated)

## 📋 Tổng Quan

Backend sẽ lưu **tên CSS class** trong database, frontend team sẽ tạo các CSS classes tương ứng để tạo hiệu ứng cho tên người chơi.

**✅ CẬP NHẬT**: <PERSON> yêu cầu frontend - <PERSON><PERSON><PERSON> thấp không có effect, từ bậc 2 trở lên có hiệu ứng tăng dần.

## 🎯 **Cách Ho<PERSON>t Động**

### Backend Response
```json
{
  "effect_id": 5,
  "effect_name": "Hiệu Ứng Sapphire Sóng",
  "effect_code": "SAPPHIRE_WAVE",
  "css_class": "name-effect-sapphire-wave",
  "tier_name": "Sapphire",
  "unlock_level": 73,
  "is_animated": true
}
```

### Frontend Implementation
```jsx
// Component hiển thị tên với hiệu ứng
<span className={nameEffect.css_class}>
  {userName}
</span>
```

## 🎨 **CSS Classes Cần Tạo**

### **Progression System**
- **Wood/Bronze (Level 1-20)**: ❌ Không có name effects
- **Silver (Level 21-30)**: 🎨 Đổi màu đơn giản
- **Gold (Level 31-40)**: ✨ Màu sắc nâng cao + text-shadow nhẹ
- **Platinum (Level 41-50)**: 🌟 Glow effects nhẹ
- **Onyx (Level 51-60)**: 🔥 Dark glow mạnh hơn
- **Sapphire (Level 61-70)**: 🌊 Animation đầu tiên
- **Ruby (Level 71-80)**: 🔥 Fire animations
- **Amethyst (Level 81-90)**: ✨ Magic effects
- **Master (Level 91+)**: 🌈 Ultimate rainbow effects

### **SILVER TIER** (Level 21-30) - Simple Colors

```css
.name-effect-silver-basic {
  color: #a0aec0;
  font-weight: 500;
}

.name-effect-silver-bright {
  color: #e2e8f0;
  font-weight: 600;
}
```

### **GOLD TIER** (Level 31-40) - Enhanced Colors

```css
.name-effect-gold-basic {
  color: #d69e2e;
  font-weight: 600;
}

.name-effect-gold-bold {
  color: #b7791f;
  font-weight: 700;
  text-shadow: 1px 1px 2px rgba(183, 121, 31, 0.3);
}
```

### **PLATINUM TIER** (Level 41-50) - Subtle Glow

```css
.name-effect-platinum-basic {
  color: #cbd5e0;
  font-weight: 600;
}

.name-effect-platinum-glow {
  color: #cbd5e0;
  text-shadow: 0 0 6px rgba(203, 213, 224, 0.5);
}
```

### **ONYX TIER** (Level 51-60) - Dark Glow

```css
.name-effect-onyx-basic {
  color: #2d3748;
  font-weight: 600;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

.name-effect-onyx-glow {
  color: #2d3748;
  text-shadow: 0 0 8px rgba(45, 55, 72, 0.6);
}
```

### **SAPPHIRE TIER** (Level 61-70) - Blue Animations

```css
.name-effect-sapphire-basic {
  color: #3182ce;
  font-weight: 600;
  text-shadow: 0 0 4px rgba(49, 130, 206, 0.4);
}

.name-effect-sapphire-wave {
  color: #3182ce;
  animation: sapphire-wave 2s ease-in-out infinite;
}

@keyframes sapphire-wave {
  0%, 100% {
    transform: translateY(0px);
    text-shadow: 0 0 4px rgba(49, 130, 206, 0.4);
  }
  50% {
    transform: translateY(-2px);
    text-shadow: 0 0 8px rgba(49, 130, 206, 0.6);
  }
}
```

### **RUBY TIER** (Level 81-90) - Red Effects

```css
.name-effect-ruby-basic {
  color: #e53e3e;
  font-weight: 600;
}

.name-effect-ruby-fire {
  color: #e53e3e;
  animation: ruby-fire 2s ease-in-out infinite;
  text-shadow: 0 0 10px rgba(229, 62, 62, 0.8);
}

.name-effect-ruby-blood {
  color: #c53030;
  text-shadow: 0 0 12px rgba(197, 48, 48, 0.9);
}

.name-effect-ruby-lava {
  color: #e53e3e;
  animation: ruby-lava 3s ease-in-out infinite;
}

@keyframes ruby-fire {
  0%, 100% { 
    text-shadow: 0 0 10px #e53e3e;
    transform: scale(1);
  }
  50% { 
    text-shadow: 0 0 20px #e53e3e, 0 0 30px #ff6b6b;
    transform: scale(1.05);
  }
}

@keyframes ruby-lava {
  0%, 100% { color: #e53e3e; }
  25% { color: #ff6b6b; }
  50% { color: #ff8e53; }
  75% { color: #ff6b6b; }
}
```

### **AMETHYST TIER** (Level 91-100) - Purple Effects

```css
.name-effect-amethyst-basic {
  color: #805ad5;
  font-weight: 600;
}

.name-effect-amethyst-magic {
  color: #805ad5;
  animation: amethyst-magic 2.5s ease-in-out infinite;
  text-shadow: 0 0 15px rgba(128, 90, 213, 0.9);
}

.name-effect-amethyst-cosmic {
  color: #805ad5;
  animation: amethyst-cosmic 4s ease-in-out infinite;
}

.name-effect-amethyst-legendary {
  color: #805ad5;
  animation: amethyst-legendary 3s ease-in-out infinite;
  text-shadow: 0 0 20px rgba(128, 90, 213, 1);
}

@keyframes amethyst-magic {
  0%, 100% { 
    text-shadow: 0 0 15px #805ad5;
    filter: hue-rotate(0deg);
  }
  50% { 
    text-shadow: 0 0 25px #805ad5, 0 0 35px #b794f6;
    filter: hue-rotate(30deg);
  }
}

@keyframes amethyst-cosmic {
  0% { text-shadow: 0 0 10px #805ad5; }
  25% { text-shadow: 0 0 20px #9f7aea; }
  50% { text-shadow: 0 0 30px #b794f6; }
  75% { text-shadow: 0 0 20px #9f7aea; }
  100% { text-shadow: 0 0 10px #805ad5; }
}

@keyframes amethyst-legendary {
  0%, 100% { 
    transform: scale(1);
    text-shadow: 0 0 20px #805ad5;
  }
  50% { 
    transform: scale(1.1);
    text-shadow: 0 0 30px #805ad5, 0 0 40px #b794f6;
  }
}
```

### **MASTER TIER** (Level 101+) - Rainbow/Gold Effects

```css
.name-effect-master-gold {
  color: #d69e2e;
  animation: master-sparkle 3s ease-in-out infinite;
  text-shadow: 0 0 20px rgba(214, 158, 46, 1);
}

.name-effect-master-rainbow {
  background: linear-gradient(45deg, #ff0000, #ff7f00, #ffff00, #00ff00, #0000ff, #4b0082, #9400d3);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: master-rainbow 4s ease-in-out infinite;
}

.name-effect-master-divine {
  color: #f7fafc;
  text-shadow: 0 0 25px rgba(247, 250, 252, 1);
  animation: master-divine 5s ease-in-out infinite;
}

.name-effect-master-mythical {
  background: linear-gradient(45deg, #ffd700, #ff69b4, #00ffff, #ff1493);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: master-mythical 6s ease-in-out infinite;
}

.name-effect-master-infinite {
  background: linear-gradient(360deg, #ff0000, #ff7f00, #ffff00, #00ff00, #0000ff, #4b0082, #9400d3, #ff0000);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: master-infinite 8s linear infinite;
}

@keyframes master-sparkle {
  0%, 100% { 
    text-shadow: 0 0 20px #d69e2e;
    transform: scale(1);
  }
  50% { 
    text-shadow: 0 0 30px #d69e2e, 0 0 40px #f6e05e;
    transform: scale(1.05);
  }
}

@keyframes master-rainbow {
  0% { filter: hue-rotate(0deg); }
  100% { filter: hue-rotate(360deg); }
}

@keyframes master-divine {
  0%, 100% { 
    text-shadow: 0 0 25px rgba(247, 250, 252, 1);
    opacity: 0.9;
  }
  50% { 
    text-shadow: 0 0 35px rgba(247, 250, 252, 1), 0 0 45px rgba(255, 255, 255, 0.8);
    opacity: 1;
  }
}

@keyframes master-mythical {
  0% { filter: hue-rotate(0deg) saturate(1); }
  25% { filter: hue-rotate(90deg) saturate(1.2); }
  50% { filter: hue-rotate(180deg) saturate(1.4); }
  75% { filter: hue-rotate(270deg) saturate(1.2); }
  100% { filter: hue-rotate(360deg) saturate(1); }
}

@keyframes master-infinite {
  0% { background-position: 0% 50%; }
  100% { background-position: 100% 50%; }
}
```

## 🔧 **React Component Example**

```jsx
import React from 'react';
import './NameEffects.css'; // Import CSS file với các classes trên

const PlayerName = ({ userName, nameEffect }) => {
  if (!nameEffect || !nameEffect.css_class) {
    return <span className="name-default">{userName}</span>;
  }

  return (
    <span 
      className={nameEffect.css_class}
      title={`${nameEffect.effect_name} (${nameEffect.tier_name} Tier)`}
    >
      {userName}
    </span>
  );
};

export default PlayerName;
```

## 📱 **Usage Examples**

### Leaderboard
```jsx
<div className="leaderboard-item">
  <PlayerName 
    userName="NguyenVanA" 
    nameEffect={{
      css_class: "name-effect-sapphire-wave",
      effect_name: "Hiệu Ứng Sapphire Sóng",
      tier_name: "Sapphire"
    }} 
  />
  <span className="score">1250 điểm</span>
</div>
```

### Quiz Room
```jsx
<div className="player-list">
  {players.map(player => (
    <div key={player.id} className="player-item">
      <PlayerName 
        userName={player.name} 
        nameEffect={player.equipped_name_effect}
      />
      <span className="level">Level {player.level}</span>
    </div>
  ))}
</div>
```

## 🎯 **API Integration**

### Get User's Name Effect
```javascript
// API response sẽ include css_class
const response = await fetch('/api/avatar/my-data');
const data = await response.json();

const nameEffect = data.customization.equipped_name_effect;
// nameEffect.css_class = "name-effect-ruby-fire"
```

### Apply Name Effect
```jsx
<PlayerName 
  userName={userData.name}
  nameEffect={userData.customization.equipped_name_effect}
/>
```

## 🚀 **Implementation Steps**

1. **Tạo CSS file** với tất cả classes trên
2. **Import CSS** vào components cần dùng
3. **Sử dụng css_class** từ API response
4. **Test với các tier khác nhau**
5. **Optimize animations** cho performance

## 📝 **Notes**

- **20 Name Effects** total (3 Onyx + 4 Sapphire + 4 Ruby + 4 Amethyst + 5 Master)
- **CSS classes** được define sẵn trong backend
- **Animations** chỉ cho high-tier effects
- **Responsive** - effects hoạt động trên mobile
- **Performance** - sử dụng CSS transforms thay vì layout changes

Backend đã sẵn sàng, frontend chỉ cần tạo CSS classes và sử dụng!
