// Test script to check if all models and methods are working
const { UserEmoji, EmojiType, EmojiUsageHistory, SocialInteraction, UserSocialStats } = require('../../backend/src/models');

async function testModels() {
    console.log('Testing UserEmoji model...');
    
    try {
        // Test getUserEmojiCollection method
        console.log('Testing getUserEmojiCollection...');
        const collection = await UserEmoji.getUserEmojiCollection(1);
        console.log('✅ getUserEmojiCollection works');
        
        // Test getUserEmojiStats method
        console.log('Testing getUserEmojiStats...');
        const stats = await UserEmoji.getUserEmojiStats(1);
        console.log('✅ getUserEmojiStats works');
        
        // Test hasEmoji method
        console.log('Testing hasEmoji...');
        const hasEmoji = await UserEmoji.hasEmoji(1, 1);
        console.log('✅ hasEmoji works');
        
        console.log('All UserEmoji methods are working!');
        
    } catch (error) {
        console.error('❌ Error testing UserEmoji:', error.message);
    }
    
    console.log('\nTesting EmojiType model...');
    try {
        const emojiTypes = await EmojiType.findAll({ limit: 1 });
        console.log('✅ EmojiType model works');
    } catch (error) {
        console.error('❌ Error testing EmojiType:', error.message);
    }
    
    console.log('\nTesting EmojiUsageHistory model...');
    try {
        const usage = await EmojiUsageHistory.findAll({ limit: 1 });
        console.log('✅ EmojiUsageHistory model works');
    } catch (error) {
        console.error('❌ Error testing EmojiUsageHistory:', error.message);
    }
    
    console.log('\nTesting SocialInteraction model...');
    try {
        const interactions = await SocialInteraction.findAll({ limit: 1 });
        console.log('✅ SocialInteraction model works');
    } catch (error) {
        console.error('❌ Error testing SocialInteraction:', error.message);
    }
    
    console.log('\nTesting UserSocialStats model...');
    try {
        const stats = await UserSocialStats.findAll({ limit: 1 });
        console.log('✅ UserSocialStats model works');
    } catch (error) {
        console.error('❌ Error testing UserSocialStats:', error.message);
    }
    
    process.exit(0);
}

testModels();
