# 📚 API Phân Tích Học Tập <PERSON> - Cập <PERSON>t <PERSON>ới

## 🎯 Thay Đổi Quan Trọng

### ✨ Từ LO sang Chương (Chapter-Based Analysis)

**Lý do thay đổi:** Sinh viên chỉ biết về **chương** trong giáo trình, không biết về Learning Outcomes (LO). <PERSON> đ<PERSON>, các API báo cáo cần hiển thị thông tin theo chương để dễ hiểu và hữu ích hơn.

**Cách tiếp cận:**
- **Chính:** Hiển thị phân tích theo chương với nội dung chi tiết
- **Ph<PERSON>:** Vẫn giữ phân tích LO cho giảng viên/admin tham khảo
- **B<PERSON> sung:** Hiển thị sections trong chương để hỗ trợ học tập cụ thể

---

## 🔄 Các Thay Đổi Chính

### 1. Helper Functions Mới

**File:** `backend/src/utils/learningAnalysisHelpers.js`

#### Hàm <PERSON>:
```javascript
// Phân tích điểm mạnh/yếu theo chương
analyzeChapterStrengthsWeaknesses(questionHistory, weakThreshold = 40)

// Tính phân bổ câu hỏi theo chương
calculateChapterQuestionDistribution(questions)
```

#### Tính Năng:
- ✅ Ánh xạ từ LO → Chapter thông qua bảng ChapterLO
- ✅ Tập hợp thống kê theo chương
- ✅ Hiển thị sections trong chương
- ✅ Liệt kê các LO liên quan đến chương
- ✅ Tính toán độ chính xác và thời gian trung bình

### 2. API Response Structure Mới

#### API Chi Tiết Kết Quả Quiz
```json
{
  "data": {
    "question_distribution": {
      "by_chapter": [
        {
          "chapter_id": 1,
          "chapter_name": "Chương 1: HTML/CSS Cơ Bản",
          "question_count": 8,
          "percentage": 40.0,
          "related_los": ["Hiểu cú pháp HTML", "Áp dụng CSS"]
        }
      ],
      "by_difficulty": [...],
      "total_questions": 20
    },
    "chapter_analysis": {
      "strengths": [
        {
          "chapter_id": 1,
          "chapter_name": "Chương 1: HTML/CSS Cơ Bản",
          "chapter_description": "Giới thiệu về HTML và CSS",
          "sections": [
            {
              "section_id": 1,
              "title": "1.1 Cú pháp HTML cơ bản",
              "content_type": "text",
              "has_content": true
            },
            {
              "section_id": 2,
              "title": "1.2 CSS Selectors",
              "content_type": "video",
              "has_content": true
            }
          ],
          "accuracy_percentage": 87.5,
          "performance_level": "excellent",
          "total_questions": 8,
          "correct_answers": 7,
          "related_los": ["Hiểu cú pháp HTML", "Áp dụng CSS"],
          "los_covered": 2
        }
      ],
      "weaknesses": [
        {
          "chapter_id": 3,
          "chapter_name": "Chương 3: JavaScript Nâng Cao",
          "accuracy_percentage": 33.3,
          "performance_level": "weak",
          "sections": [
            {
              "section_id": 7,
              "title": "3.1 Async/Await",
              "content_type": "text",
              "has_content": true
            }
          ],
          "related_los": ["Lập trình bất đồng bộ"],
          "los_covered": 1
        }
      ],
      "summary": {
        "total_chapters_covered": 3,
        "strong_chapters_count": 1,
        "weak_chapters_count": 1,
        "chapters_needing_attention": [
          {
            "chapter_id": 3,
            "chapter_name": "Chương 3: JavaScript Nâng Cao",
            "accuracy": 33.3,
            "gap_to_target": 36.7,
            "related_los": ["Lập trình bất đồng bộ"],
            "sections": [
              {
                "section_id": 7,
                "title": "3.1 Async/Await",
                "content_type": "text",
                "has_content": true
              }
            ]
          }
        ]
      }
    },
    "learning_outcome_analysis": {
      // Vẫn giữ để tham khảo
    },
    "learning_insights": {
      "what_you_did_well": "Bạn đã thể hiện tốt ở 1 chương: Chương 1: HTML/CSS Cơ Bản",
      "areas_for_improvement": "Bạn cần tập trung cải thiện 1 chương: Chương 3: JavaScript Nâng Cao",
      "next_steps": "Ưu tiên ôn tập: Chương 3: JavaScript Nâng Cao",
      "study_chapters": [
        {
          "chapter_name": "Chương 3: JavaScript Nâng Cao",
          "accuracy": 33.3,
          "sections_to_review": ["3.1 Async/Await"],
          "related_concepts": ["Lập trình bất đồng bộ"]
        }
      ]
    }
  }
}
```

#### API Báo Cáo Tổng Thể Theo Môn Học
```json
{
  "data": {
    "chapter_completion_chart": {
      "labels": ["Chương 1: HTML/CSS", "Chương 2: JavaScript", "Chương 3: Backend"],
      "completion_percentages": [85.0, 65.0, 45.0],
      "target_line": 70,
      "chart_data": [
        {
          "chapter_id": 1,
          "chapter_name": "Chương 1: HTML/CSS",
          "completion_percentage": 85.0,
          "status": "achieved",
          "gap_to_target": 0,
          "related_los": ["HTML cơ bản", "CSS styling"],
          "sections": [
            {
              "section_id": 1,
              "title": "1.1 Cú pháp HTML",
              "content_type": "text",
              "has_content": true
            }
          ]
        },
        {
          "chapter_id": 3,
          "chapter_name": "Chương 3: Backend",
          "completion_percentage": 45.0,
          "status": "needs_attention",
          "gap_to_target": 25.0,
          "related_los": ["API Development", "Database"],
          "sections": [
            {
              "section_id": 8,
              "title": "3.1 REST API",
              "content_type": "video",
              "has_content": true
            }
          ]
        }
      ]
    },
    "chapter_analysis": {
      "achievement_summary": {
        "total_chapters": 3,
        "achieved_chapters": 1,
        "in_progress_chapters": 1,
        "needs_attention_chapters": 1
      }
    },
    "learning_insights": {
      "subject_mastery_level": "Tốt",
      "strongest_chapters": ["Chương 1: HTML/CSS"],
      "chapters_needing_improvement": ["Chương 3: Backend"],
      "recommended_focus": "Tập trung ôn tập: Chương 3: Backend",
      "study_recommendations": [
        {
          "chapter_name": "Chương 3: Backend",
          "current_accuracy": 45.0,
          "sections_to_review": ["3.1 REST API", "3.2 Database Design"],
          "related_concepts": ["API Development", "Database"],
          "priority": "high"
        }
      ]
    }
  }
}
```

---

## 🎯 Lợi Ích Cho Sinh Viên

### ✅ Dễ Hiểu Hơn
- Hiển thị theo **chương** thay vì LO trừu tượng
- Sinh viên biết chính xác cần ôn tập chương nào

### ✅ Hỗ Trợ Học Tập Cụ Thể
- Liệt kê **sections** trong chương cần ôn tập
- Hiển thị loại nội dung (text, video, exercise)
- Gợi ý các khái niệm liên quan

### ✅ Kế Hoạch Học Tập Rõ Ràng
- Ưu tiên chương nào cần ôn trước
- Sections cụ thể cần xem lại
- Mức độ ưu tiên (critical, high, medium)

---

## 🔧 Cách Sử Dụng

### Cho Sinh Viên:
```javascript
// Xem kết quả quiz chi tiết theo chương
GET /api/quiz-results/detailed-analysis/123/456

// Xem báo cáo tổng thể môn học theo chương
GET /api/reports/subject/10/comprehensive-analysis/456
```

### Response sẽ bao gồm:
1. **chapter_analysis** - Phân tích chính theo chương
2. **chapter_completion_chart** - Biểu đồ hoàn thành theo chương
3. **learning_insights.study_chapters** - Gợi ý ôn tập cụ thể
4. **learning_outcome_analysis** - Vẫn giữ để tham khảo

---

## 📊 Mapping Logic

### LO → Chapter Mapping:
```
Question → LO → ChapterLO → Chapter → Sections
```

### Tính Toán:
1. **Lấy tất cả câu hỏi** của user
2. **Tìm LO** của từng câu hỏi  
3. **Ánh xạ LO → Chapter** qua bảng ChapterLO
4. **Tập hợp thống kê** theo chapter_id
5. **Tính accuracy** cho từng chương
6. **Phân loại** strengths/weaknesses
7. **Lấy sections** của chương để hiển thị

---

## 🚀 Kết Quả

Sinh viên giờ đây sẽ thấy:
- ✅ "Bạn cần ôn tập **Chương 3: JavaScript Nâng Cao**"
- ✅ "Xem lại **Section 3.1: Async/Await**"
- ✅ "Tập trung vào khái niệm **Lập trình bất đồng bộ**"

Thay vì:
- ❌ "Bạn yếu ở LO: Apply asynchronous programming concepts"
