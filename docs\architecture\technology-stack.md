# Technology Stack

## Core Technologies

| Category           | Technology   | Version  | Purpose                   | Rationale                            |
| ------------------ | ------------ | -------- | ------------------------- | ------------------------------------ |
| Package Manager    | pnpm         | 8.15.0   | Workspace management      | Efficient monorepo support           |
| Frontend Framework | Next.js      | 15.3.0   | React application         | App Router, SSR, modern React 19     |
| Frontend Runtime   | React        | 19.0.0   | UI library                | Latest stable version                |
| Backend Runtime    | Node.js      | >=18.0.0 | Server runtime            | LTS version, modern features         |
| Backend Framework  | Express      | 5.1.0    | Web application framework | Mature, extensive ecosystem          |
| Language           | TypeScript   | ^5       | Frontend type safety      | Enhanced developer experience        |
| Database           | PostgreSQL   | 15       | Primary data store        | ACID compliance, complex queries     |
| Cache              | Redis        | 7        | Session & real-time cache | High performance, pub/sub support    |
| ORM                | Sequelize    | 6.37.7   | Database abstraction      | Mature ORM with migration support    |
| Real-time          | Socket.IO    | 4.8.1    | WebSocket communication   | Reliable real-time features          |
| UI Library         | Radix UI     | Latest   | Component primitives      | Accessible, unstyled components      |
| Styling            | Tailwind CSS | ^4       | Utility-first CSS         | Rapid development, consistent design |
| Authentication     | JWT          | 9.0.2    | Token-based auth          | Stateless, scalable authentication   |
| Password Hashing   | bcrypt       | 5.1.1    | Secure password storage   | Industry standard hashing            |
| File Processing    | xlsx         | 0.18.5   | Excel import/export       | Bulk data operations                 |
| Reverse Proxy      | Nginx        | latest   | Load balancing & SSL      | Production deployment                |

## Development Tools

| Category         | Technology     | Version | Purpose                  |
| ---------------- | -------------- | ------- | ------------------------ |
| Containerization | Docker         | latest  | Development & deployment |
| Orchestration    | Docker Compose | latest  | Multi-service management |
| Version Control  | Git            | latest  | Source code management   |
