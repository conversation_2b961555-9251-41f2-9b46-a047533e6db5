-- =====================================================
-- GAMIFICATION LEVEL SYSTEM FOR SYNLEARNIA
-- Hệ thống cấp độ và danh hiệu theo kế hoạch gamification
-- =====================================================

-- Bắt đầu transaction
BEGIN;

-- =====================================================
-- 1. TẠO BẢNG LEVEL REQUIREMENTS
-- =====================================================

-- Bảng chứa yêu cầu XP cho từng level
CREATE TABLE IF NOT EXISTS "LevelRequirements" (
    "level" INTEGER PRIMARY KEY,
    "xp_required" INTEGER NOT NULL,
    "cumulative_xp" INTEGER NOT NULL,
    "tier_name" VARCHAR(50) NOT NULL,
    "tier_color" VARCHAR(20) NOT NULL,
    "created_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE "LevelRequirements" IS 'Bảng yêu cầu XP cho từng level';
COMMENT ON COLUMN "LevelRequirements"."level" IS 'Cấp độ (1-120+)';
COMMENT ON COLUMN "LevelRequirements"."xp_required" IS 'XP cần thiết để lên level này từ level trước';
COMMENT ON COLUMN "LevelRequirements"."cumulative_xp" IS 'Tổng XP tích lũy cần thiết để đạt level này';
COMMENT ON COLUMN "LevelRequirements"."tier_name" IS 'Tên tầng (Wood, Bronze, Silver, etc.)';
COMMENT ON COLUMN "LevelRequirements"."tier_color" IS 'Màu sắc đại diện cho tầng';

-- =====================================================
-- 2. TẠO BẢNG TITLES (DANH HIỆU)
-- =====================================================

CREATE TABLE IF NOT EXISTS "Titles" (
    "title_id" SERIAL PRIMARY KEY,
    "tier_name" VARCHAR(50) NOT NULL,
    "title_name" VARCHAR(100) NOT NULL,
    "title_display" VARCHAR(100) NOT NULL,
    "level_range_start" INTEGER NOT NULL,
    "level_range_end" INTEGER NOT NULL,
    "description" TEXT,
    "icon_url" VARCHAR(255),
    "color" VARCHAR(20) NOT NULL,
    "created_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE "Titles" IS 'Bảng danh hiệu theo tầng cấp độ';
COMMENT ON COLUMN "Titles"."tier_name" IS 'Tên tầng (Wood, Bronze, etc.)';
COMMENT ON COLUMN "Titles"."title_name" IS 'Tên danh hiệu (Tân Binh Gỗ, Chiến Binh Đồng, etc.)';
COMMENT ON COLUMN "Titles"."title_display" IS 'Tên hiển thị bên cạnh tên người chơi';
COMMENT ON COLUMN "Titles"."level_range_start" IS 'Level bắt đầu của tầng';
COMMENT ON COLUMN "Titles"."level_range_end" IS 'Level kết thúc của tầng';

-- =====================================================
-- 3. TẠO BẢNG BADGES (HUY HIỆU)
-- =====================================================

CREATE TABLE IF NOT EXISTS "Badges" (
    "badge_id" SERIAL PRIMARY KEY,
    "tier_name" VARCHAR(50) NOT NULL,
    "badge_name" VARCHAR(100) NOT NULL,
    "badge_description" TEXT,
    "icon_url" VARCHAR(255),
    "unlock_level" INTEGER NOT NULL,
    "rarity" VARCHAR(20) DEFAULT 'common',
    "created_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE "Badges" IS 'Bảng huy hiệu mở khóa theo cấp độ';
COMMENT ON COLUMN "Badges"."tier_name" IS 'Tên tầng tương ứng';
COMMENT ON COLUMN "Badges"."unlock_level" IS 'Level cần thiết để mở khóa';
COMMENT ON COLUMN "Badges"."rarity" IS 'Độ hiếm (common, rare, epic, legendary)';

-- =====================================================
-- 4. TẠO BẢNG USER_TITLES (DANH HIỆU CỦA USER)
-- =====================================================

CREATE TABLE IF NOT EXISTS "UserTitles" (
    "user_title_id" SERIAL PRIMARY KEY,
    "user_id" INTEGER NOT NULL,
    "title_id" INTEGER NOT NULL,
    "unlocked_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "is_active" BOOLEAN DEFAULT FALSE,
    FOREIGN KEY ("user_id") REFERENCES "Users"("user_id") ON DELETE CASCADE,
    FOREIGN KEY ("title_id") REFERENCES "Titles"("title_id") ON DELETE CASCADE,
    UNIQUE("user_id", "title_id")
);

COMMENT ON TABLE "UserTitles" IS 'Bảng danh hiệu đã mở khóa của user';
COMMENT ON COLUMN "UserTitles"."is_active" IS 'Danh hiệu đang được sử dụng';

-- =====================================================
-- 5. TẠO BẢNG USER_BADGES (HUY HIỆU CỦA USER)
-- =====================================================

CREATE TABLE IF NOT EXISTS "UserBadges" (
    "user_badge_id" SERIAL PRIMARY KEY,
    "user_id" INTEGER NOT NULL,
    "badge_id" INTEGER NOT NULL,
    "unlocked_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY ("user_id") REFERENCES "Users"("user_id") ON DELETE CASCADE,
    FOREIGN KEY ("badge_id") REFERENCES "Badges"("badge_id") ON DELETE CASCADE,
    UNIQUE("user_id", "badge_id")
);

COMMENT ON TABLE "UserBadges" IS 'Bảng huy hiệu đã mở khóa của user';

-- =====================================================
-- 6. CHÈN DỮ LIỆU LEVEL REQUIREMENTS (1-120)
-- =====================================================

-- Xóa dữ liệu cũ nếu có
DELETE FROM "LevelRequirements";

-- Chèn dữ liệu level requirements với công thức tăng dần
-- Level 1-12: Wood (100 XP mỗi level)
-- Level 13-24: Bronze (150 XP mỗi level)  
-- Level 25-36: Silver (200 XP mỗi level)
-- Level 37-48: Gold (250 XP mỗi level)
-- Level 49-60: Platinum (300 XP mỗi level)
-- Level 61-72: Onyx (350 XP mỗi level)
-- Level 73-84: Sapphire (400 XP mỗi level)
-- Level 85-96: Ruby (450 XP mỗi level)
-- Level 97-108: Amethyst (500 XP mỗi level)
-- Level 109-120+: Master (600 XP mỗi level)

WITH RECURSIVE level_data AS (
    -- Base case: Level 1
    SELECT 
        1 as level,
        100 as xp_required,
        0 as cumulative_xp,
        'Wood' as tier_name,
        '#8B4513' as tier_color
    
    UNION ALL
    
    -- Recursive case: Calculate next levels
    SELECT 
        level + 1,
        CASE 
            WHEN level + 1 <= 12 THEN 100
            WHEN level + 1 <= 24 THEN 150
            WHEN level + 1 <= 36 THEN 200
            WHEN level + 1 <= 48 THEN 250
            WHEN level + 1 <= 60 THEN 300
            WHEN level + 1 <= 72 THEN 350
            WHEN level + 1 <= 84 THEN 400
            WHEN level + 1 <= 96 THEN 450
            WHEN level + 1 <= 108 THEN 500
            ELSE 600
        END as xp_required,
        cumulative_xp + 
        CASE 
            WHEN level <= 12 THEN 100
            WHEN level <= 24 THEN 150
            WHEN level <= 36 THEN 200
            WHEN level <= 48 THEN 250
            WHEN level <= 60 THEN 300
            WHEN level <= 72 THEN 350
            WHEN level <= 84 THEN 400
            WHEN level <= 96 THEN 450
            WHEN level <= 108 THEN 500
            ELSE 600
        END as cumulative_xp,
        CASE 
            WHEN level + 1 <= 12 THEN 'Wood'
            WHEN level + 1 <= 24 THEN 'Bronze'
            WHEN level + 1 <= 36 THEN 'Silver'
            WHEN level + 1 <= 48 THEN 'Gold'
            WHEN level + 1 <= 60 THEN 'Platinum'
            WHEN level + 1 <= 72 THEN 'Onyx'
            WHEN level + 1 <= 84 THEN 'Sapphire'
            WHEN level + 1 <= 96 THEN 'Ruby'
            WHEN level + 1 <= 108 THEN 'Amethyst'
            ELSE 'Master'
        END as tier_name,
        CASE 
            WHEN level + 1 <= 12 THEN '#8B4513'
            WHEN level + 1 <= 24 THEN '#CD7F32'
            WHEN level + 1 <= 36 THEN '#C0C0C0'
            WHEN level + 1 <= 48 THEN '#FFD700'
            WHEN level + 1 <= 60 THEN '#E5E4E2'
            WHEN level + 1 <= 72 THEN '#353839'
            WHEN level + 1 <= 84 THEN '#0F52BA'
            WHEN level + 1 <= 96 THEN '#E0115F'
            WHEN level + 1 <= 108 THEN '#9966CC'
            ELSE '#FF6B35'
        END as tier_color
    FROM level_data
    WHERE level < 120
)
INSERT INTO "LevelRequirements" (level, xp_required, cumulative_xp, tier_name, tier_color)
SELECT level, xp_required, cumulative_xp, tier_name, tier_color FROM level_data;

-- =====================================================
-- 7. CHÈN DỮ LIỆU TITLES (DANH HIỆU)
-- =====================================================

INSERT INTO "Titles" (tier_name, title_name, title_display, level_range_start, level_range_end, description, color) VALUES
('Wood', 'Tân Binh Gỗ', 'Tân Binh Gỗ', 1, 12, 'Những bước đầu tiên trong hành trình học tập', '#8B4513'),
('Bronze', 'Chiến Binh Đồng', 'Chiến Binh Đồng', 13, 24, 'Đã có nền tảng kiến thức vững chắc', '#CD7F32'),
('Silver', 'Tinh Anh Bạc', 'Tinh Anh Bạc', 25, 36, 'Thể hiện sự xuất sắc trong học tập', '#C0C0C0'),
('Gold', 'Cao Thủ Vàng', 'Cao Thủ Vàng', 37, 48, 'Đạt đến trình độ cao trong lĩnh vực', '#FFD700'),
('Platinum', 'Bậc Thầy Bạch Kim', 'Bậc Thầy Bạch Kim', 49, 60, 'Thành thạo và am hiểu sâu sắc', '#E5E4E2'),
('Onyx', 'Thống Lĩnh Onyx', 'Thống Lĩnh Onyx', 61, 72, 'Dẫn dắt và truyền cảm hứng cho người khác', '#353839'),
('Sapphire', 'Hiền Triết Sapphire', 'Hiền Triết Sapphire', 73, 84, 'Trí tuệ sâu sắc và kinh nghiệm phong phú', '#0F52BA'),
('Ruby', 'Chúa Tể Ruby', 'Chúa Tể Ruby', 85, 96, 'Quyền năng và uy tín trong lĩnh vực', '#E0115F'),
('Amethyst', 'Đại Sư Amethyst', 'Đại Sư Amethyst', 97, 108, 'Đỉnh cao của sự hiểu biết và kỹ năng', '#9966CC'),
('Master', 'Bậc Thầy Vô Song', 'Bậc Thầy Vô Song', 109, 120, 'Đạt đến cảnh giới tối cao, không ai sánh bằng', '#FF6B35');

-- =====================================================
-- 8. CHÈN DỮ LIỆU BADGES (HUY HIỆU)
-- =====================================================

INSERT INTO "Badges" (tier_name, badge_name, badge_description, unlock_level, rarity) VALUES
('Wood', 'Huy Hiệu Gỗ', 'Hoàn thành giai đoạn đầu tiên của hành trình học tập', 1, 'common'),
('Bronze', 'Huy Hiệu Đồng', 'Chứng minh sự kiên trì và nỗ lực không ngừng', 13, 'common'),
('Silver', 'Huy Hiệu Bạc', 'Đạt được thành tích xuất sắc trong học tập', 25, 'rare'),
('Gold', 'Huy Hiệu Vàng', 'Thể hiện tài năng và sự xuất sắc vượt trội', 37, 'rare'),
('Platinum', 'Huy Hiệu Bạch Kim', 'Đạt đến trình độ thành thạo cao', 49, 'epic'),
('Onyx', 'Huy Hiệu Onyx', 'Biểu tượng của sự lãnh đạo và ảnh hưởng', 61, 'epic'),
('Sapphire', 'Huy Hiệu Sapphire', 'Đại diện cho trí tuệ và sự hiểu biết sâu sắc', 73, 'legendary'),
('Ruby', 'Huy Hiệu Ruby', 'Tượng trưng cho quyền lực và uy tín', 85, 'legendary'),
('Amethyst', 'Huy Hiệu Amethyst', 'Biểu tượng của sự hoàn hảo và tinh thông', 97, 'legendary'),
('Master', 'Huy Hiệu Bậc Thầy', 'Đỉnh cao của mọi thành tựu, không thể vượt qua', 109, 'legendary');

-- =====================================================
-- 9. TẠO INDEXES ĐỂ TỐI ƯU PERFORMANCE
-- =====================================================

-- Indexes cho LevelRequirements
CREATE INDEX IF NOT EXISTS "idx_level_requirements_level" ON "LevelRequirements"("level");
CREATE INDEX IF NOT EXISTS "idx_level_requirements_cumulative_xp" ON "LevelRequirements"("cumulative_xp");
CREATE INDEX IF NOT EXISTS "idx_level_requirements_tier" ON "LevelRequirements"("tier_name");

-- Indexes cho Titles
CREATE INDEX IF NOT EXISTS "idx_titles_tier" ON "Titles"("tier_name");
CREATE INDEX IF NOT EXISTS "idx_titles_level_range" ON "Titles"("level_range_start", "level_range_end");

-- Indexes cho Badges
CREATE INDEX IF NOT EXISTS "idx_badges_tier" ON "Badges"("tier_name");
CREATE INDEX IF NOT EXISTS "idx_badges_unlock_level" ON "Badges"("unlock_level");
CREATE INDEX IF NOT EXISTS "idx_badges_rarity" ON "Badges"("rarity");

-- Indexes cho UserTitles
CREATE INDEX IF NOT EXISTS "idx_user_titles_user_id" ON "UserTitles"("user_id");
CREATE INDEX IF NOT EXISTS "idx_user_titles_active" ON "UserTitles"("user_id", "is_active") WHERE "is_active" = true;

-- Indexes cho UserBadges
CREATE INDEX IF NOT EXISTS "idx_user_badges_user_id" ON "UserBadges"("user_id");

-- =====================================================
-- 10. CẬP NHẬT TRIGGER ĐỂ TỰ ĐỘNG CẬP NHẬT updated_at
-- =====================================================

-- Function để cập nhật updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Tạo triggers
CREATE TRIGGER update_level_requirements_updated_at BEFORE UPDATE ON "LevelRequirements" FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_titles_updated_at BEFORE UPDATE ON "Titles" FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_badges_updated_at BEFORE UPDATE ON "Badges" FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Commit transaction
COMMIT;

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Kiểm tra số lượng levels đã tạo
SELECT COUNT(*) as total_levels FROM "LevelRequirements";

-- Kiểm tra phân bố theo tầng
SELECT tier_name, COUNT(*) as level_count, MIN(level) as min_level, MAX(level) as max_level 
FROM "LevelRequirements" 
GROUP BY tier_name 
ORDER BY MIN(level);

-- Kiểm tra titles và badges
SELECT 'Titles' as type, COUNT(*) as count FROM "Titles"
UNION ALL
SELECT 'Badges' as type, COUNT(*) as count FROM "Badges";

-- Hiển thị một số level mẫu
SELECT level, xp_required, cumulative_xp, tier_name, tier_color 
FROM "LevelRequirements" 
WHERE level IN (1, 12, 13, 24, 25, 36, 37, 48, 49, 60, 61, 72, 73, 84, 85, 96, 97, 108, 109, 120)
ORDER BY level;
