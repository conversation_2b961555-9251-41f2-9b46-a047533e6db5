# Epic 3: <PERSON><PERSON><PERSON>ệ Thống Level/XP Mới với Visual Assets - Brownfield Enhancement

**Epic Goal**: T<PERSON><PERSON> hợp hệ thống level/XP 10-tier mới từ backend (đã hoàn thành trong TASK_1_1) vào giao diện frontend hiện tại, thay thế hệ thống level đơn giản cũ để sinh viên có trải nghiệm gamification phong phú hơn với titles, badges và rich visual assets.

**Business Context**: Backend đã hoàn thành hệ thống 10-tier level system với 120+ levels, titles, badges và comprehensive APIs. Frontend hiện tại vẫn sử dụng hệ thống level đơn giản (mỗi 100 XP = 1 level) cần được nâng cấp để tận dụng hệ thống mới.

**Integration Requirements**: Tích hợp với APIs mới từ TASK_1_1 và sử dụng visual assets có sẵn trong `/public/vector-ranks-pack/` để tạo trải nghiệm gamification rich và engaging.

## Epic Description

**Existing System Context:**

- Current functionality: Frontend sử dụng hệ thống level đơn giản với UserLevelBadge component cơ bản
- Technology stack: Next.js 15, React 19, TypeScript, Radix UI, Tailwind CSS
- Integration points:
  - `useGamification` hook trong `/lib/hooks/use-gamification`
  - `UserLevelBadge` component trong `/components/features/gamification/`
  - Dashboard pages: `/dashboard/page.tsx`
  - API services: `gamificationService` trong `/lib/services/`
- Visual assets: 120+ tier icons trong `/public/vector-ranks-pack/` (10 tiers × 12 variations each)

**Enhancement Details:**

- What's being added/changed:
  - Cập nhật API integration để sử dụng hệ thống 10-tier level mới
  - Nâng cấp UserLevelBadge để hiển thị tier names, icons và progress chi tiết
  - Thêm Title & Badge management components với rich visuals
  - Cập nhật dashboard để hiển thị achievements và tier progression
  - Image mapping system cho tier icons và badge visuals
- How it integrates:
  - Sử dụng API endpoints mới: `/gamification-level/`, `/titles/`
  - Mapping tier names với visual assets trong `/public/vector-ranks-pack/`
  - Backward compatibility với existing gamification APIs
- Success criteria:
  - Sinh viên thấy tier names và beautiful icons thay vì chỉ level numbers
  - Progress tracking chính xác theo XP requirements của từng tier
  - Title và Badge collection hiển thị với proper visuals
  - Smooth animations và transitions cho level progression
  - No regression trong existing functionality

## Story 3.1: Cập Nhật API Integration và Image Mapping System

Là một **developer**,
Tôi muốn **tích hợp APIs mới và tạo image mapping system cho tier visuals**,
Để **có foundation vững chắc cho việc hiển thị hệ thống level mới với rich graphics**.

### Acceptance Criteria

1. Tích hợp các API endpoints mới từ TASK_1_1:
   - `/gamification-level/my-progress` - User level progress
   - `/gamification-level/tiers` - Tier information
   - `/titles/my-titles` - User titles
   - `/titles/my-badges` - User badges
2. Cập nhật `gamificationService` để sử dụng APIs mới
3. Tạo image mapping utility `getTierIcon(tierName, levelInTier)`
4. Cập nhật TypeScript types cho tier system và visual assets
5. Implement fallback logic cho missing images và API errors
6. Cập nhật `useGamification` hook để sử dụng level calculation mới

### Integration Verification

- **IV1**: API calls return correct tier-based data structure với image paths
- **IV2**: Image mapping correctly resolves tier icons từ `/public/vector-ranks-pack/`
- **IV3**: TypeScript compilation successful với new types
- **IV4**: Existing gamification functionality không bị break

## Story 3.2: Nâng Cấp UserLevelBadge với Rich Tier Visuals

Là một **sinh viên**,
Tôi muốn **thấy tier icons đẹp mắt và thông tin chi tiết về level progression**,
Để **có motivation và hiểu rõ tiến độ học tập của mình trong hệ thống tier**.

### Acceptance Criteria

1. Cập nhật UserLevelBadge component để hiển thị:
   - Tier icons từ `/public/vector-ranks-pack/` based on current level
   - Tier names (Wood, Bronze, Silver, etc.) thay vì chỉ level numbers
   - Progress bar chính xác theo XP requirements của từng tier
   - Tier colors và styling theo design system
2. Dynamic icon selection dựa trên level trong tier (1-12 variations)
3. Enhanced tooltip với tier information và next tier preview
4. Smooth transitions và animations khi level up
5. Responsive design cho mobile và desktop
6. Maintain existing component API để không break existing usage

### Integration Verification

- **IV1**: Tier icons hiển thị correctly cho tất cả 10 tiers
- **IV2**: Progress calculation accurate theo tier XP requirements
- **IV3**: Component performance không giảm với image loading
- **IV4**: Mobile responsiveness maintained với new visuals

## Story 3.3: Xây Dựng Title & Badge Gallery với Interactive Management

Là một **sinh viên**,
Tôi muốn **xem và quản lý collection titles/badges của mình với giao diện đẹp**,
Để **có sense of achievement và có thể customize profile với titles yêu thích**.

### Acceptance Criteria

1. Tạo Title Gallery component:
   - Hiển thị tất cả titles đã unlock với tier icons
   - Show locked titles với preview và unlock requirements
   - Active title selection với visual feedback
   - Tier-based organization và filtering
2. Tạo Badge Collection component:
   - Badge grid với rarity-based styling (common, rare, epic, legendary)
   - Badge details modal với unlock criteria và description
   - Achievement unlock animations
   - Progress tracking cho achievement badges
3. Tích hợp vào student dashboard và profile pages
4. Add badge/title unlock notifications với celebratory animations
5. Search và filter functionality cho large collections

### Integration Verification

- **IV1**: Title selection correctly updates active title via API
- **IV2**: Badge collection accurately reflects user achievements
- **IV3**: Unlock animations smooth và engaging
- **IV4**: Performance optimized cho large badge/title collections

## Compatibility Requirements

- [x] Existing APIs remain unchanged (gamification APIs cũ vẫn hoạt động)
- [x] Database schema changes are backward compatible (TASK_1_1 đã đảm bảo)
- [x] UI changes follow existing patterns (sử dụng Radix UI và Tailwind CSS hiện có)
- [x] Performance impact is minimal (optimized image loading và caching)
- [x] Mobile responsiveness maintained với new visual components

## Risk Mitigation

- **Primary Risk**: Breaking existing gamification display và user experience
- **Mitigation**:
  - Progressive enhancement approach với feature flags
  - Comprehensive fallback logic cho API failures
  - Image preloading và lazy loading strategies
  - Thorough testing với existing user data
- **Rollback Plan**:
  - Revert API calls về gamification service cũ
  - Hide title/badge features với feature toggle
  - Restore original UserLevelBadge component

## Definition of Done

- [x] All stories completed with acceptance criteria met
- [x] Existing functionality verified through testing
- [x] Integration points working correctly với backend APIs mới
- [x] Visual assets properly integrated và optimized
- [x] Documentation updated appropriately
- [x] No regression in existing gamification features
- [x] Sinh viên có rich visual experience với tier progression, title management, và badge collection

---
