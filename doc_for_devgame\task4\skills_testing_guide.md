# 🧪 SKILLS SYSTEM TESTING GUIDE

## 📋 TESTING OVERVIEW

Hướng dẫn test toàn diện Skills System với 17 kỹ năng, t<PERSON>ch hợp Economy và Quiz Racing.

---

## 🛠️ SETUP & PREREQUISITES

### 1. Database Setup
```sql
-- Run skills system SQL
\i doc_for_devgame/task4/skills_system.sql

-- Verify tables created
\dt *skill*
-- Should show: Skills, UserSkills, QuizSkillLoadouts, SkillUsageHistory, ActiveSkillEffects
```

### 2. Server Setup
```bash
cd backend
npm start
# Server should start without errors
# Check logs for Skills routes registration
```

### 3. Test User Setup
```sql
-- Create test users with currency
UPDATE "Users" SET syncoin_balance = 1000, kristal_balance = 100 WHERE user_id IN (1, 2, 3);
```

---

## 🛒 SKILL SHOP TESTING

### Test 1: Browse Skills
```bash
# Get all skills
curl -X GET "http://localhost:8888/api/skills"

# Expected: 17 skills with categories, tiers, costs
# Verify: All 5 categories present (ATTACK, DEFENSE, BURST, SPECIAL, ULTIMATE)
```

### Test 2: Filter Skills
```bash
# Get attack skills only
curl -X GET "http://localhost:8888/api/skills?category=ATTACK"

# Expected: 4 skills (blackhole, steal, break, slow)

# Get S-tier skills
curl -X GET "http://localhost:8888/api/skills?tier=S"

# Expected: 2 Ultimate skills (king, phoenix)
```

### Test 3: User Authentication
```bash
# Login to get token
curl -X POST "http://localhost:8888/api/users/login" \
  -H "Content-Type: application/json" \
  -d '{"username": "testuser", "password": "password"}'

# Save token for subsequent requests
TOKEN="your_jwt_token_here"
```

### Test 4: Get User Skills (Empty Initially)
```bash
curl -X GET "http://localhost:8888/api/skills/my-skills" \
  -H "Authorization: Bearer $TOKEN"

# Expected: Empty skills array, total_owned: 0
```

### Test 5: Get Affordable Skills
```bash
curl -X GET "http://localhost:8888/api/skills/affordable" \
  -H "Authorization: Bearer $TOKEN"

# Expected: Skills user can afford with current balance
# Should exclude skills costing more than user's SynCoin/Kristal
```

### Test 6: Purchase Skills
```bash
# Purchase basic skill (100 SynCoin)
curl -X POST "http://localhost:3000/api/skills/purchase" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"skill_id": 4}'

# Expected: Success, balance reduced by 100
# Verify: User now owns the skill

# Try purchasing same skill again
curl -X POST "http://localhost:3000/api/skills/purchase" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"skill_id": 4}'

# Expected: Error - skill already owned
```

### Test 7: Purchase Premium Skills
```bash
# Purchase Ultimate skill (200 Kristal)
curl -X POST "http://localhost:3000/api/skills/purchase" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"skill_id": 16}'

# Expected: Success if user has 200 Kristal, error if insufficient
```

---

## ⚔️ QUIZ LOADOUT TESTING

### Test 8: Create Quiz Session
```sql
-- Create test quiz session
INSERT INTO "QuizSessions" (session_id, quiz_id, created_by, status) 
VALUES ('test_quiz_001', 1, 1, 'WAITING');
```

### Test 9: Create Loadout (Should Fail - Not Enough Skills)
```bash
curl -X POST "http://localhost:3000/api/skills/loadout" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "quiz_session_id": "test_quiz_001",
    "skill_ids": [1, 2, 3, 4]
  }'

# Expected: Error - user doesn't own all 4 skills
```

### Test 10: Purchase More Skills
```bash
# Purchase 3 more skills to have 4 total
for skill_id in 1 2 3; do
  curl -X POST "http://localhost:3000/api/skills/purchase" \
    -H "Authorization: Bearer $TOKEN" \
    -H "Content-Type: application/json" \
    -d "{\"skill_id\": $skill_id}"
done
```

### Test 11: Create Valid Loadout
```bash
curl -X POST "http://localhost:3000/api/skills/loadout" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "quiz_session_id": "test_quiz_001",
    "skill_ids": [1, 2, 3, 4]
  }'

# Expected: Success, loadout created with 4 skills
```

### Test 12: Get User Loadout
```bash
curl -X GET "http://localhost:3000/api/skills/loadout/test_quiz_001" \
  -H "Authorization: Bearer $TOKEN"

# Expected: User's 4-skill loadout for the quiz
```

### Test 13: Update Loadout
```bash
# Purchase another skill and update loadout
curl -X POST "http://localhost:3000/api/skills/purchase" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"skill_id": 5}'

# Update loadout
curl -X POST "http://localhost:3000/api/skills/loadout" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "quiz_session_id": "test_quiz_001",
    "skill_ids": [1, 2, 3, 5]
  }'

# Expected: Loadout updated, skill 4 replaced with skill 5
```

---

## ⚡ SKILL EXECUTION TESTING

### Test 14: Get Random Skill
```bash
curl -X GET "http://localhost:3000/api/skills/random/test_quiz_001" \
  -H "Authorization: Bearer $TOKEN"

# Expected: One random skill from user's 4-skill loadout
```

### Test 15: Execute Attack Skill
```bash
curl -X POST "http://localhost:3000/api/skills/execute" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "quiz_session_id": "test_quiz_001",
    "skill_id": 1,
    "target_user_id": 2,
    "question_number": 1,
    "energy_level": 100,
    "game_state": {
      "current_leaderboard": [
        {"user_id": 2, "position": 1, "points": 100},
        {"user_id": 1, "position": 2, "points": 80}
      ]
    }
  }'

# Expected: Success, skill executed, effect applied
```

### Test 16: Execute Defense Skill
```bash
curl -X POST "http://localhost:3000/api/skills/execute" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "quiz_session_id": "test_quiz_001",
    "skill_id": 5,
    "question_number": 2,
    "energy_level": 100
  }'

# Expected: Shield activated, immunity effect applied
```

### Test 17: Execute Burst Skill (with Risk)
```bash
curl -X POST "http://localhost:3000/api/skills/execute" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "quiz_session_id": "test_quiz_001",
    "skill_id": 9,
    "question_number": 3,
    "energy_level": 100
  }'

# Expected: Double points effect with 20% risk factor
```

### Test 18: Execute Special Skill
```bash
curl -X POST "http://localhost:3000/api/skills/execute" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "quiz_session_id": "test_quiz_001",
    "skill_id": 14,
    "target_user_id": 3,
    "question_number": 4,
    "energy_level": 100
  }'

# Expected: Position swap with target user
```

### Test 19: Execute Ultimate Skill
```bash
curl -X POST "http://localhost:3000/api/skills/execute" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "quiz_session_id": "test_quiz_001",
    "skill_id": 16,
    "question_number": 5,
    "energy_level": 100
  }'

# Expected: King mode activated, god mode effect
```

---

## 📊 EFFECTS & STATISTICS TESTING

### Test 20: Get Active Effects
```bash
curl -X GET "http://localhost:3000/api/skills/effects/test_quiz_001" \
  -H "Authorization: Bearer $TOKEN"

# Expected: List of active skill effects from previous executions
```

### Test 21: Get User Statistics
```bash
curl -X GET "http://localhost:3000/api/skills/stats/my-stats" \
  -H "Authorization: Bearer $TOKEN"

# Expected: User's skill usage statistics
```

### Test 22: Get Usage Statistics (Teacher/Admin)
```bash
# Login as teacher/admin
curl -X GET "http://localhost:3000/api/skills/stats/usage" \
  -H "Authorization: Bearer $TEACHER_TOKEN"

# Expected: Global skill usage statistics
```

---

## 🔍 ERROR TESTING

### Test 23: Invalid Skill Purchase
```bash
# Try purchasing non-existent skill
curl -X POST "http://localhost:3000/api/skills/purchase" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"skill_id": 999}'

# Expected: 404 Error - Skill not found
```

### Test 24: Insufficient Balance
```bash
# Reduce user balance
UPDATE "Users" SET syncoin_balance = 50 WHERE user_id = 1;

# Try purchasing expensive skill
curl -X POST "http://localhost:3000/api/skills/purchase" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"skill_id": 8}'

# Expected: 400 Error - Insufficient balance
```

### Test 25: Invalid Loadout
```bash
# Try creating loadout with duplicate skills
curl -X POST "http://localhost:3000/api/skills/loadout" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "quiz_session_id": "test_quiz_001",
    "skill_ids": [1, 1, 2, 3]
  }'

# Expected: 400 Error - Duplicate skills not allowed
```

### Test 26: Unauthorized Access
```bash
# Try accessing teacher endpoint without proper role
curl -X GET "http://localhost:3000/api/skills/stats/usage" \
  -H "Authorization: Bearer $STUDENT_TOKEN"

# Expected: 403 Error - Insufficient permissions
```

---

## ✅ SUCCESS CRITERIA

### Database Verification
```sql
-- Check skills data
SELECT COUNT(*) FROM "Skills"; -- Should be 17

-- Check user purchases
SELECT COUNT(*) FROM "UserSkills" WHERE user_id = 1; -- Should match purchases

-- Check loadouts
SELECT COUNT(*) FROM "QuizSkillLoadouts" WHERE user_id = 1; -- Should be 1

-- Check usage history
SELECT COUNT(*) FROM "SkillUsageHistory" WHERE user_id = 1; -- Should match executions

-- Check active effects
SELECT COUNT(*) FROM "ActiveSkillEffects" WHERE quiz_session_id = 'test_quiz_001'; -- Should have effects
```

### API Response Verification
- All endpoints return proper HTTP status codes
- Response format matches documentation
- Error messages are descriptive
- Authentication/authorization works correctly
- Currency deduction works properly
- Skill effects are applied correctly

### Integration Testing
- Skills integrate with existing Economy system
- Loadouts work with Quiz system
- Real-time events fire correctly (if Socket.IO implemented)
- Statistics track properly

---

## 🐛 COMMON ISSUES & SOLUTIONS

1. **Skills not loading**: Check database connection and table creation
2. **Purchase fails**: Verify user balance and skill ownership
3. **Loadout creation fails**: Ensure user owns all 4 skills
4. **Skill execution fails**: Check quiz session exists and user has loadout
5. **Statistics empty**: Execute some skills first to generate data

---

## 📝 TEST CHECKLIST

- [ ] All 17 skills load correctly
- [ ] Skill categories and tiers work
- [ ] Purchase system works with both currencies
- [ ] Loadout creation and updates work
- [ ] All 5 skill categories execute properly
- [ ] Effects are applied and tracked
- [ ] Statistics are generated correctly
- [ ] Error handling works for all edge cases
- [ ] Authentication and authorization work
- [ ] Database integrity maintained
