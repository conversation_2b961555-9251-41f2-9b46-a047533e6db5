# Technical Constraints and Integration Requirements

## Existing Technology Stack

**Languages**: TypeScript ^5, JavaScript (Node.js >=18.0.0)
**Frameworks**: Next.js 15.3.0 (App Router), React 19.0.0
**External Dependencies**: Radix <PERSON> (latest), Tailwind CSS ^4, Socket.IO 4.8.1 client

## Integration Approach

**API Integration Strategy**: Maintain tất cả existing API service files trong `src/services/api/`, preserve axios client configuration

**Frontend Integration Strategy**:

- Component Migration: Di chuyển components theo feature-based structure mà không break imports
- Route Preservation: Tất cả App Router routes giữ nguyên structure
- Service Layer: API services, Socket.IO client, auth utilities maintain current interfaces

## Code Organization and Standards

**File Structure Approach**:

```
src/
├── app/                    # Next.js App Router (KHÔNG THAY ĐỔI)
├── components/
│   ├── ui/                 # Pure UI components (Radix wrappers)
│   ├── features/           # Business logic components
│   └── charts/             # Chart.js components (GIỮ NGUYÊN)
├── lib/                    # Utilities và shared logic
├── services/               # API và external services (GIỮ NGUYÊN)
└── styles/                 # Global styles (GIỮ NGUYÊN)
```

**Naming Conventions**:

- Files: kebab-case, Components: PascalCase, Directories: kebab-case

## Risk Assessment and Mitigation

**Technical Risks**:

- Import path breaks (Risk cao) - Mitigation: Systematic find/replace với TypeScript compiler checking
- Component dependency cycles (Risk trung bình) - Mitigation: Careful dependency analysis
- Build time increase (Risk thấp) - Mitigation: Benchmark before/after

**Mitigation Strategies**:

1. Incremental migration theo batches
2. TypeScript compiler để catch import errors
3. Staging validation trước production deployment
