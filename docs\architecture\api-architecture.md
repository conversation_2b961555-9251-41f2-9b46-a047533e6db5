# API Architecture

## REST API Design

**Base URL**: `http://localhost:8888/api`

**Authentication**: Bearer JWT token trong Authorization header

### Core API Endpoints

**Authentication & User Management**:

```
POST /api/users/login                    # User authentication
GET /api/users                           # List users (Admin)
POST /api/users/createStudent            # Create student (Admin/Teacher)
POST /api/users/createTeacher            # Create teacher (Admin)
PUT /api/users/:id                       # Update user
DELETE /api/users/:id                    # Delete user (Admin)
```

**Program & Course Management**:

```
GET /api/programs                        # List programs
POST /api/programs                       # Create program (Admin)
PUT /api/programs/:id                    # Update program (Admin)
DELETE /api/programs/:id                 # Delete program (Admin)
GET /api/courses                         # List courses
POST /api/courses                        # Create course (Admin)
GET /api/subjects                        # List subjects
GET /api/chapters/subject/:subject_id    # Chapters by subject
```

**Quiz Management** (Core System):

```
GET /api/quizzes                         # List quizzes
POST /api/quizzes                        # Create quiz
PUT /api/quizzes/:id                     # Update quiz
DELETE /api/quizzes/:id                  # Delete quiz
POST /api/quizzes/:id/start              # Start quiz (Teacher)
POST /api/quizzes/:id/join               # Join quiz (Student)
POST /api/quizzes/realtime/answer        # Submit answer (Real-time)
GET /api/quizzes/:id/leaderboard         # Live leaderboard
GET /api/quizzes/pin/:pin                # Find quiz by PIN
POST /api/quizzes/:id/end                # End quiz (Teacher)
```

**Question & Answer Management**:

```
GET /api/questions                       # List questions
POST /api/questions                      # Create question
PUT /api/questions/:id                   # Update question
DELETE /api/questions/:id                # Delete question
POST /api/questions/bulk                 # Bulk import questions
GET /api/answers/quiz/:quiz_id           # Quiz answers
```

**Analytics & Reporting**:

```
GET /api/analytics/quiz/:id              # Quiz analytics
GET /api/analytics/student/:id           # Student performance
GET /api/analytics/class/:id             # Class performance
GET /api/reports/export/:type            # Export reports
```

**Gamification**:

```
GET /api/gamification/leaderboard        # Global leaderboard
GET /api/gamification/achievements       # User achievements
GET /api/gamification/streaks            # Learning streaks
```

## API Response Format

**Success Response**:

```json
{
  "success": true,
  "data": { ... },
  "message": "Operation successful"
}
```

**Error Response**:

```json
{
  "success": false,
  "error": "Error message",
  "code": "ERROR_CODE"
}
```

## Real-time Events (Socket.IO)

**Quiz Events**:

```javascript
// Server to Client
'quiz:started'           # Quiz has started
'quiz:question'          # New question broadcast
'quiz:answer_received'   # Answer acknowledgment
'quiz:leaderboard'       # Updated leaderboard
'quiz:ended'             # Quiz completed

// Client to Server
'join_quiz'              # Join quiz room
'submit_answer'          # Submit answer
'leave_quiz'             # Leave quiz room
```

**Room Structure**:

- `quiz:{quiz_id}` - All participants
- `quiz:{quiz_id}:teachers` - Teachers only
- `quiz:{quiz_id}:students` - Students only
