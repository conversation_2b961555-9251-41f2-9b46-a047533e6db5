# 🎮 SKILLS SYSTEM API DOCUMENTATION

## 📋 OVERVIEW

Skills System là hệ thống 17 kỹ năng cho Quiz Racing với 5 categories, tích hợp với Economy System và Real-time Socket.IO events.

### System Architecture
- **17 Skills** chia thành 5 categories: Attack, Defense, Burst, Special, Ultimate
- **Tier System**: S-Tier (Ultimate) → A-Tier (Premium) → B-Tier (Advanced) → C-Tier (Solid) → D-Tier (Basic)
- **Currency Integration**: SynCoin (primary) và Kristal (premium)
- **Quiz Racing Integration**: 4-skill loadouts với energy-based activation
- **Real-time Effects**: Socket.IO events cho skill execution và effects

---

## 🛒 SKILL SHOP ENDPOINTS

### 1. Get All Skills
```http
GET /api/skills
```

**Query Parameters:**
- `category` (optional): ATTACK, DEFENSE, BURST, SPECIAL, ULTIMATE
- `tier` (optional): S, A, B, C, D
- `cost_type` (optional): SYNCOIN, KRISTAL

**Response:**
```json
{
    "success": true,
    "message": "Skills fetched successfully",
    "data": {
        "success": true,
        "skills": [
            {
                "skill_id": 1,
                "skill_name": "Hố Đen",
                "skill_code": "blackhole",
                "skill_icon": "blackhole.png",
                "category": "ATTACK",
                "tier": "B",
                "cost_type": "SYNCOIN",
                "cost_amount": 150,
                "description": "Ngăn leader tạo khoảng cách xa",
                "effect_description": "Leader nhận 0 điểm trong 3 câu tiếp theo",
                "target_type": "LEADER"
            }
        ],
        "total": 17
    }
}
```

### 2. Get Skills by Category
```http
GET /api/skills/category/:category
```

**Parameters:**
- `category`: attack, defense, burst, special, ultimate

**Response:** Similar to Get All Skills but filtered by category

### 3. Get User's Owned Skills
```http
GET /api/skills/my-skills
Authorization: Bearer <token>
```

**Query Parameters:**
- `include_details` (optional): true/false (default: true)
- `equipped_only` (optional): true/false (default: false)

**Response:**
```json
{
    "success": true,
    "message": "User skills fetched successfully",
    "data": {
        "user_id": 123,
        "skills": [
            {
                "user_skill_id": 1,
                "skill_id": 1,
                "times_used": 15,
                "total_success": 12,
                "total_failure": 3,
                "is_equipped": true,
                "skill": {
                    "skill_name": "Hố Đen",
                    "skill_icon": "blackhole.png",
                    "category": "ATTACK"
                }
            }
        ],
        "total_owned": 8
    }
}
```

### 4. Get Affordable Skills
```http
GET /api/skills/affordable
Authorization: Bearer <token>
```

**Response:**
```json
{
    "success": true,
    "data": {
        "user_id": 123,
        "user_balances": {
            "syncoin_balance": 500,
            "kristal_balance": 25
        },
        "affordable_skills": [...],
        "count": 12
    }
}
```

### 5. Purchase Skill
```http
POST /api/skills/purchase
Authorization: Bearer <token>
Content-Type: application/json

{
    "skill_id": 1
}
```

**Response:**
```json
{
    "success": true,
    "message": "Skill purchased successfully",
    "data": {
        "success": true,
        "userSkill": {...},
        "balance_before": 500,
        "balance_after": 350
    }
}
```

---

## ⚔️ QUIZ LOADOUT ENDPOINTS

### 6. Create/Update Quiz Loadout
```http
POST /api/skills/loadout
Authorization: Bearer <token>
Content-Type: application/json

{
    "quiz_session_id": "quiz_12345",
    "skill_ids": [1, 5, 9, 13]
}
```

**Response:**
```json
{
    "success": true,
    "message": "Quiz loadout created successfully",
    "data": {
        "success": true,
        "loadout": {
            "loadout_id": 1,
            "quiz_session_id": "quiz_12345",
            "user_id": 123,
            "skill_slot_1": 1,
            "skill_slot_2": 5,
            "skill_slot_3": 9,
            "skill_slot_4": 13
        }
    }
}
```

### 7. Get User's Quiz Loadout
```http
GET /api/skills/loadout/:quiz_session_id
Authorization: Bearer <token>
```

**Response:** Returns user's 4-skill loadout for the quiz session

### 8. Get All Quiz Loadouts (Teacher/Admin)
```http
GET /api/skills/loadouts/:quiz_session_id
Authorization: Bearer <token>
```

**Access:** Teachers and Admins only

**Response:**
```json
{
    "success": true,
    "data": {
        "quiz_session_id": "quiz_12345",
        "loadouts": [
            {
                "user": {
                    "user_id": 123,
                    "username": "student1",
                    "full_name": "Nguyễn Văn A"
                },
                "skill1": {
                    "skill_name": "Hố Đen",
                    "skill_icon": "blackhole.png",
                    "category": "ATTACK"
                },
                "skill2": {...},
                "skill3": {...},
                "skill4": {...}
            }
        ],
        "participant_count": 25
    }
}
```

---

## ⚡ SKILL EXECUTION ENDPOINTS

### 9. Execute Skill During Quiz
```http
POST /api/skills/execute
Authorization: Bearer <token>
Content-Type: application/json

{
    "quiz_session_id": "quiz_12345",
    "skill_id": 1,
    "target_user_id": 456,
    "question_number": 5,
    "energy_level": 100,
    "game_state": {
        "current_leaderboard": [...],
        "question_type": "multiple_choice"
    }
}
```

**Response:**
```json
{
    "success": true,
    "message": "Skill executed successfully",
    "data": {
        "success": true,
        "skill_name": "Hố Đen",
        "execution_result": "SUCCESS",
        "effect_data": {
            "points_multiplier": 0
        },
        "points_affected": 0
    }
}
```

### 10. Get Random Skill from Loadout
```http
GET /api/skills/random/:quiz_session_id
Authorization: Bearer <token>
```

**Usage:** Called when user's energy reaches 100%

**Response:**
```json
{
    "success": true,
    "message": "Random skill selected",
    "data": {
        "skill": {
            "skill_id": 1,
            "skill_name": "Hố Đen",
            "skill_icon": "blackhole.png",
            "category": "ATTACK",
            "description": "Ngăn leader tạo khoảng cách xa",
            "target_type": "LEADER"
        }
    }
}
```

### 11. Get Active Skill Effects
```http
GET /api/skills/effects/:quiz_session_id?user_id=123
Authorization: Bearer <token>
```

**Query Parameters:**
- `user_id` (optional): Filter effects for specific user

**Response:**
```json
{
    "success": true,
    "data": {
        "quiz_session_id": "quiz_12345",
        "active_effects": [
            {
                "effect_id": 1,
                "affected_user_id": 123,
                "skill_id": 1,
                "effect_type": "BLACKHOLE",
                "questions_remaining": 2,
                "effect_data": {
                    "points_multiplier": 0
                },
                "skill": {
                    "skill_name": "Hố Đen",
                    "skill_icon": "blackhole.png"
                }
            }
        ],
        "count": 3
    }
}
```

---

## 📊 STATISTICS ENDPOINTS

### 12. Get Skill Usage Statistics (Teacher/Admin)
```http
GET /api/skills/stats/usage?limit=10&timeframe=week&quiz_session_id=quiz_12345
Authorization: Bearer <token>
```

**Query Parameters:**
- `limit` (optional): Number of results (default: 10)
- `timeframe` (optional): day, week, month, year
- `quiz_session_id` (optional): Filter by specific quiz

**Response:**
```json
{
    "success": true,
    "data": {
        "skill_usage_stats": [
            {
                "skill_id": 1,
                "usage_count": 45,
                "success_count": 38,
                "avg_points_impact": 12.5,
                "skill": {
                    "skill_name": "Hố Đen",
                    "category": "ATTACK",
                    "tier": "B"
                }
            }
        ]
    }
}
```

### 13. Get User's Skill Statistics
```http
GET /api/skills/stats/my-stats
Authorization: Bearer <token>
```

**Response:**
```json
{
    "success": true,
    "data": {
        "user_id": 123,
        "skill_statistics": [
            {
                "skill_id": 1,
                "times_used": 15,
                "total_success": 12,
                "total_failure": 3,
                "success_rate": 80.00,
                "skill": {
                    "skill_name": "Hố Đen",
                    "category": "ATTACK"
                }
            }
        ]
    }
}
```

---

## 🔥 SKILL CATEGORIES & EFFECTS

### Attack Skills (4 skills)
- **Blackhole**: Leader nhận 0 điểm trong 3 câu
- **Steal**: Lấy 50% điểm từ người trên, rủi ro -10% nếu họ sai
- **Break**: Reset streak cao nhất về 0
- **Slow**: Giảm speed bonus từ 5s xuống 3s trong 2 câu

### Defense Skills (3 skills)
- **Shield**: Miễn nhiễm tấn công trong 2 câu
- **Lock**: Khóa thứ hạng trong 2 câu
- **Cleanse**: Loại bỏ hiệu ứng xấu

### Burst Skills (5 skills)
- **Double**: Nhân đôi điểm câu tiếp theo (rủi ro -20%)
- **Lucky**: Tăng 50% điểm an toàn
- **Triple**: Nhân ba điểm câu tiếp theo (rủi ro -30%)
- **Perfect**: Đảm bảo câu trả lời đúng + speed bonus
- **Quintuple**: Nhân năm điểm câu tiếp theo (rủi ro -50%)

### Special Skills (3 skills)
- **Swap**: Hoán đổi thứ hạng với player bất kỳ
- **Dice**: Nhận 1 trong 6 hiệu ứng tích cực ngẫu nhiên
- **Energy**: Hồi 50% năng lượng cho tất cả người chơi

### Ultimate Skills (2 skills)
- **King**: Miễn nhiễm mọi tấn công + điểm x2 trong 3 câu
- **Phoenix**: Nếu ở bottom 3, nhảy lên top 3 + shield 2 câu

---

## 🔌 SOCKET.IO EVENTS

### Client → Server Events
```javascript
// Không có client events - tất cả thông qua REST API
```

### Server → Client Events
```javascript
// Skill purchase notification
socket.on('skill_purchased', (data) => {
    // data: { user_id, skill_id, balance_after, timestamp }
});

// Loadout update notification
socket.on('loadout_updated', (data) => {
    // data: { user_id, skills: [...], timestamp }
});

// Skill execution notification
socket.on('skill_executed', (data) => {
    // data: { user_id, skill_id, skill_name, target_user_id, execution_result, effect_data, points_affected, question_number, timestamp }
});

// Effects processing notification
socket.on('effects_processed', (data) => {
    // data: { expired_effects, continuing_effects, timestamp }
});
```

---

## ⚠️ ERROR CODES

- **400**: Bad Request - Invalid parameters
- **401**: Unauthorized - Invalid or missing token
- **403**: Forbidden - Insufficient permissions
- **404**: Not Found - Resource not found
- **500**: Internal Server Error

---

## 🎯 INTEGRATION NOTES

1. **Quiz Racing Flow**: Students select 4 skills → Join quiz → Energy builds up → Random skill offered at 100% → Execute or save
2. **Currency Integration**: Skills purchased with SynCoin/Kristal earned from Quiz Racing
3. **Real-time Updates**: All skill actions broadcast via Socket.IO for live quiz experience
4. **Effect Processing**: Skill effects automatically processed each question transition
5. **Statistics Tracking**: Comprehensive analytics for skill usage and effectiveness
