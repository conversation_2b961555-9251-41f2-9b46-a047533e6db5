"use client";

import React, { createContext, useContext, useState, useCallback } from "react";
import { UnlockNotification, UnlockToast } from "./unlock-notification";
import { UnlockAnimation, useUnlockAnimation } from "./unlock-animation";
import { UserBadgeData, UserTitleData } from "@/lib/types/gamification";

interface NotificationContextType {
  // Notification methods
  showUnlockNotification: (
    item: UserBadgeData | UserTitleData,
    type: "badge" | "title"
  ) => void;
  showUnlockToast: (
    title: string,
    description: string,
    type: "badge" | "title"
  ) => void;
  showUnlockAnimation: (
    item: UserBadgeData | UserTitleData,
    type: "badge" | "title"
  ) => void;

  // State
  isAnimationPlaying: boolean;
}

const NotificationContext = createContext<NotificationContextType | undefined>(
  undefined
);

interface NotificationProviderProps {
  children: React.ReactNode;
}

export const NotificationProvider: React.FC<NotificationProviderProps> = ({
  children,
}) => {
  // Notification state
  const [notifications, setNotifications] = useState<
    Array<{
      id: string;
      item: UserBadgeData | UserTitleData;
      type: "badge" | "title";
      timestamp: number;
    }>
  >([]);

  const [toasts, setToasts] = useState<
    Array<{
      id: string;
      title: string;
      description: string;
      type: "badge" | "title";
      timestamp: number;
    }>
  >([]);

  // Animation hook
  const {
    currentAnimation,
    addToQueue: addAnimationToQueue,
    handleAnimationComplete,
    isAnimating,
  } = useUnlockAnimation();

  // Notification methods
  const showUnlockNotification = useCallback(
    (item: UserBadgeData | UserTitleData, type: "badge" | "title") => {
      const id = `notification-${Date.now()}-${Math.random()}`;
      const notification = {
        id,
        item,
        type,
        timestamp: Date.now(),
      };

      setNotifications((prev) => [...prev, notification]);
    },
    []
  );

  const showUnlockToast = useCallback(
    (title: string, description: string, type: "badge" | "title") => {
      const id = `toast-${Date.now()}-${Math.random()}`;
      const toast = {
        id,
        title,
        description,
        type,
        timestamp: Date.now(),
      };

      setToasts((prev) => [...prev, toast]);
    },
    []
  );

  const showUnlockAnimation = useCallback(
    (item: UserBadgeData | UserTitleData, type: "badge" | "title") => {
      addAnimationToQueue(item, type);
    },
    [addAnimationToQueue]
  );

  // Remove notification
  const removeNotification = useCallback((id: string) => {
    setNotifications((prev) => prev.filter((n) => n.id !== id));
  }, []);

  // Remove toast
  const removeToast = useCallback((id: string) => {
    setToasts((prev) => prev.filter((t) => t.id !== id));
  }, []);

  // Handle view details for notifications
  const handleViewDetails = useCallback(
    (item: UserBadgeData | UserTitleData, type: "badge" | "title") => {
      // TODO: Implement navigation to badge/title details
      console.log("View details for:", type, item);
    },
    []
  );

  const contextValue: NotificationContextType = {
    showUnlockNotification,
    showUnlockToast,
    showUnlockAnimation,
    isAnimationPlaying: isAnimating,
  };

  return (
    <NotificationContext.Provider value={contextValue}>
      {children}

      {/* Render notifications */}
      {notifications.map((notification) => (
        <UnlockNotification
          key={notification.id}
          isVisible={true}
          onClose={() => removeNotification(notification.id)}
          onViewDetails={() =>
            handleViewDetails(notification.item, notification.type)
          }
          item={notification.item}
          type={notification.type}
          position="top-right"
          autoClose={true}
          autoCloseDelay={6000}
        />
      ))}

      {/* Render toasts */}
      {toasts.map((toast, index) => (
        <UnlockToast
          key={toast.id}
          isVisible={true}
          onClose={() => removeToast(toast.id)}
          title={toast.title}
          description={toast.description}
          type={toast.type}
          className={`top-${4 + index * 16}`} // Stack toasts
        />
      ))}

      {/* Render animation */}
      {currentAnimation && (
        <UnlockAnimation
          isVisible={true}
          onComplete={handleAnimationComplete}
          item={currentAnimation.item}
          type={currentAnimation.type}
        />
      )}
    </NotificationContext.Provider>
  );
};

// Hook to use notification context
export const useNotifications = () => {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error(
      "useNotifications must be used within a NotificationProvider"
    );
  }
  return context;
};

// Convenience hooks for specific notification types
export const useUnlockNotifications = () => {
  const { showUnlockNotification, showUnlockToast, showUnlockAnimation } =
    useNotifications();

  return {
    notifyBadgeUnlock: (badge: UserBadgeData) => {
      showUnlockAnimation(badge, "badge");
      setTimeout(() => {
        showUnlockNotification(badge, "badge");
      }, 3000);
    },

    notifyTitleUnlock: (title: UserTitleData) => {
      showUnlockAnimation(title, "title");
      setTimeout(() => {
        showUnlockNotification(title, "title");
      }, 3000);
    },

    notifyQuickBadge: (badgeName: string) => {
      showUnlockToast(
        "🎉 Huy hiệu mới!",
        `Bạn đã nhận được: ${badgeName}`,
        "badge"
      );
    },

    notifyQuickTitle: (titleName: string) => {
      showUnlockToast(
        "👑 Danh hiệu mới!",
        `Bạn đã mở khóa: ${titleName}`,
        "title"
      );
    },
  };
};

// Example usage component for testing
export const NotificationTester: React.FC = () => {
  const {
    notifyBadgeUnlock,
    notifyTitleUnlock,
    notifyQuickBadge,
    notifyQuickTitle,
  } = useUnlockNotifications();

  // Mock data for testing
  const mockBadge: UserBadgeData = {
    user_badge_id: 1,
    badge_id: 1,
    unlocked_at: new Date().toISOString(),
    Badge: {
      badge_name: "Người Mới Bắt Đầu",
      description: "Hoàn thành quiz đầu tiên của bạn",
      rarity: "common",
      tier_name: "wood",
      unlock_level: 1,
      icon_path: "/badges/beginner.png",
    },
  };

  const mockTitle: UserTitleData = {
    user_title_id: 1,
    title_id: 1,
    is_active: false,
    unlocked_at: new Date().toISOString(),
    Title: {
      title_name: "Tân Binh Gỗ",
      title_display: "Tân Binh",
      tier_name: "wood",
      color: "#8B4513",
      unlock_level: 1,
    },
  };

  return (
    <div className="fixed bottom-4 left-4 space-y-2 z-50">
      <button
        onClick={() => notifyBadgeUnlock(mockBadge)}
        className="block px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700"
      >
        Test Badge Unlock
      </button>

      <button
        onClick={() => notifyTitleUnlock(mockTitle)}
        className="block px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
      >
        Test Title Unlock
      </button>

      <button
        onClick={() => notifyQuickBadge("Streak Master")}
        className="block px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
      >
        Test Quick Badge
      </button>

      <button
        onClick={() => notifyQuickTitle("Quiz Champion")}
        className="block px-4 py-2 bg-yellow-600 text-white rounded hover:bg-yellow-700"
      >
        Test Quick Title
      </button>
    </div>
  );
};

export default NotificationProvider;
