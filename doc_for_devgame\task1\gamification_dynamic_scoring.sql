-- =====================================================
-- DYNAMIC SCORING SYSTEM - DATABASE ENHANCEMENTS
-- Task 1.3: T<PERSON><PERSON> hợp hệ thống điểm động trong Quiz
-- =====================================================

-- 1. Thêm cột difficulty cho Questions table (nếu chưa có)
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'Questions' AND column_name = 'difficulty') THEN
        ALTER TABLE "Questions" ADD COLUMN difficulty VARCHAR(20) DEFAULT 'medium';
        
        -- Update existing questions with random difficulty for testing
        UPDATE "Questions" SET difficulty = 
            CASE 
                WHEN RANDOM() < 0.2 THEN 'easy'
                WHEN RANDOM() < 0.6 THEN 'medium'  
                WHEN RANDOM() < 0.9 THEN 'hard'
                ELSE 'expert'
            END;
    END IF;
END $$;

-- 2. Thêm scoring metadata cho QuizResults
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'QuizResults' AND column_name = 'scoring_details') THEN
        ALTER TABLE "QuizResults" ADD COLUMN scoring_details JSONB DEFAULT '{}';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'QuizResults' AND column_name = 'perfect_bonuses') THEN
        ALTER TABLE "QuizResults" ADD COLUMN perfect_bonuses JSONB DEFAULT '[]';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'QuizResults' AND column_name = 'max_streak') THEN
        ALTER TABLE "QuizResults" ADD COLUMN max_streak INTEGER DEFAULT 0;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'QuizResults' AND column_name = 'average_response_time') THEN
        ALTER TABLE "QuizResults" ADD COLUMN average_response_time INTEGER DEFAULT 0;
    END IF;
END $$;

-- 3. Thêm detailed scoring cho UserQuestionHistory
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'UserQuestionHistory' AND column_name = 'points_earned') THEN
        ALTER TABLE "UserQuestionHistory" ADD COLUMN points_earned INTEGER DEFAULT 0;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'UserQuestionHistory' AND column_name = 'scoring_breakdown') THEN
        ALTER TABLE "UserQuestionHistory" ADD COLUMN scoring_breakdown JSONB DEFAULT '{}';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'UserQuestionHistory' AND column_name = 'bonuses_earned') THEN
        ALTER TABLE "UserQuestionHistory" ADD COLUMN bonuses_earned TEXT[] DEFAULT '{}';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'UserQuestionHistory' AND column_name = 'streak_at_time') THEN
        ALTER TABLE "UserQuestionHistory" ADD COLUMN streak_at_time INTEGER DEFAULT 0;
    END IF;
END $$;

-- 4. Create QuizScoringStats table for detailed analytics
CREATE TABLE IF NOT EXISTS "QuizScoringStats" (
    stat_id SERIAL PRIMARY KEY,
    quiz_id INTEGER NOT NULL REFERENCES "Quizzes"(quiz_id) ON DELETE CASCADE,
    user_id INTEGER NOT NULL REFERENCES "Users"(user_id) ON DELETE CASCADE,
    
    -- Basic stats
    total_score INTEGER NOT NULL DEFAULT 0,
    base_score INTEGER NOT NULL DEFAULT 0,
    bonus_score INTEGER NOT NULL DEFAULT 0,
    
    -- Speed stats
    total_speed_bonus INTEGER DEFAULT 0,
    fastest_answer_time INTEGER DEFAULT 0,
    slowest_answer_time INTEGER DEFAULT 0,
    
    -- Streak stats  
    max_streak INTEGER DEFAULT 0,
    total_streak_bonus INTEGER DEFAULT 0,
    streak_breaks INTEGER DEFAULT 0,
    
    -- Perfect bonuses
    perfect_score_bonus INTEGER DEFAULT 0,
    perfect_speed_bonus INTEGER DEFAULT 0,
    perfect_streak_bonus INTEGER DEFAULT 0,
    flawless_victory_bonus INTEGER DEFAULT 0,
    
    -- Difficulty breakdown
    easy_questions_correct INTEGER DEFAULT 0,
    medium_questions_correct INTEGER DEFAULT 0,
    hard_questions_correct INTEGER DEFAULT 0,
    expert_questions_correct INTEGER DEFAULT 0,
    
    -- Time stats
    total_time_spent INTEGER DEFAULT 0,
    time_bonus_earned INTEGER DEFAULT 0,
    
    -- Metadata
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(quiz_id, user_id)
);

-- 5. Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_quiz_scoring_stats_quiz_id ON "QuizScoringStats"(quiz_id);
CREATE INDEX IF NOT EXISTS idx_quiz_scoring_stats_user_id ON "QuizScoringStats"(user_id);
CREATE INDEX IF NOT EXISTS idx_quiz_scoring_stats_total_score ON "QuizScoringStats"(total_score DESC);

CREATE INDEX IF NOT EXISTS idx_questions_difficulty ON "Questions"(difficulty);
CREATE INDEX IF NOT EXISTS idx_user_question_history_points ON "UserQuestionHistory"(points_earned DESC);
CREATE INDEX IF NOT EXISTS idx_user_question_history_streak ON "UserQuestionHistory"(streak_at_time DESC);

-- 6. Create view for scoring leaderboards
CREATE OR REPLACE VIEW "ScoringLeaderboard" AS
SELECT 
    u.user_id,
    u.name,
    u.email,
    qss.quiz_id,
    q.name as quiz_name,
    qss.total_score,
    qss.base_score,
    qss.bonus_score,
    qss.max_streak,
    qss.perfect_score_bonus + qss.perfect_speed_bonus + qss.perfect_streak_bonus + qss.flawless_victory_bonus as total_perfect_bonus,
    CASE 
        WHEN qss.flawless_victory_bonus > 0 THEN 'Flawless Victory'
        WHEN qss.perfect_score_bonus > 0 AND qss.perfect_speed_bonus > 0 THEN 'Speed Master'
        WHEN qss.perfect_score_bonus > 0 THEN 'Perfect Score'
        WHEN qss.max_streak >= 20 THEN 'Legendary Streak'
        WHEN qss.max_streak >= 15 THEN 'Unstoppable'
        WHEN qss.max_streak >= 10 THEN 'On Fire'
        WHEN qss.max_streak >= 5 THEN 'Hot Streak'
        ELSE 'Good Job'
    END as achievement_title,
    qss.created_at
FROM "QuizScoringStats" qss
JOIN "Users" u ON qss.user_id = u.user_id
JOIN "Quizzes" q ON qss.quiz_id = q.quiz_id
ORDER BY qss.total_score DESC, qss.created_at ASC;

-- 7. Create function to calculate scoring statistics
CREATE OR REPLACE FUNCTION calculate_quiz_scoring_stats(p_quiz_id INTEGER, p_user_id INTEGER)
RETURNS TABLE(
    total_score INTEGER,
    base_score INTEGER,
    bonus_score INTEGER,
    max_streak INTEGER,
    perfect_bonuses JSONB
) AS $$
DECLARE
    result_record RECORD;
BEGIN
    -- Get scoring data from UserQuestionHistory
    SELECT 
        COALESCE(SUM(points_earned), 0) as total_points,
        COALESCE(SUM(CASE WHEN (scoring_breakdown->>'base_points')::INTEGER > 0 
                         THEN (scoring_breakdown->>'base_points')::INTEGER ELSE 0 END), 0) as base_points,
        COALESCE(MAX(streak_at_time), 0) as max_streak_value
    INTO result_record
    FROM "UserQuestionHistory"
    WHERE quiz_id = p_quiz_id AND user_id = p_user_id;
    
    total_score := COALESCE(result_record.total_points, 0);
    base_score := COALESCE(result_record.base_points, 0);
    bonus_score := total_score - base_score;
    max_streak := COALESCE(result_record.max_streak_value, 0);
    perfect_bonuses := '[]'::JSONB;
    
    RETURN NEXT;
END;
$$ LANGUAGE plpgsql;

-- 8. Sample data for testing (optional)
-- Update some questions with specific difficulties for testing
UPDATE "Questions" SET difficulty = 'easy' WHERE question_id IN (
    SELECT question_id FROM "Questions" ORDER BY RANDOM() LIMIT 5
);

UPDATE "Questions" SET difficulty = 'hard' WHERE question_id IN (
    SELECT question_id FROM "Questions" WHERE difficulty != 'easy' ORDER BY RANDOM() LIMIT 3
);

UPDATE "Questions" SET difficulty = 'expert' WHERE question_id IN (
    SELECT question_id FROM "Questions" WHERE difficulty NOT IN ('easy', 'hard') ORDER BY RANDOM() LIMIT 2
);

-- 9. Add comments for documentation
COMMENT ON TABLE "QuizScoringStats" IS 'Detailed scoring statistics for quiz attempts with dynamic scoring system';
COMMENT ON COLUMN "QuizScoringStats".total_score IS 'Final score including all bonuses and multipliers';
COMMENT ON COLUMN "QuizScoringStats".base_score IS 'Score from correct answers only, no bonuses';
COMMENT ON COLUMN "QuizScoringStats".bonus_score IS 'Additional points from speed, streak, difficulty bonuses';
COMMENT ON COLUMN "QuizScoringStats".max_streak IS 'Longest streak of correct answers in this quiz';
COMMENT ON COLUMN "QuizScoringStats".flawless_victory_bonus IS 'Bonus for achieving perfect score + speed + streak';

COMMENT ON VIEW "ScoringLeaderboard" IS 'Leaderboard view with scoring details and achievement titles';

-- 10. Grant permissions (adjust as needed)
-- GRANT SELECT, INSERT, UPDATE ON "QuizScoringStats" TO quiz_app_user;
-- GRANT SELECT ON "ScoringLeaderboard" TO quiz_app_user;

-- =====================================================
-- COMPLETION MESSAGE
-- =====================================================
DO $$
BEGIN
    RAISE NOTICE '✅ Dynamic Scoring System database setup completed successfully!';
    RAISE NOTICE '📊 Added scoring columns to Questions, QuizResults, UserQuestionHistory';
    RAISE NOTICE '📈 Created QuizScoringStats table for detailed analytics';
    RAISE NOTICE '🏆 Created ScoringLeaderboard view for rankings';
    RAISE NOTICE '⚡ Added performance indexes for fast queries';
    RAISE NOTICE '🎯 Ready for dynamic scoring implementation!';
END $$;
