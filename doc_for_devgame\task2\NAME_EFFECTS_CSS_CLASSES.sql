-- =====================================================
-- NAME EFFECTS CSS CLASSES UPDATE
-- =====================================================
-- File: NAME_EFFECTS_CSS_CLASSES.sql
-- Purpose: Update Name Effects to store CSS class names for frontend
-- Frontend will create corresponding CSS classes based on these names
-- Date: 2025-07-28

-- Clear existing name effects data
DELETE FROM "UserInventory" WHERE item_type = 'NAME_EFFECT';
DELETE FROM "NameEffects";

-- Reset name effects sequence
ALTER SEQUENCE "NameEffects_effect_id_seq" RESTART WITH 1;

-- =====================================================
-- INSERT NAME EFFECTS WITH CSS CLASS NAMES
-- =====================================================

INSERT INTO "NameEffects" ("effect_name", "effect_code", "description", "css_class", "tier_name", "unlock_level", "is_active", "sort_order") VALUES

-- Silver Tier (Level 21-30) - Simple Color Effects
('Hiệu Ứng Bạc', 'SILVER_BASIC', 'Tên màu bạc thanh lịch', 'name-effect-silver-basic', 'Silver', 21, true, 1),
('Hiệu Ứng Bạc Sáng', 'SILVER_BRIGHT', 'Tên bạc với độ sáng cao', 'name-effect-silver-bright', 'Silver', 25, true, 2),

-- Gold Tier (Level 31-40) - Enhanced Color Effects
('Hiệu Ứng Vàng', 'GOLD_BASIC', 'Tên màu vàng rực rỡ', 'name-effect-gold-basic', 'Gold', 31, true, 3),
('Hiệu Ứng Vàng Đậm', 'GOLD_BOLD', 'Tên vàng đậm nổi bật', 'name-effect-gold-bold', 'Gold', 35, true, 4),

-- Platinum Tier (Level 41-50) - Subtle Glow Effects
('Hiệu Ứng Bạch Kim', 'PLATINUM_BASIC', 'Tên bạch kim sang trọng', 'name-effect-platinum-basic', 'Platinum', 41, true, 5),
('Hiệu Ứng Bạch Kim Phát Sáng', 'PLATINUM_GLOW', 'Tên có hào quang bạch kim nhẹ', 'name-effect-platinum-glow', 'Platinum', 45, true, 6),

-- Onyx Tier (Level 51-60) - Dark Glow Effects
('Hiệu Ứng Onyx', 'ONYX_BASIC', 'Tên có viền xám đậm sang trọng', 'name-effect-onyx-basic', 'Onyx', 51, true, 7),
('Hiệu Ứng Onyx Phát Sáng', 'ONYX_GLOW', 'Tên có hào quang xám mạnh', 'name-effect-onyx-glow', 'Onyx', 55, true, 8),

-- Sapphire Tier (Level 61-70) - Blue Animation Effects
('Hiệu Ứng Sapphire', 'SAPPHIRE_BASIC', 'Tên màu xanh dương tinh khiết', 'name-effect-sapphire-basic', 'Sapphire', 61, true, 9),
('Hiệu Ứng Sapphire Sóng', 'SAPPHIRE_WAVE', 'Tên có hiệu ứng sóng nước xanh', 'name-effect-sapphire-wave', 'Sapphire', 65, true, 10),

-- Ruby Tier (Level 71-80) - Red Animation Effects
('Hiệu Ứng Ruby', 'RUBY_BASIC', 'Tên màu đỏ ruby rực rỡ', 'name-effect-ruby-basic', 'Ruby', 71, true, 11),
('Hiệu Ứng Ruby Lửa', 'RUBY_FIRE', 'Tên có hiệu ứng lửa đỏ bùng cháy', 'name-effect-ruby-fire', 'Ruby', 75, true, 12),

-- Amethyst Tier (Level 81-90) - Purple Advanced Effects
('Hiệu Ứng Amethyst', 'AMETHYST_BASIC', 'Tên màu tím amethyst huyền bí', 'name-effect-amethyst-basic', 'Amethyst', 81, true, 13),
('Hiệu Ứng Amethyst Ma Thuật', 'AMETHYST_MAGIC', 'Tên có hiệu ứng ma thuật tím', 'name-effect-amethyst-magic', 'Amethyst', 85, true, 14),

-- Master Tier (Level 91+) - Ultimate Effects
('Hiệu Ứng Master Vàng', 'MASTER_GOLD', 'Tên có hào quang vàng lấp lánh', 'name-effect-master-gold', 'Master', 91, true, 15),
('Hiệu Ứng Master Cầu Vồng', 'MASTER_RAINBOW', 'Tên có hiệu ứng cầu vồng đa sắc', 'name-effect-master-rainbow', 'Master', 95, true, 16),
('Hiệu Ứng Master Thiên Thần', 'MASTER_DIVINE', 'Tên có hào quang thiêng liêng', 'name-effect-master-divine', 'Master', 100, true, 17),
('Hiệu Ứng Master Huyền Thoại', 'MASTER_MYTHICAL', 'Tên có hiệu ứng huyền thoại tối thượng', 'name-effect-master-mythical', 'Master', 105, true, 18),
('Hiệu Ứng Master Vô Cực', 'MASTER_INFINITE', 'Tên có hiệu ứng vô cực siêu việt', 'name-effect-master-infinite', 'Master', 110, true, 19);

-- =====================================================
-- CSS CLASS MAPPING FOR FRONTEND REFERENCE
-- =====================================================

-- Frontend team sẽ tạo các CSS classes tương ứng:

/*
PROGRESSION: Từ đơn giản (đổi màu) đến phức tạp (animation)

SILVER TIER CSS CLASSES (Level 21-30) - Simple Color:
.name-effect-silver-basic { color: #a0aec0; font-weight: 500; }
.name-effect-silver-bright { color: #e2e8f0; font-weight: 600; }

GOLD TIER CSS CLASSES (Level 31-40) - Enhanced Color:
.name-effect-gold-basic { color: #d69e2e; font-weight: 600; }
.name-effect-gold-bold { color: #b7791f; font-weight: 700; text-shadow: 1px 1px 2px rgba(183, 121, 31, 0.3); }

PLATINUM TIER CSS CLASSES (Level 41-50) - Subtle Glow:
.name-effect-platinum-basic { color: #cbd5e0; font-weight: 600; }
.name-effect-platinum-glow { color: #cbd5e0; text-shadow: 0 0 6px rgba(203, 213, 224, 0.5); }

ONYX TIER CSS CLASSES (Level 51-60) - Dark Glow:
.name-effect-onyx-basic { color: #2d3748; font-weight: 600; text-shadow: 1px 1px 2px rgba(0,0,0,0.5); }
.name-effect-onyx-glow { color: #2d3748; text-shadow: 0 0 8px rgba(45, 55, 72, 0.6); }

SAPPHIRE TIER CSS CLASSES (Level 61-70) - Blue Animations:
.name-effect-sapphire-basic { color: #3182ce; font-weight: 600; text-shadow: 0 0 4px rgba(49, 130, 206, 0.4); }
.name-effect-sapphire-wave { color: #3182ce; animation: sapphire-wave 2s ease-in-out infinite; }

RUBY TIER CSS CLASSES (Level 71-80) - Red Animations:
.name-effect-ruby-basic { color: #e53e3e; font-weight: 600; text-shadow: 0 0 6px rgba(229, 62, 62, 0.5); }
.name-effect-ruby-fire { color: #e53e3e; animation: ruby-fire 2s ease-in-out infinite; text-shadow: 0 0 10px rgba(229, 62, 62, 0.8); }

AMETHYST TIER CSS CLASSES (Level 81-90) - Purple Advanced:
.name-effect-amethyst-basic { color: #805ad5; font-weight: 600; text-shadow: 0 0 8px rgba(128, 90, 213, 0.6); }
.name-effect-amethyst-magic { color: #805ad5; animation: amethyst-magic 2.5s ease-in-out infinite; text-shadow: 0 0 15px rgba(128, 90, 213, 0.9); }

MASTER TIER CSS CLASSES (Level 91+) - Ultimate Effects:
.name-effect-master-gold { color: #d69e2e; animation: master-sparkle 3s ease-in-out infinite; text-shadow: 0 0 20px rgba(214, 158, 46, 1); }
.name-effect-master-rainbow { background: linear-gradient(45deg, #ff0000, #ff7f00, #ffff00, #00ff00, #0000ff, #4b0082, #9400d3); -webkit-background-clip: text; -webkit-text-fill-color: transparent; animation: master-rainbow 4s ease-in-out infinite; }
.name-effect-master-divine { color: #f7fafc; text-shadow: 0 0 25px rgba(247, 250, 252, 1); animation: master-divine 5s ease-in-out infinite; }
.name-effect-master-mythical { background: linear-gradient(45deg, #ffd700, #ff69b4, #00ffff, #ff1493); -webkit-background-clip: text; -webkit-text-fill-color: transparent; animation: master-mythical 6s ease-in-out infinite; }
.name-effect-master-infinite { background: linear-gradient(360deg, #ff0000, #ff7f00, #ffff00, #00ff00, #0000ff, #4b0082, #9400d3, #ff0000); -webkit-background-clip: text; -webkit-text-fill-color: transparent; animation: master-infinite 8s linear infinite; }
*/

-- =====================================================
-- SUMMARY
-- =====================================================

SELECT CONCAT('Name Effects updated successfully! Total effects: ', COUNT(*)) FROM "NameEffects";

-- Expected: 19 Name Effects
-- Wood (1-10): No effects
-- Bronze (11-20): No effects
-- Silver (21-30): 2 effects
-- Gold (31-40): 2 effects
-- Platinum (41-50): 2 effects
-- Onyx (51-60): 2 effects
-- Sapphire (61-70): 2 effects
-- Ruby (71-80): 2 effects
-- Amethyst (81-90): 2 effects
-- Master (91+): 5 effects

-- Verify tier distribution
SELECT 
    tier_name,
    COUNT(*) as effect_count,
    MIN(unlock_level) as min_level,
    MAX(unlock_level) as max_level
FROM "NameEffects" 
GROUP BY tier_name 
ORDER BY MIN(unlock_level);
