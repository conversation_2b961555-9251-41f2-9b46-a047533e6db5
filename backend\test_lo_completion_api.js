/**
 * TEST SCRIPT FOR LO COMPLETION ANALYSIS API
 * Script để test API phân tích LO theo % hoàn thành
 */

const axios = require('axios');

// C<PERSON><PERSON> hình test
const BASE_URL = 'http://localhost:3000/api';
const TEST_CONFIG = {
    subject_id: 1, // Thay đổi theo dữ liệu thực tế
    user_id: 1,    // Thay đổi theo dữ liệu thực tế
    auth_token: 'your_jwt_token_here' // Thay đổi theo token thực tế
};

/**
 * Test API chính: LO Completion Analysis
 */
async function testLOCompletionAnalysis() {
    try {
        console.log('🧪 Testing LO Completion Analysis API...');
        
        const response = await axios.get(
            `${BASE_URL}/learning-outcomes/completion-analysis/${TEST_CONFIG.subject_id}/${TEST_CONFIG.user_id}`,
            {
                headers: {
                    'Authorization': `Bearer ${TEST_CONFIG.auth_token}`,
                    'Content-Type': 'application/json'
                }
            }
        );

        console.log('✅ API Response Status:', response.status);
        console.log('📊 Response Data Structure:');
        console.log('- Subject Info:', !!response.data.data.subject_info);
        console.log('- Student Info:', !!response.data.data.student_info);
        console.log('- LO Analysis:', !!response.data.data.lo_analysis);
        console.log('- Learning Recommendations:', !!response.data.data.learning_recommendations);
        
        if (response.data.data.lo_analysis) {
            const analysis = response.data.data.lo_analysis;
            console.log(`- LOs needing improvement: ${analysis.needs_improvement?.length || 0}`);
            console.log(`- LOs ready for advancement: ${analysis.ready_for_advancement?.length || 0}`);
        }

        return response.data;
    } catch (error) {
        console.error('❌ Error testing LO Completion Analysis:', error.response?.data || error.message);
        return null;
    }
}

/**
 * Test API hỗ trợ: Get LOs by Subject
 */
async function testGetLOsBySubject() {
    try {
        console.log('\n🧪 Testing Get LOs by Subject API...');
        
        const response = await axios.get(
            `${BASE_URL}/learning-outcomes/subject/${TEST_CONFIG.subject_id}`,
            {
                headers: {
                    'Authorization': `Bearer ${TEST_CONFIG.auth_token}`,
                    'Content-Type': 'application/json'
                }
            }
        );

        console.log('✅ API Response Status:', response.status);
        console.log('📊 Learning Outcomes found:', response.data.data.learning_outcomes?.length || 0);
        
        return response.data;
    } catch (error) {
        console.error('❌ Error testing Get LOs by Subject:', error.response?.data || error.message);
        return null;
    }
}

/**
 * Test API cập nhật: Quiz Result Detailed Analysis (với LO completion)
 */
async function testUpdatedQuizAnalysis() {
    try {
        console.log('\n🧪 Testing Updated Quiz Result Detailed Analysis...');
        
        // Giả sử có quiz_id = 1
        const quiz_id = 1;
        
        const response = await axios.get(
            `${BASE_URL}/quiz-results/detailed-analysis/${quiz_id}/${TEST_CONFIG.user_id}`,
            {
                headers: {
                    'Authorization': `Bearer ${TEST_CONFIG.auth_token}`,
                    'Content-Type': 'application/json'
                }
            }
        );

        console.log('✅ API Response Status:', response.status);
        console.log('📊 Response includes:');
        console.log('- Overall Performance:', !!response.data.data.overall_performance);
        console.log('- Chapter Analysis:', !!response.data.data.chapter_analysis);
        console.log('- LO Completion Analysis:', !!response.data.data.lo_completion_analysis);
        console.log('- Personalized Recommendations:', !!response.data.data.personalized_recommendations);
        
        return response.data;
    } catch (error) {
        console.error('❌ Error testing Updated Quiz Analysis:', error.response?.data || error.message);
        return null;
    }
}

/**
 * Test API cập nhật: Subject Comprehensive Analysis (với LO completion)
 */
async function testUpdatedSubjectAnalysis() {
    try {
        console.log('\n🧪 Testing Updated Subject Comprehensive Analysis...');
        
        const response = await axios.get(
            `${BASE_URL}/reports/subject/${TEST_CONFIG.subject_id}/comprehensive-analysis/${TEST_CONFIG.user_id}`,
            {
                headers: {
                    'Authorization': `Bearer ${TEST_CONFIG.auth_token}`,
                    'Content-Type': 'application/json'
                }
            }
        );

        console.log('✅ API Response Status:', response.status);
        console.log('📊 Response includes:');
        console.log('- Subject Performance:', !!response.data.data.subject_performance);
        console.log('- Chapter Analysis:', !!response.data.data.chapter_analysis);
        console.log('- LO Completion Analysis:', !!response.data.data.lo_completion_analysis);
        console.log('- Personalized Recommendations:', !!response.data.data.personalized_recommendations);
        
        return response.data;
    } catch (error) {
        console.error('❌ Error testing Updated Subject Analysis:', error.response?.data || error.message);
        return null;
    }
}

/**
 * Chạy tất cả tests
 */
async function runAllTests() {
    console.log('🚀 Starting LO Completion Analysis API Tests...\n');
    
    // Test 1: API chính
    await testLOCompletionAnalysis();
    
    // Test 2: API hỗ trợ
    await testGetLOsBySubject();
    
    // Test 3: API cập nhật - Quiz Analysis
    await testUpdatedQuizAnalysis();
    
    // Test 4: API cập nhật - Subject Analysis
    await testUpdatedSubjectAnalysis();
    
    console.log('\n✨ All tests completed!');
    console.log('\n📝 Notes:');
    console.log('- Make sure to update TEST_CONFIG with real values');
    console.log('- Ensure the server is running on localhost:3000');
    console.log('- Check that you have valid JWT token');
    console.log('- Verify that test data exists in database');
}

// Chạy tests nếu file được execute trực tiếp
if (require.main === module) {
    runAllTests().catch(console.error);
}

module.exports = {
    testLOCompletionAnalysis,
    testGetLOsBySubject,
    testUpdatedQuizAnalysis,
    testUpdatedSubjectAnalysis,
    runAllTests
};
