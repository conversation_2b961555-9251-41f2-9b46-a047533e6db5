# Technical Debt & Known Issues

## Critical Issues

**Backend Technical Debt**:

1. **Massive Quiz Controller**: 3400+ lines cần refactor
2. **File Upload Management**: No cleanup mechanism
3. **Database Migrations**: Naming inconsistencies
4. **Error Handling**: Inconsistent response formats
5. **Socket.IO Rooms**: Complex logic scattered

**Frontend Technical Debt** (RESOLVED):

- ✅ Component organization resolved
- ✅ Import path consistency achieved
- ✅ Code duplication eliminated
- ✅ Utilities centralized
- ✅ Constants consolidated

## Performance Issues

**Backend**:

- Single massive quiz controller
- No file size limits
- N+1 query patterns
- No rate limiting

**Frontend** (IMPROVED):

- ✅ Better tree-shaking
- ✅ Optimized imports
- ✅ Code splitting support
