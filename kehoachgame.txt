ĐỀ XUẤT CƠ CHẾ "GAMIFICATION" CHO SYNLEARNIA
Ngày: 26/07/2025
Biên soạn: <PERSON><PERSON><PERSON><PERSON> <PERSON>h và Gemini 2.5 Pro
Phiên bản: 2.0
M<PERSON>c tiêu:
Tăng cường sự tương tác và gắn bó của sinh viên (người chơi) với nền tảng.
Tạo ra cảm giác tiến bộ và thành tựu rõ ràng thông qua học tập.
Cung cấp phần thưởng hấp dẫn, khuyến khích việc tham gia làm bài trắc nghiệm thường xuyên.
Xây dựng một hệ thống tích hợp, nơi các cơ chế game hỗ trợ và bổ sung cho nhau.
Cơ chế 1: <PERSON><PERSON> thống Cấp độ & Danh hiệu
Đ<PERSON><PERSON> là hệ thống cốt lõi, ghi nhận sự tiến bộ của người chơi qua thời gian.
1.	<PERSON><PERSON><PERSON><PERSON> kinh nghiệm (XP):
<PERSON><PERSON>ờ<PERSON> chơi nhận được XP khi hoàn thành các hoạt động học tập, chủ yếu là trả lời đúng các câu hỏi trắc nghiệm.
Các nguồn nhận XP khác bao gồm: hoàn thành một bài quiz, đạt chuỗi trả lời đúng (streak), nhận thưởng từ Trứng.
2.	Cấp độ (Levels):
Khi tích lũy đủ XP, người chơi sẽ lên cấp. Mỗi cấp độ là một cột mốc ghi nhận nỗ lực.
Cấp độ là điều kiện tiên quyết để mở khóa các tầng Danh hiệu và một số Avatar nhất định.
3.	Huy hiệu & Danh hiệu (Badges & Titles):
Để tạo ra các mục tiêu lớn và rõ ràng, hệ thống sẽ có các tầng cấp độ. Mỗi tầng cấp độ sẽ mở khóa một Huy hiệu (hiển thị trên hồ sơ) và Danh hiệu (hiển thị cạnh tên người chơi).
Hệ thống sẽ có các tầng danh hiệu như sau:
Dải Cấp độ	Tên Tầng (Tier)	Huy hiệu được mở khóa	Danh hiệu hiển thị
1-12	Wood (Gỗ)	Huy hiệu Gỗ	Tân Binh Gỗ
13-24	Bronze (Đồng)	Huy hiệu Đồng	Chiến Binh Đồng
25-36	Silver (Bạc)	Huy hiệu Bạc	Tinh Anh Bạc
37-48	Gold (Vàng)	Huy hiệu Vàng	Cao Thủ Vàng
49-60	Platinum (Bạch Kim)	Huy hiệu Bạch Kim	Bậc Thầy Bạch Kim
61-72	Onyx (Đá Mã Não)	Huy hiệu Onyx	Thống Lĩnh Onyx
73-84	Sapphire (Lam Ngọc)	Huy hiệu Sapphire	Hiền Triết Sapphire
85-96	Ruby (Hồng Ngọc)	Huy hiệu Ruby	Chúa Tể Ruby
97-108	Amethyst (Thạch Anh Tím)	Huy hiệu Amethyst	Đại Sư Amethyst
109-120+	Master (Bậc Thầy)	Huy hiệu Bậc Thầy	Bậc Thầy Vô Song

Cơ chế 2: Tùy chỉnh & Thể hiện
Cơ chế này cho phép người chơi thể hiện thành tích và cá tính của mình thông qua các vật phẩm trang trí.
Avatar:
Khi tạo tài khoản, người chơi được chọn một trong vài avatar cơ bản.
Hệ thống có tổng cộng 30 avatar động vật. Các avatar khác sẽ được mở khóa theo 2 cách:
Mở khóa theo cấp độ: Đạt đến một cấp độ cụ thể sẽ mở khóa avatar tương ứng.
Mở khóa từ Trứng Thưởng: Một số avatar hiếm và độc quyền chỉ có thể nhận được từ việc ấp trứng.
Khung Avatar (Avatar Frames):
Đây là các khung viền trang trí cho avatar, là biểu tượng cho cấp bậc và thành tích của người chơi.
Cách nhận:
Khung viền cấp bậc: Khi người chơi đạt đến một tầng danh hiệu mới (Bronze, Silver, Gold...), họ sẽ mở khóa vĩnh viễn khung viền tương ứng với tầng đó. Ví dụ: Đạt cấp 25 (Silver) sẽ nhận được "Khung Bạc".
Khung viền đặc biệt: Các khung viền có thiết kế độc đáo, hiếm có thể nhận được từ Trứng Thưởng (độ hiếm Rare, Epic, Legendary).
Hiệu ứng Tên (Name Effects):
Đây là hiệu ứng hình ảnh đặc biệt (như hào quang, viền màu, lấp lánh) áp dụng cho tên của người chơi trên bảng xếp hạng và sảnh chờ.
Hiệu ứng tên là phần thưởng danh giá, chỉ dành cho những người chơi ở các tầng cấp độ cao nhất để thể hiện đẳng cấp.
Cách nhận: Tự động áp dụng khi người chơi đạt các tầng danh hiệu cao, ví dụ:
Onyx: Viền tên màu xám đậm.
Sapphire: Tên có hiệu ứng sóng nước xanh lam.
Ruby: Tên có viền đỏ rực.
Master: Tên có hào quang vàng lấp lánh.
Cơ chế 3: Trứng Thưởng & Sưu Tầm
Cơ chế này tạo ra yếu tố bất ngờ và khuyến khích người chơi tham gia thường xuyên để "săn" các vật phẩm giá trị.
Mục tiêu: Tạo ra một vòng lặp tham gia - nhận thưởng - sưu tầm hấp dẫn, giữ chân người chơi lâu dài.
Cách nhận Trứng:
Hoàn thành bài quiz: Tỉ lệ nhỏ nhận được "Trứng Cơ Bản".
Chuỗi thắng (Streak): Trả lời đúng 5, 10, 15 câu liên tiếp có cơ hội nhận trứng ngẫu nhiên.
Đạt điểm tuyệt đối (100%): Chắc chắn nhận được một quả trứng cấp cao hơn.
Lên cấp: Đạt các mốc cấp độ quan trọng (5, 10, 15...) sẽ được tặng một quả trứng theo chủ đề.
Phần thưởng bên trong Trứng: Khi ấp trứng, người chơi có thể nhận được một trong các vật phẩm sau:
Avatar Mới: Bao gồm cả các avatar độc quyền không thể mở khóa bằng cấp độ.
Khung Avatar Đặc Biệt: Các khung viền hiếm có, không mở khóa bằng cấp độ.
Điểm kinh nghiệm (XP): Một lượng XP ngẫu nhiên giúp lên cấp nhanh hơn.
Emoji độc quyền: Các biểu tượng cảm xúc vui nhộn không có sẵn.
Cơ chế 4: Tương tác Xã hội
Cơ chế này thúc đẩy sự tương tác nhẹ nhàng, vui vẻ giữa những người chơi mà không cần hệ thống chat phức tạp.
Biểu tượng cảm xúc (Emoji):
Người chơi có thể sử dụng các biểu tượng Emoji vui nhộn để tương tác với nhau trong sảnh chờ hoặc trong quá trình làm bài quiz (nếu được cho phép).
Cách nhận:
Một bộ Emoji cơ bản sẽ có sẵn cho tất cả mọi người.
Các Emoji hiếm, độc đáo và thú vị hơn sẽ được mở khóa thông qua Trứng Thưởng.
Tích hợp: Việc khoe Avatar, Khung Avatar, Danh hiệu và Hiệu ứng Tên trên bảng xếp hạng và sảnh chờ cũng là một phần quan trọng của tương tác xã hội, tạo động lực cho người chơi khác phấn đấu.














ĐỀ XUẤT HỆ THỐNG TIỀN TỆ VÀ KINH TẾ TRONG GAME (ECONOMY) CHO SYNLEARNIA
Ngày: 26/07/2025
Biên soạn: Gemini 2.5 Pro
Phiên bản: 2.0
1. TỔNG QUAN
Để tạo ra một hệ thống kinh tế trong game (economy) cân bằng, hấp dẫn và có chiều sâu, Synlearnia sẽ triển khai mô hình tiền tệ hai lớp. Mô hình này vừa khuyến khích sự tham gia thường xuyên, vừa mang lại giá trị cho cả sự may mắn (mở trứng) và sự kiên trì (tích lũy).
SynCoin (Tiền tệ chính):
Icon: coin.png
Mục đích: Là đơn vị tiền tệ cơ bản, dễ kiếm, dùng cho các giao dịch hàng ngày và tăng cơ hội nhận thưởng.
Kristal (Tiền tệ cao cấp):
Icon: gem_*.png (có thể thay đổi ngẫu nhiên hoặc dùng một icon chung).
Mục đích: Là đơn vị tiền tệ cao cấp, khó kiếm hơn, dùng để mua các vật phẩm giá trị và là "cơ chế cứu cánh" cho các vật phẩm trùng lặp, đảm bảo mọi nỗ lực của người chơi đều có giá trị.
2. CHI TIẾT CÁC LOẠI TIỀN TỆ
2.1. SynCoin (Tiền tệ chính)
Mô tả: SynCoin là đơn vị tiền tệ cơ bản trong Synlearnia, có thể kiếm được thông qua các hoạt động học tập cốt lõi. Đây là động lực chính cho các hoạt động ngắn hạn.
Cách Nhận (Nguồn Cung):
Hoàn thành bài quiz: Nhận một lượng SynCoin nhỏ dựa trên số câu trả lời đúng.
Đăng nhập hàng ngày: Nhận thưởng một lượng SynCoin nhất định mỗi ngày.
Hoàn thành nhiệm vụ đơn giản: Ví dụ: "Hoàn thành 3 bài quiz", "Đạt 70% điểm trở lên trong 5 bài quiz liên tiếp".
Mở từ Trứng cấp thấp: Một số loại trứng có thể cho ra SynCoin như một phần thưởng phụ.
Công Dụng (Nguồn Cầu):
Mua các loại Trứng cấp thấp trong Cửa hàng (ví dụ: Basic Egg, Cracked Egg) để có thêm cơ hội nhận vật phẩm.
Mua các Emoji phổ thông và các vật phẩm tiêu hao nhỏ.
Sử dụng cho các tính năng hỗ trợ nhỏ (ví dụ: mua thêm một lượt trợ giúp trong bài quiz).
2.2. Kristal (Tiền tệ cao cấp)
Mô tả: Kristal là đơn vị tiền tệ cao cấp, tượng trưng cho sự nỗ lực và may mắn. Đây là "cơ chế cứu cánh", đảm bảo người chơi không bao giờ cảm thấy lãng phí khi mở ra vật phẩm trùng lặp.
Cách Nhận (Nguồn Cung):
Phân giải vật phẩm trùng lặp (Nguồn chính): Khi người chơi mở trứng và nhận được một Avatar hoặc Emoji đã sở hữu, vật phẩm đó sẽ tự động được phân giải thành một lượng Kristal tương ứng với độ hiếm của nó.
Ví dụ: Mở ra Avatar "Mèo" (Phổ biến) đã có -> Nhận 50 Kristal.
Ví dụ: Mở ra Avatar "Rồng" (Sử thi) đã có -> Nhận 500 Kristal.
Phần thưởng thành tích lớn: Đạt các mốc Danh hiệu quan trọng (ví dụ: Lên hạng Vàng, Bạch Kim, Bậc Thầy...).
Phần thưởng trên Bảng xếp hạng: Top người chơi hàng tuần/tháng sẽ nhận được một lượng Kristal.
Mở từ Trứng cấp cao: Tỉ lệ rất nhỏ nhận được Kristal khi mở các loại trứng hiếm.
Công Dụng (Nguồn Cầu):
Mua các Avatar và Emoji hiếm được bán trong "Cửa Hàng Xoay Vòng" (vật phẩm trong cửa hàng sẽ thay đổi mỗi tuần, tạo sự khan hiếm).
Mua các loại Trứng cấp cao (ví dụ: Dragon Egg, Legendary Egg) với một cái giá đáng kể.
Mua các vật phẩm trang trí đặc biệt như Khung Avatar và Hiệu ứng Tên độc quyền.
________________________________________
3. VÒNG LẶP KINH TẾ ĐỀ XUẤT
Mô hình này tạo ra một vòng lặp tích cực, giữ chân người dùng:
HỌC & CHƠI: Người dùng tham gia làm quiz.
NHẬN THƯỞNG: Nhận XP (để lên cấp), SynCoin, và có cơ hội nhận Trứng.
MỞ TRỨNG:
Nếu ra vật phẩm mới -> Có thêm động lực thể hiện (khoe) và sưu tầm.
Nếu ra vật phẩm trùng lặp -> Nhận lại Kristal, không cảm thấy lãng phí.
MUA SẮM:
Dùng SynCoin để mua trứng cơ bản (thêm lượt may mắn).
Dùng Kristal để mua chắc chắn các vật phẩm hiếm mà mình mong muốn trong Cửa hàng Xoay Vòng.
QUAY LẠI BƯỚC 1: Việc có mục tiêu mua sắm rõ ràng tạo động lực để người dùng tiếp tục học và chơi.
4. LỢI ÍCH CỦA HỆ THỐNG
Cân bằng giữa May rủi và Nỗ lực: Người chơi có thể trông chờ vào may mắn từ trứng, nhưng cũng có một con đường chắc chắn để sở hữu vật phẩm thông qua việc tích lũy Kristal.
Tạo giá trị cho mọi phần thưởng: Không có phần thưởng nào là "vô dụng", kể cả vật phẩm trùng lặp, giúp người chơi luôn cảm thấy hài lòng.
Tăng khả năng giữ chân người dùng: Vòng lặp kinh tế và cửa hàng xoay vòng khuyến khích người dùng quay trở lại mỗi ngày/tuần để kiếm tiền tệ và kiểm tra vật phẩm mới.
Tạo chiều sâu cho hệ thống: Người chơi có nhiều thứ để tính toán và đặt mục tiêu (ngắn hạn: kiếm SynCoin mua trứng, dài hạn: tích Kristal mua avatar/khung viền hiếm).






ĐỀ XUẤT HOÀN CHỈNH: CƠ CHẾ GAMIFICATION TRONG "CUỘC ĐUA QUIZ TỐC ĐỘ"
Ý tưởng chủ đạo: Biến mỗi bài quiz thành một "Đấu Trường Tri Thức Synlearnia" – một cuộc đua gay cấn nơi người chơi cạnh tranh không chỉ bằng kiến thức mà còn bằng chiến thuật. Người chơi sẽ trang bị một bộ kỹ năng cá nhân, tích lũy Năng Lượng trong trận đấu để kích hoạt chúng, tạo ra những khoảnh khắc bứt phá và tương tác cạnh tranh, vui vẻ.
1. Hệ thống Điểm Năng Động (Nền tảng Cuộc Đua)
Hệ thống điểm là cốt lõi, thưởng cho cả tốc độ, sự chính xác và nỗ lực sửa sai.
Điểm Cơ Bản: Mỗi câu trả lời đúng nhận 100 điểm.
Điểm Thưởng Tốc Độ (Speed Bonus):
Trả lời đúng trong 5 giây đầu nhận thêm tối đa 50 điểm, giảm dần theo thời gian.
Công thức: Điểm thưởng = 50 * (Thời gian thưởng còn lại / Tổng thời gian thưởng).
Điểm Thưởng Chuỗi Thắng (Streak Bonus):
Khi trả lời đúng 3 câu liên tiếp trở lên, người chơi nhận điểm thưởng cộng dồn cho mỗi câu đúng tiếp theo (ví dụ: +10, +15, +20...).
Hiển thị biểu tượng "ngọn lửa" 🔥 bên cạnh tên người chơi.
Điểm Vượt Lên (Vòng 2 & 3):
Trả lời đúng một câu hỏi đã làm sai hoặc bỏ qua ở các vòng sau sẽ nhận 50% điểm cơ bản (50 điểm).
Không có điểm thưởng tốc độ hoặc chuỗi thắng cho các vòng này để giữ giá trị cho việc trả lời đúng ngay từ đầu.
2. Cơ Chế Năng Lượng & Kỹ Năng Chiến Lược
Đây là trái tim của hệ thống chiến thuật, thay thế cho các vật phẩm dùng một lần.
1.	Vòng Lặp Lối Chơi Mới:
Mua sắm (Ngoài cuộc đua): Dùng SynCoin kiếm được để mua các Kỹ năng (Skills) từ Cửa hàng.
Trang bị (Trước cuộc đua): Chọn 4 kỹ năng từ kho đồ để mang vào trận đấu.
Tích lũy (Trong cuộc đua): Trả lời câu hỏi để tích đầy Thanh Năng Lượng.
Kích hoạt (Trong cuộc đua): Sử dụng Năng Lượng để kích hoạt một kỹ năng ngẫu nhiên từ bộ 4 kỹ năng đã trang bị.
2.	Kho Kỹ Năng (Skill Inventory):
Là nơi người chơi lưu trữ tất cả các kỹ năng họ đã mua hoặc nhận được.
Việc sưu tầm và sở hữu nhiều kỹ năng đa dạng sẽ mang lại lợi thế chiến lược lâu dài.
3.	Trang Bị (Skill Loadout):
Trước khi vào phòng chờ, người chơi sẽ thấy giao diện "Trang bị Kỹ năng".
Họ phải chọn 4 kỹ năng từ kho đồ của mình. Lựa chọn này quyết định chiến thuật của họ trong cả cuộc đua (tấn công, phòng thủ, hay tập trung bứt phá điểm số).
4.	Thanh Năng Lượng & Kích Hoạt:
Trong giao diện cuộc đua, người chơi có một Thanh Năng Lượng và một ô Kỹ năng duy nhất.
Cách tích Năng Lượng:
Trả lời đúng: +20%
Nhận thưởng Tốc Độ: +10%
Mỗi câu trong Chuỗi Thắng: +5%
Khi thanh Năng Lượng đạt 100%, hệ thống sẽ ngẫu nhiên chọn 1 trong 4 kỹ năng đã trang bị và đưa vào ô Kỹ năng. Nút kỹ năng sẽ sáng lên, sẵn sàng để người chơi kích hoạt.
Sau khi sử dụng, Thanh Năng Lượng sẽ cạn và bắt đầu tích lũy lại.
3. Danh Sách Kỹ Năng Mẫu
A. Dạng Tấn Công
Hố Đen Điểm Số (Point Blackhole) 💣
Hiệu ứng: Nhắm vào người chơi hạng 1. Câu trả lời đúng tiếp theo của họ sẽ nhận 0 điểm.
Kẻ Cắp Nhanh Trí (Thief's Gambit) 💰
Hiệu ứng: Nhắm vào người chơi ngay trên bạn. Nếu họ trả lời đúng, bạn cướp 50% số điểm họ nhận được.
Kẻ Phá Chuỗi (Streak Breaker) 💥
Hiệu ứng: Nhắm vào người chơi đang có chuỗi thắng dài nhất. Phá vỡ ngay lập tức chuỗi thắng của họ.
B. Dạng Phòng Thủ
Khiên Năng Lượng (Energy Shield) 🛡️
Hiệu ứng: Chặn một kỹ năng Tấn công tiếp theo nhắm vào bạn. Có tác dụng trong 15 giây.
Kiên Cố (Steadfast) 🔗
Hiệu ứng: Kích hoạt trước khi trả lời. Nếu trả lời sai, chuỗi thắng sẽ không bị mất.
C. Dạng Bứt Phá
Bùng Nổ Điểm Số (Point Burst) 💎
Hiệu ứng: Nhân đôi (x2) toàn bộ điểm số nhận được từ câu trả lời đúng tiếp theo.
Tầm Nhìn Chiến Lược (Strategic Vision) 🎯
Hiệu ứng: Cho bạn biết độ khó và chủ đề (LO) của câu hỏi tiếp theo.

4. Sự Kiện Bất Ngờ (Sự Kiện Toàn Cục)
Để cuộc đua thêm kịch tính, hệ thống sẽ ngẫu nhiên kích hoạt các sự kiện ảnh hưởng đến tất cả người chơi.
Câu Hỏi Vàng (Golden Question): Một câu hỏi ngẫu nhiên được đánh dấu Vàng, trả lời đúng sẽ nhận gấp đôi điểm cơ bản.
Vùng Tăng Tốc (Speed Zone): Trong 3 câu hỏi tiếp theo, điểm thưởng tốc độ sẽ được nhân đôi cho tất cả mọi người.
Cơ Hội Vàng (Redemption Chance): Ở đầu Vòng 2, người chơi đầu tiên trả lời đúng câu hỏi sai của mình sẽ nhận 100% điểm cơ bản.
5. Giao Diện & Trải Nghiệm Người Dùng (UI/UX)
Trước Cuộc Đua: Giao diện "Cửa hàng Kỹ năng" và "Trang bị Kỹ năng" (chọn 4 kỹ năng).
Sảnh chờ: Hiển thị người chơi trên "vạch xuất phát". Có thể hiển thị 4 icon kỹ năng mà mỗi người chơi đã trang bị để tăng tính phán đoán.
Trong Cuộc Đua:
Thanh Năng Lượng và một ô Kỹ năng duy nhất được hiển thị rõ ràng.
Đường Đua Tiến Trình ở đầu màn hình và Bảng Xếp Hạng ở bên cạnh, cập nhật liên tục.
Hiệu ứng âm thanh và hình ảnh rõ ràng khi trả lời đúng/sai và khi kỹ năng được kích hoạt.
Màn hình kết quả:
Hiển thị "Bục Vinh Quang" cho top 3.
Hiển thị thứ hạng, điểm số, và các thành tích nổi bật ("Chuỗi thắng dài nhất", "Vua tốc độ").
Thêm mục "Khoảnh khắc nổi bật": "Sử dụng 'Kẻ Cắp Nhanh Trí' để vươn lên hạng 2!".
Thông báo phần thưởng nhận được (XP, SynCoin, Trứng).


Tóm tắt Lợi ích của Cơ chế này:
1.	Tăng Cạnh tranh & Hứng khởi: Biến việc làm quiz thụ động thành một cuộc đua chủ động và gay cấn.
2.	Tạo Yếu tố Bất ngờ: Các sự kiện ngẫu nhiên làm mỗi cuộc đua trở nên độc nhất.
3.	Thêm Chiều sâu Chiến lược: Hệ thống skill yêu cầu người chơi phải đưa ra quyết định thông minh.
4.	Tưởng thưởng cho Cả Tốc độ và Nỗ lực: Vừa tôn vinh những người trả lời nhanh và đúng ở Vòng 1, vừa cho người chơi khác cơ hội "lội ngược dòng" ở các vòng sau.
5.	Tích hợp Kinh tế Game: Tạo ra lý do để người chơi kiếm và tiêu SynCoin, kết nối với các cơ chế khác.
Phân Tích Độ Khó Lập Trình
⭐ Cấp độ 1: Dễ (Các phần nền tảng)
Đây là những phần việc cơ bản, chủ yếu liên quan đến logic CRUD (Tạo, Đọc, Cập nhật, Xóa) và hiển thị dữ liệu đơn giản.
Hệ thống điểm cơ bản: Cộng điểm khi trả lời đúng. Đây là logic if-else đơn giản.
Hệ thống Avatar, Khung, Danh hiệu:
Backend: Thêm các cột như avatar_id, frame_id, level, xp vào bảng Users.
Frontend: Đọc dữ liệu này và hiển thị hình ảnh/text tương ứng.
Cửa hàng & Kho Kỹ Năng (phần cơ bản):
Backend: Tạo bảng Skills (chứa thông tin kỹ năng) và UserSkills (lưu kỹ năng người dùng sở hữu). Tạo API để mua (trừ SynCoin, thêm vào UserSkills).
Frontend: Giao diện hiển thị danh sách kỹ năng và kho đồ cá nhân.
⭐⭐ Cấp độ 2: Trung bình (Các hệ thống game cốt lõi)
Đây là những phần cần logic phức tạp hơn, quản lý trạng thái và tương tác với nhiều thành phần khác nhau.
Hệ thống Cấp độ & XP:
Cần một công thức tính XP cần cho mỗi cấp.
Sau mỗi hành động (trả lời đúng, hoàn thành quiz), phải cập nhật XP của người dùng và kiểm tra xem họ có lên cấp không. Cần xử lý cẩn thận bằng database transactions để tránh lỗi dữ liệu.
Điểm Thưởng Tốc Độ & Chuỗi Thắng (Streak):
Backend: Phải tính toán điểm dựa trên chênh lệch thời gian (timestamp) giữa lúc câu hỏi được gửi và lúc nhận câu trả lời.
Frontend & Backend: Cần lưu và cập nhật trạng thái "chuỗi thắng" của người chơi trong suốt cuộc đua.
Hệ thống Trứng Thưởng & Phân giải:
Logic xác suất để quyết định có nhận được trứng không và nhận được vật phẩm gì.
Logic "phân giải" vật phẩm trùng lặp thành Kristal đòi hỏi kiểm tra kho đồ của người dùng trước khi trao thưởng.
Hệ thống Năng Lượng:
Backend: Sau mỗi câu trả lời, phải tính toán lượng Năng Lượng cộng thêm dựa trên nhiều yếu tố (đúng/sai, tốc độ, streak).
Frontend: Cần một thanh UI cập nhật mượt mà.
⭐⭐⭐ Cấp độ 3: Khó (Các tính năng Real-time & Tương tác)
Đây là những phần phức tạp nhất, đòi hỏi kiến thức vững về real-time (WebSockets), quản lý trạng thái đồng bộ và xử lý nhiều sự kiện đồng thời.
Bảng Xếp Hạng Real-time & "Đường Đua":
Cần một hệ thống đẩy (push) dữ liệu hiệu quả. Sau mỗi câu trả lời của bất kỳ ai, server phải tính toán lại thứ hạng và gửi cập nhật đến tất cả người chơi trong phòng.
Sử dụng database thông thường cho việc này sẽ rất chậm. Cần một giải pháp cache tốc độ cao như Redis để quản lý bảng xếp hạng.
Frontend cần xử lý animation mượt mà để cập nhật vị trí mà không gây giật lag.
Kích hoạt Kỹ năng Ngẫu nhiên & Nhắm mục tiêu:
Đây là một chuỗi sự kiện phức tạp: Client thông báo tích đủ Năng Lượng -> Server xác nhận -> Server chọn ngẫu nhiên 1 trong 4 kỹ năng đã trang bị -> Server gửi lại kỹ năng đó cho client -> Client hiển thị nút -> Client bấm dùng (có thể kèm mục tiêu) -> Server nhận yêu cầu, xác thực và thực thi.
Xử lý các Kỹ năng Tấn Công:
Đây là phần khó nhất. Server phải:
1.	Nhận yêu cầu sử dụng kỹ năng từ người chơi A.
2.	Xác thực (A có kỹ năng đó không? Đủ Năng lượng chưa?).
3.	Xác định mục tiêu (người chơi B).
4.	Áp dụng hiệu ứng bất lợi cho người chơi B (ví dụ: đánh dấu B sẽ nhận 0 điểm ở câu tiếp theo).
5.	Gửi một sự kiện real-time riêng cho client của B để thông báo họ đang bị ảnh hưởng.
6.	Gửi một sự kiện chung cho cả phòng để mọi người biết A đã dùng kỹ năng lên B.
🔥🔥🔥🔥 Cấp độ 4: Rất Khó / Rủi ro cao (Những thách thức ẩn)
Đây không phải là tính năng, mà là những vấn đề cốt lõi cần giải quyết để hệ thống hoạt động trơn tru.
Cân Bằng Game (Game Balancing):
Đây là thách thức về thiết kế, không phải lập trình. Kỹ năng nào quá mạnh? Kỹ năng nào quá yếu? Tích Năng Lượng có quá nhanh/chậm không?
Đòi hỏi rất nhiều lần chơi thử và điều chỉnh. Hệ thống cần được thiết kế để các chỉ số (điểm, năng lượng, hiệu ứng) có thể được thay đổi dễ dàng mà không cần sửa code.
Đồng bộ hóa & Độ trễ (Synchronization & Latency):
Làm sao để đảm bảo công bằng khi người chơi có kết nối mạng khác nhau?
Làm sao để xử lý trường hợp hai người chơi dùng kỹ năng lên nhau gần như cùng một lúc (race condition)?
Chống gian lận (Anti-Cheat):
Toàn bộ logic tính điểm, xác thực câu trả lời, kích hoạt kỹ năng bắt buộc phải được xử lý ở server.
Client chỉ gửi hành động của người dùng ("Tôi chọn đáp án B", "Tôi muốn dùng kỹ năng X"), server sẽ quyết định kết quả.

