# Epic 1: Frontend Code Restructuring

**Epic Goal**: T<PERSON><PERSON> cấu trúc toàn bộ frontend codebase để có tổ chức thư mục logic, loại bỏ code duplication, cải thiện maintainability và developer experience mà không thay đổi functionality hoặc UI hiện có.

**Integration Requirements**: Preserve tất cả existing API integrations, Socket.IO connections, Next.js App Router structure, và authentication flows.

## Story 1.1: Thi<PERSON><PERSON> lập C<PERSON>u trúc Thư mục Mới và Migration Plan

Là một **developer**,
Tôi muốn **có cấu trúc thư mục rõ ràng và migration plan chi tiết**,
Đ<PERSON> **có thể thực hiện refactoring một cách an toàn và có hệ thống**.

### Acceptance Criteria

1. Tạo new folder structure trong `src/` theo feature-based organization
2. Document migration plan với step-by-step instructions
3. Setup TypeScript path mapping cho new structure trong tsconfig.json
4. Create barrel export files (index.ts) cho major directories
5. Backup current codebase và create migration branch

### Integration Verification

- **IV1**: Existing functionality verification - Tất cả current imports vẫn hoạt động bình thường
- **IV2**: Integration point verification - Build process không bị break
- **IV3**: Performance impact verification - Build time không tăng đáng kể

## Story 1.2: Migration UI Components sang Components/UI Structure

Là một **developer**,
Tôi muốn **tất cả UI components được tổ chức trong components/ui với categorization rõ ràng**,
Để **dễ dàng tìm và reuse components**.

### Acceptance Criteria

1. Di chuyển tất cả Radix UI wrapper components vào `components/ui/`
2. Categorize components: forms/, navigation/, layout/, feedback/
3. Update tất cả import statements across codebase
4. Create comprehensive barrel exports cho UI components
5. Remove duplicate UI components và consolidate functionality

### Integration Verification

- **IV1**: Existing functionality verification - Tất cả UI components render correctly với same styling
- **IV2**: Integration point verification - Component props và events hoạt động như cũ
- **IV3**: Performance impact verification - Bundle size không tăng quá 5%

## Story 1.3: Tách Business Logic Components sang Features Structure

Là một **developer**,
Tôi muốn **business logic components được tách riêng khỏi pure UI components**,
Để **có separation of concerns rõ ràng và improve maintainability**.

### Acceptance Criteria

1. Identify và categorize business logic components (auth, quiz, dashboard)
2. Di chuyển components vào `components/features/` với proper grouping
3. Refactor components để tách UI logic khỏi business logic
4. Update import paths và ensure proper dependency flow
5. Create feature-specific barrel exports

### Integration Verification

- **IV1**: Existing functionality verification - Business logic hoạt động identical như trước
- **IV2**: Integration point verification - API calls và state management unchanged
- **IV3**: Performance impact verification - Component re-renders không increase

## Story 1.4: Consolidate Utilities và Shared Logic vào Lib Structure

Là một **developer**,
Tôi muốn **tất cả utilities, helpers, và shared logic được centralized trong lib/**,
Để **có single source of truth cho shared functionality**.

### Acceptance Criteria

1. Di chuyển custom hooks từ scattered locations vào `lib/hooks/`
2. Consolidate utility functions vào `lib/utils/`
3. Move auth-related utilities vào `lib/auth/`
4. Centralize constants và types vào `lib/constants/` và `lib/types/`
5. Update tất cả imports để use new lib structure

### Integration Verification

- **IV1**: Existing functionality verification - Tất cả utilities function correctly
- **IV2**: Integration point verification - Hook dependencies và context providers work
- **IV3**: Performance impact verification - No performance regression trong utility usage

## Story 1.4 (Final): Consolidate Utilities và Complete Migration

Là một **developer**,
Tôi muốn **tất cả utilities, helpers, shared logic được centralized và hoàn tất migration**,
Để **có codebase hoàn chỉnh với structure rõ ràng và maintainable**.

### Acceptance Criteria

1. Di chuyển custom hooks từ scattered locations vào `lib/hooks/`
2. Consolidate utility functions vào `lib/utils/`
3. Move auth-related utilities vào `lib/auth/`
4. Centralize constants và types vào `lib/constants/` và `lib/types/`
5. Update tất cả imports để use new lib structure
6. **Code Cleanup**: Remove duplicate components/functions và unused code
7. **NO TESTING POLICY**: Delete HOÀN TOÀN tất cả test files và test-related configurations
8. Finalize tất cả import paths và verify TypeScript compilation
9. Run full build và ensure application hoạt động hoàn hảo

### Integration Verification

- **IV1**: Existing functionality verification - Tất cả utilities function correctly và complete application works identically
- **IV2**: Integration point verification - Hook dependencies, context providers, API calls, Socket.IO, auth flows work perfectly
- **IV3**: Performance impact verification - No performance regression, bundle size optimized

---

---
