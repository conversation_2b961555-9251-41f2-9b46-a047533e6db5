"use client";

import React, { useState, useEffect } from "react";
import { cn } from "@/lib/utils";
import { Badge } from "@/components/ui/feedback";
import { TierIcon } from "@/lib/hooks/use-tier-icon";
import { UserBadgeData, UserTitleData, BadgeRarity } from "@/lib/types/gamification";
import { getVietnameseTierName } from "@/lib/utils/tier-assets";
import { Star, Sparkles, Crown, Gem, Trophy, Award } from "lucide-react";

interface UnlockAnimationProps {
  isVisible: boolean;
  onComplete: () => void;
  item: UserBadgeData | UserTitleData;
  type: "badge" | "title";
  className?: string;
}

export const UnlockAnimation: React.FC<UnlockAnimationProps> = ({
  isVisible,
  onComplete,
  item,
  type,
  className,
}) => {
  const [animationPhase, setAnimationPhase] = useState<"enter" | "celebrate" | "exit">("enter");

  useEffect(() => {
    if (!isVisible) return;

    const timer1 = setTimeout(() => {
      setAnimationPhase("celebrate");
    }, 500);

    const timer2 = setTimeout(() => {
      setAnimationPhase("exit");
    }, 2500);

    const timer3 = setTimeout(() => {
      onComplete();
    }, 3000);

    return () => {
      clearTimeout(timer1);
      clearTimeout(timer2);
      clearTimeout(timer3);
    };
  }, [isVisible, onComplete]);

  if (!isVisible) return null;

  // Get rarity configuration for badges
  const getRarityConfig = (rarity: BadgeRarity) => {
    const rarityConfigs = {
      common: {
        color: "text-gray-600",
        icon: Star,
        label: "Thường",
        gradient: "from-gray-400 to-gray-600",
        particles: "bg-gray-400",
      },
      rare: {
        color: "text-blue-600",
        icon: Sparkles,
        label: "Hiếm",
        gradient: "from-blue-400 to-blue-600",
        particles: "bg-blue-400",
      },
      epic: {
        color: "text-purple-600",
        icon: Crown,
        label: "Sử Thi",
        gradient: "from-purple-400 to-purple-600",
        particles: "bg-purple-400",
      },
      legendary: {
        color: "text-yellow-600",
        icon: Gem,
        label: "Huyền Thoại",
        gradient: "from-yellow-400 to-orange-500",
        particles: "bg-yellow-400",
      },
    };
    return rarityConfigs[rarity] || rarityConfigs.common;
  };

  const isBadge = type === "badge";
  const badgeData = isBadge ? (item as UserBadgeData) : null;
  const titleData = !isBadge ? (item as UserTitleData) : null;

  const rarityConfig = isBadge && badgeData 
    ? getRarityConfig(badgeData.Badge.rarity)
    : { color: "text-blue-600", particles: "bg-blue-400", gradient: "from-blue-400 to-blue-600" };

  return (
    <div
      className={cn(
        "fixed inset-0 z-50 flex items-center justify-center bg-black/80 backdrop-blur-sm",
        className
      )}
    >
      {/* Particle Effects */}
      <div className="absolute inset-0 overflow-hidden">
        {[...Array(20)].map((_, i) => (
          <div
            key={i}
            className={cn(
              "absolute w-2 h-2 rounded-full opacity-80",
              rarityConfig.particles,
              animationPhase === "celebrate" && "animate-bounce"
            )}
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 2}s`,
              animationDuration: `${1 + Math.random()}s`,
            }}
          />
        ))}
      </div>

      {/* Main Content */}
      <div
        className={cn(
          "relative bg-white rounded-2xl p-8 max-w-md w-full mx-4 text-center shadow-2xl",
          "transform transition-all duration-500",
          animationPhase === "enter" && "scale-0 rotate-180 opacity-0",
          animationPhase === "celebrate" && "scale-100 rotate-0 opacity-100",
          animationPhase === "exit" && "scale-110 opacity-0"
        )}
      >
        {/* Celebration Background */}
        <div
          className={cn(
            "absolute inset-0 rounded-2xl opacity-10 bg-gradient-to-br",
            rarityConfig.gradient
          )}
        />

        {/* Content */}
        <div className="relative space-y-4">
          {/* Header */}
          <div className="space-y-2">
            <div className="flex justify-center">
              {isBadge ? (
                <Award className="h-8 w-8 text-yellow-500" />
              ) : (
                <Trophy className="h-8 w-8 text-yellow-500" />
              )}
            </div>
            <h2 className="text-2xl font-bold text-gray-900">
              🎉 Chúc mừng! 🎉
            </h2>
            <p className="text-gray-600">
              Bạn đã mở khóa {isBadge ? "huy hiệu" : "danh hiệu"} mới!
            </p>
          </div>

          {/* Item Display */}
          <div className="space-y-4">
            {/* Icon */}
            <div
              className={cn(
                "flex justify-center transform transition-transform duration-1000",
                animationPhase === "celebrate" && "scale-110 rotate-12"
              )}
            >
              {isBadge && badgeData ? (
                <div className="w-20 h-20 bg-gradient-to-br from-yellow-100 to-yellow-200 rounded-full flex items-center justify-center shadow-lg">
                  <TierIcon
                    level={badgeData.Badge.unlock_level}
                    size="lg"
                    tierName={badgeData.Badge.tier_name}
                    levelInTier={1}
                  />
                </div>
              ) : titleData ? (
                <div className="w-20 h-20 bg-gradient-to-br from-blue-100 to-blue-200 rounded-full flex items-center justify-center shadow-lg">
                  <TierIcon
                    level={titleData.Title.unlock_level}
                    size="lg"
                    tierName={titleData.Title.tier_name}
                    levelInTier={1}
                  />
                </div>
              ) : null}
            </div>

            {/* Item Info */}
            <div className="space-y-2">
              <h3 className="text-xl font-bold text-gray-900">
                {isBadge && badgeData 
                  ? badgeData.Badge.badge_name 
                  : titleData?.Title.title_display}
              </h3>
              
              <div className="flex justify-center gap-2">
                <Badge variant="outline" className="text-xs">
                  {isBadge && badgeData 
                    ? getVietnameseTierName(badgeData.Badge.tier_name)
                    : titleData ? getVietnameseTierName(titleData.Title.tier_name) : ""}
                </Badge>
                
                {isBadge && badgeData && (
                  <Badge 
                    variant="outline" 
                    className={cn("text-xs", rarityConfig.color)}
                  >
                    {getRarityConfig(badgeData.Badge.rarity).label}
                  </Badge>
                )}
              </div>

              {/* Description */}
              <p className="text-sm text-gray-600 max-w-xs mx-auto">
                {isBadge && badgeData 
                  ? badgeData.Badge.description 
                  : titleData ? `Danh hiệu ${titleData.Title.title_name}` : ""}
              </p>
            </div>
          </div>

          {/* Sparkle Effects */}
          <div className="absolute -top-4 -right-4">
            <div
              className={cn(
                "text-yellow-400 transform transition-all duration-1000",
                animationPhase === "celebrate" && "scale-150 rotate-180"
              )}
            >
              ✨
            </div>
          </div>
          <div className="absolute -bottom-4 -left-4">
            <div
              className={cn(
                "text-yellow-400 transform transition-all duration-1000",
                animationPhase === "celebrate" && "scale-150 -rotate-180"
              )}
            >
              ⭐
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Hook for managing unlock animations
export const useUnlockAnimation = () => {
  const [queue, setQueue] = useState<Array<{
    id: string;
    item: UserBadgeData | UserTitleData;
    type: "badge" | "title";
  }>>([]);
  const [currentAnimation, setCurrentAnimation] = useState<{
    item: UserBadgeData | UserTitleData;
    type: "badge" | "title";
  } | null>(null);

  const addToQueue = (item: UserBadgeData | UserTitleData, type: "badge" | "title") => {
    const id = `${type}-${Date.now()}-${Math.random()}`;
    setQueue(prev => [...prev, { id, item, type }]);
  };

  const processQueue = () => {
    if (queue.length > 0 && !currentAnimation) {
      const next = queue[0];
      setCurrentAnimation({ item: next.item, type: next.type });
      setQueue(prev => prev.slice(1));
    }
  };

  const handleAnimationComplete = () => {
    setCurrentAnimation(null);
  };

  // Process queue when it changes
  React.useEffect(() => {
    processQueue();
  }, [queue, currentAnimation]);

  return {
    currentAnimation,
    addToQueue,
    handleAnimationComplete,
    isAnimating: !!currentAnimation,
  };
};

export default UnlockAnimation;
