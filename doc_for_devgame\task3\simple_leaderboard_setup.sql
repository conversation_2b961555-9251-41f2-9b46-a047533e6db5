-- =====================================================
-- SIMPLE LEADERBOARD SETUP FOR TESTING
-- =====================================================

-- 1. Create LeaderboardEntries table
CREATE TABLE IF NOT EXISTS "LeaderboardEntries" (
    "entry_id" SERIAL PRIMARY KEY,
    "user_id" INTEGER NOT NULL,
    "leaderboard_type" VARCHAR(50) NOT NULL DEFAULT 'GLOBAL',
    "ranking_criteria" VARCHAR(50) NOT NULL DEFAULT 'TOTAL_XP',
    "score_value" BIGINT NOT NULL DEFAULT 0,
    "current_rank" INTEGER,
    "previous_rank" INTEGER,
    "rank_change" INTEGER DEFAULT 0,
    "tier_filter" VARCHAR(20),
    "time_period" DATE,
    "last_updated" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "created_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign key
    FOREIGN KEY ("user_id") REFERENCES "Users"("user_id") ON DELETE CASCADE
);

-- 2. Create UserPerformanceStats table
CREATE TABLE IF NOT EXISTS "UserPerformanceStats" (
    "stats_id" SERIAL PRIMARY KEY,
    "user_id" INTEGER NOT NULL,
    "time_period" VARCHAR(20) NOT NULL DEFAULT 'ALL_TIME',
    "period_date" DATE NOT NULL DEFAULT '1970-01-01',
    
    -- Quiz Performance Stats
    "total_quizzes_played" INTEGER DEFAULT 0,
    "total_questions_answered" INTEGER DEFAULT 0,
    "total_correct_answers" INTEGER DEFAULT 0,
    "accuracy_rate" DECIMAL(5,2) DEFAULT 0.00,
    "average_score" DECIMAL(10,2) DEFAULT 0.00,
    "highest_score" INTEGER DEFAULT 0,
    "total_score_earned" BIGINT DEFAULT 0,
    
    -- Ranking Performance
    "first_place_finishes" INTEGER DEFAULT 0,
    "top_3_finishes" INTEGER DEFAULT 0,
    "top_5_finishes" INTEGER DEFAULT 0,
    "average_rank" DECIMAL(5,2) DEFAULT 0.00,
    "best_rank" INTEGER DEFAULT 999,
    
    -- Streak & Speed Stats
    "longest_streak" INTEGER DEFAULT 0,
    "total_streaks_achieved" INTEGER DEFAULT 0,
    "average_answer_time" DECIMAL(8,3) DEFAULT 0.000,
    "fastest_answer_time" DECIMAL(8,3) DEFAULT 0.000,
    "speed_bonus_earned" INTEGER DEFAULT 0,
    
    -- Currency & Rewards
    "syncoin_earned" INTEGER DEFAULT 0,
    "kristal_earned" INTEGER DEFAULT 0,
    "xp_earned" INTEGER DEFAULT 0,
    "eggs_received" INTEGER DEFAULT 0,
    
    -- Social & Interaction
    "emojis_used" INTEGER DEFAULT 0,
    "social_interactions_sent" INTEGER DEFAULT 0,
    "social_interactions_received" INTEGER DEFAULT 0,
    
    "created_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign key
    FOREIGN KEY ("user_id") REFERENCES "Users"("user_id") ON DELETE CASCADE
);

-- 3. Create indexes
CREATE INDEX IF NOT EXISTS "idx_leaderboard_entries_user_type_criteria" 
ON "LeaderboardEntries"("user_id", "leaderboard_type", "ranking_criteria");

CREATE INDEX IF NOT EXISTS "idx_leaderboard_entries_type_criteria_rank" 
ON "LeaderboardEntries"("leaderboard_type", "ranking_criteria", "current_rank");

CREATE INDEX IF NOT EXISTS "idx_user_performance_user_period" 
ON "UserPerformanceStats"("user_id", "time_period", "period_date");

-- 4. Create unique constraints
ALTER TABLE "LeaderboardEntries"
ADD CONSTRAINT "unique_leaderboard_entry"
UNIQUE ("user_id", "leaderboard_type", "ranking_criteria", "tier_filter");

ALTER TABLE "UserPerformanceStats"
ADD CONSTRAINT "unique_user_performance_stats"
UNIQUE ("user_id", "time_period", "period_date");

-- 5. Insert sample data
-- Insert global XP leaderboard
INSERT INTO "LeaderboardEntries" ("user_id", "leaderboard_type", "ranking_criteria", "score_value", "current_rank") 
SELECT 
    u."user_id",
    'GLOBAL' as leaderboard_type,
    'TOTAL_XP' as ranking_criteria,
    COALESCE(u."total_points", 0) as score_value,
    ROW_NUMBER() OVER (ORDER BY COALESCE(u."total_points", 0) DESC) as current_rank
FROM "Users" u
WHERE u."role_id" = 3 -- Only students
ON CONFLICT ("user_id", "leaderboard_type", "ranking_criteria") DO NOTHING;

-- Insert global LEVEL leaderboard
INSERT INTO "LeaderboardEntries" ("user_id", "leaderboard_type", "ranking_criteria", "score_value", "current_rank") 
SELECT 
    u."user_id",
    'GLOBAL' as leaderboard_type,
    'LEVEL' as ranking_criteria,
    COALESCE(u."current_level", 1) as score_value,
    ROW_NUMBER() OVER (ORDER BY COALESCE(u."current_level", 1) DESC, COALESCE(u."total_points", 0) DESC) as current_rank
FROM "Users" u
WHERE u."role_id" = 3 -- Only students
ON CONFLICT ("user_id", "leaderboard_type", "ranking_criteria") DO NOTHING;

-- Initialize basic performance stats for all students
INSERT INTO "UserPerformanceStats" ("user_id", "time_period", "period_date")
SELECT 
    u."user_id",
    'ALL_TIME' as time_period,
    '1970-01-01' as period_date
FROM "Users" u
WHERE u."role_id" = 3
ON CONFLICT ("user_id", "time_period", "period_date") DO NOTHING;

-- 6. Verify setup
SELECT 
    'LeaderboardEntries' as table_name,
    COUNT(*) as record_count
FROM "LeaderboardEntries"
UNION ALL
SELECT 
    'UserPerformanceStats' as table_name,
    COUNT(*) as record_count
FROM "UserPerformanceStats";

-- Show sample leaderboard data
SELECT 
    le."user_id",
    u."name" as username,
    le."leaderboard_type",
    le."ranking_criteria",
    le."current_rank",
    le."score_value"
FROM "LeaderboardEntries" le
JOIN "Users" u ON le."user_id" = u."user_id"
ORDER BY le."leaderboard_type", le."ranking_criteria", le."current_rank"
LIMIT 20;
