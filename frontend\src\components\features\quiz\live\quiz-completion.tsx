"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { motion } from "framer-motion";
import { Trophy, Home, Loader2 } from "lucide-react";
import { Button } from "@/components/ui/forms";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/layout";
import LeaderboardDisplay from "./leaderboard-display";
import { quizService } from "@/lib/services/api";

// Interface cho item trong leaderboard
interface LeaderboardItem {
  user_id: string | number;
  score: number;
  name?: string;
  student_id?: string;
}

interface QuizCompletionProps {
  finalScore: number;
  correctAnswers: number;
  totalQuestions: number;
  quizId: number;
  // Thêm props cho round system
  roundHistory?: Array<{
    round: number;
    questionsAttempted: number[];
    correctAnswers: number;
    incorrectAnswers: number;
  }>;
}

export const QuizCompletion: React.FC<QuizCompletionProps> = ({
  finalScore,
  correctAnswers,
  totalQuestions,
  quizId,
  roundHistory = [],
}) => {
  const router = useRouter();
  const [showLeaderboard, setShowLeaderboard] = useState(false);
  const [leaderboard, setLeaderboard] = useState<LeaderboardItem[]>([]);
  const [loadingLeaderboard, setLoadingLeaderboard] = useState(false);

  // Lấy bảng xếp hạng khi component mount
  useEffect(() => {
    const fetchLeaderboard = async () => {
      try {
        setLoadingLeaderboard(true);
        console.log("Fetching final leaderboard for quiz:", quizId);
        const response = await quizService.getLeaderboard(quizId);
        console.log("Final leaderboard response:", response);

        if (response.leaderboard && response.leaderboard.length > 0) {
          setLeaderboard(response.leaderboard);
        }
      } catch (error) {
        console.error("Error fetching final leaderboard:", error);
      } finally {
        setLoadingLeaderboard(false);
      }
    };

    // Delay một chút để hiển thị kết quả cá nhân trước
    const timer = setTimeout(() => {
      fetchLeaderboard();
    }, 2000);

    return () => clearTimeout(timer);
  }, [quizId]);

  // Xác định màu sắc và thông điệp dựa trên điểm số
  const getScoreColor = (score: number) => {
    if (score >= 80) return "text-green-600";
    if (score >= 60) return "text-yellow-600";
    return "text-red-600";
  };

  const getScoreBgColor = (score: number) => {
    if (score >= 80) return "bg-green-50 border-green-200";
    if (score >= 60) return "bg-yellow-50 border-yellow-200";
    return "bg-red-50 border-red-200";
  };

  const getScoreMessage = (score: number) => {
    if (score >= 80) return "Xuất sắc! Bạn đã làm rất tốt!";
    if (score >= 60) return "Tốt! Bạn đã vượt qua bài quiz!";
    return "Cần cố gắng thêm! Hãy ôn tập và thử lại!";
  };

  // Function để toggle hiển thị bảng xếp hạng
  const handleShowLeaderboard = () => {
    setShowLeaderboard(true);
  };

  const handleBackToResults = () => {
    setShowLeaderboard(false);
  };

  // Nếu đang hiển thị bảng xếp hạng
  if (showLeaderboard) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto p-4">
          {/* Header với nút quay lại */}
          <div className="flex justify-between items-center mb-6 pt-4">
            <Button
              onClick={handleBackToResults}
              variant="outline"
              className="flex items-center gap-2"
            >
              ← Quay lại kết quả
            </Button>
            <Button
              onClick={() => router.push("/dashboard")}
              className="flex items-center gap-2"
            >
              <Home className="h-4 w-4" />
              Về Dashboard
            </Button>
          </div>

          {/* Bảng xếp hạng */}
          <LeaderboardDisplay
            leaderboard={leaderboard}
            totalQuestions={totalQuestions}
            currentQuestionIndex={totalQuestions - 1}
            isLastQuestion={true}
          />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, ease: "easeOut" }}
        className="w-full max-w-lg"
      >
        <Card className="border-0 bg-card">
          <CardHeader className="text-center pb-4">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, type: "spring", damping: 20 }}
              className="flex justify-center mb-4"
            >
              <div className="p-4 rounded-full bg-primary/10">
                <Trophy className="h-12 w-12 text-primary" />
              </div>
            </motion.div>
            <CardTitle className="text-2xl font-bold text-foreground">
              Quiz Hoàn Thành!
            </CardTitle>
          </CardHeader>

          <CardContent className="space-y-6">
            {/* Điểm số chính */}
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ delay: 0.4, duration: 0.5 }}
              className={`text-center p-6 rounded-2xl border ${getScoreBgColor(
                finalScore
              )}`}
            >
              <div
                className={`text-5xl font-bold mb-2 ${getScoreColor(
                  finalScore
                )}`}
              >
                {finalScore}%
              </div>
              <p className="text-base text-muted-foreground font-medium">
                {getScoreMessage(finalScore)}
              </p>
            </motion.div>

            {/* Thống kê chi tiết */}
            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.6, duration: 0.5 }}
              className="grid grid-cols-3 gap-4"
            >
              <div className="text-center p-4 bg-card rounded-xl border-0">
                <div className="text-2xl font-bold text-green-600 mb-1">
                  {correctAnswers}
                </div>
                <p className="text-xs text-muted-foreground font-medium">
                  Câu đúng
                </p>
              </div>
              <div className="text-center p-4 bg-card rounded-xl border-0">
                <div className="text-2xl font-bold text-red-600 mb-1">
                  {totalQuestions - correctAnswers}
                </div>
                <p className="text-xs text-muted-foreground font-medium">
                  Câu sai
                </p>
              </div>
              <div className="text-center p-4 bg-card rounded-xl border-0">
                <div className="text-2xl font-bold text-primary mb-1">
                  {roundHistory.length}
                </div>
                <p className="text-xs text-muted-foreground font-medium">
                  Số vòng
                </p>
              </div>
            </motion.div>

            {/* Thống kê theo vòng - Giao diện sạch */}
            {roundHistory.length > 0 && (
              <motion.div
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.7, duration: 0.5 }}
                className="space-y-4"
              >
                <h3 className="text-base font-semibold text-center text-foreground mb-1">
                  Lịch sử vòng
                </h3>

                {/* Grid layout cho các vòng */}
                <div className="space-y-3">
                  {roundHistory.map((round) => (
                    <div
                      key={round.round}
                      className="bg-muted/30 border-0 rounded-lg p-3"
                    >
                      <div className="flex items-center justify-between">
                        {/* Thông tin vòng */}
                        <div className="flex items-center gap-3">
                          <div className="w-9 h-9 rounded-full bg-primary text-primary-foreground flex items-center justify-center text-sm font-bold">
                            {round.round}
                          </div>
                          <div>
                            <div className="font-medium text-foreground">
                              Vòng {round.round}
                            </div>
                            <div className="text-sm text-muted-foreground">
                              {round.questionsAttempted.length} câu hỏi
                            </div>
                          </div>
                        </div>

                        {/* Kết quả */}
                        <div className="flex items-center gap-3 sm:gap-4">
                          <div className="text-center min-w-[40px]">
                            <div className="text-base sm:text-lg font-bold text-green-600">
                              {round.correctAnswers}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              Đúng
                            </div>
                          </div>
                          <div className="text-center min-w-[40px]">
                            <div className="text-base sm:text-lg font-bold text-red-600">
                              {round.incorrectAnswers}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              Sai
                            </div>
                          </div>
                          <div className="text-center min-w-[50px]">
                            <div className="text-base sm:text-lg font-bold text-primary">
                              {Math.round(
                                (round.correctAnswers /
                                  round.questionsAttempted.length) *
                                  100
                              )}
                              %
                            </div>
                            <div className="text-xs text-muted-foreground">
                              Tỷ lệ
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </motion.div>
            )}

            {/* Nút xem bảng xếp hạng */}
            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.8, duration: 0.5 }}
              className="pt-4 space-y-3"
            >
              <Button
                onClick={handleShowLeaderboard}
                disabled={loadingLeaderboard || leaderboard.length === 0}
                className="w-full h-12 text-base font-medium rounded-xl cursor-pointer"
                size="lg"
                variant="outline"
              >
                {loadingLeaderboard ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Đang tải...
                  </>
                ) : (
                  <>
                    <Trophy className="h-4 w-4 mr-2" />
                    Xem bảng xếp hạng
                  </>
                )}
              </Button>

              <Button
                onClick={() => router.push("/dashboard")}
                className="w-full h-12 text-base font-medium rounded-xl cursor-pointer"
                size="lg"
              >
                <Home className="h-4 w-4 mr-2" />
                Về Dashboard
              </Button>
            </motion.div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
};

export default QuizCompletion;
