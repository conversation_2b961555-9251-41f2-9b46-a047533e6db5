# TASK 1.2: HỆ THỐNG ACHIEVEMENT & BADGE NÂNG CAO

## Tổng quan

Task 1.2 mở rộng hệ thống badges từ simple level-based system thành comprehensive achievement system với:

- **Achievement-based Badges**: Badges dựa trên hành vi và thành tích cụ thể
- **Event Badges**: Badges giới hạn thời gian cho các sự kiện đặc biệt
- **Progress Tracking**: <PERSON> dõi tiến độ đạt achievement
- **Special Effects**: Hiệu ứng đặc biệt cho high-tier users (sẽ implement ở frontend)

## Cấu trúc Database

### Badges Table (Đã mở rộng)
```sql
ALTER TABLE "Badges" ADD COLUMN "badge_type" VARCHAR(20) DEFAULT 'level';
ALTER TABLE "Badges" ADD COLUMN "unlock_criteria" JSONB DEFAULT '{}';
ALTER TABLE "Badges" ADD COLUMN "is_active" BOOLEAN DEFAULT TRUE;
ALTER TABLE "Badges" ADD COLUMN "event_type" VARCHAR(50) DEFAULT NULL;
ALTER TABLE "Badges" ADD COLUMN "valid_from" TIMESTAMP DEFAULT NULL;
ALTER TABLE "Badges" ADD COLUMN "valid_until" TIMESTAMP DEFAULT NULL;
```

### Badge Types
- **level**: Badges mở khóa theo level (như cũ)
- **achievement**: Badges dựa trên thành tích cụ thể
- **event**: Badges giới hạn thời gian
- **milestone**: Badges cho các mốc quan trọng
- **social**: Badges cho hoạt động xã hội

### Unlock Criteria Examples
```json
// Speed answers
{"type": "speed_answers", "count": 10, "max_time": 5000}

// Streak achievements
{"type": "streak", "count": 15}

// Perfect scores
{"type": "perfect_quiz", "count": 5}

// Subject mastery
{"type": "subject_mastery", "subject": "JavaScript", "points": 150}

// Multi-subject mastery
{"type": "multi_subject", "subjects": 5, "min_points": 100}
```

## API Endpoints

### Achievement Progress
```
GET /api/achievements/progress
- Lấy tiến độ achievements của user hiện tại
- Response: { achievements: [...], completion_rate: "75.5%" }
```

### Badge Management
```
GET /api/achievements/badges?rarity=rare&badge_type=achievement
- Lấy danh sách badges với filter
- Public endpoint

POST /api/achievements/track
- Track user action để check achievements
- Body: { action_type: "quiz_completed", action_data: {...} }
```

### Event Badges
```
GET /api/achievements/events
- Lấy event badges đang active

POST /api/achievements/events/participate
- Tham gia event và check event badges
- Body: { event_type: "tet_2025", event_data: {...} }
```

### Statistics
```
GET /api/achievements/stats
- Thống kê badges của user

GET /api/achievements/leaderboard?limit=10&badge_type=achievement
- Bảng xếp hạng theo badges
```

## Achievement Service

### Tracking User Actions
```javascript
// Automatic tracking
AchievementService.trackUserAction(userId, 'quiz_completed', {
    score: 100,
    time_taken: 120,
    perfect_score: true
});

// Manual checking
const newBadges = await AchievementService.checkAchievements(userId, 'speed_answers', {
    response_time: 2000
});
```

### Supported Action Types
- `quiz_completed`: Hoàn thành quiz
- `question_answered`: Trả lời câu hỏi
- `streak_achieved`: Đạt chuỗi thắng
- `daily_login`: Đăng nhập hàng ngày
- `subject_progress`: Tiến bộ theo môn học

## Achievement Middleware

### Auto-tracking Middleware
```javascript
// Track quiz completion
router.post('/complete-quiz', 
    authenticateToken,
    AchievementMiddleware.trackQuizCompletion,
    QuizController.completeQuiz
);

// Track question answers
router.post('/answer-question',
    authenticateToken, 
    AchievementMiddleware.trackQuestionAnswer,
    QuestionController.answerQuestion
);
```

### Custom Action Tracking
```javascript
// Track custom actions
router.post('/custom-action',
    authenticateToken,
    AchievementMiddleware.trackCustomAction('custom_achievement'),
    CustomController.handleAction
);
```

## Badge Categories

### 1. Level-based Badges (Tier Progression)
- First Steps (Level 1)
- Wood Master (Level 12)
- Bronze Legend (Level 24)
- Silver Perfection (Level 36)
- Gold Mastery (Level 48)
- Platinum Ascension (Level 60)
- Onyx Transcendence (Level 72)
- Sapphire Enlightenment (Level 84)
- Ruby Supremacy (Level 96)
- Amethyst Divinity (Level 108)
- Eternal Legend (Level 120)

### 2. Speed-based Achievements
- Speed Demon: 10 câu dưới 5 giây
- Lightning Fast: 25 câu dưới 3 giây
- Flash Master: 50 câu dưới 2 giây
- Time Lord: 100 câu dưới 1.5 giây

### 3. Streak Achievements
- Streak Starter: 5 câu đúng liên tiếp
- Combo King: 10 câu đúng liên tiếp
- Unstoppable: 15 câu đúng liên tiếp
- Legendary Streak: 20 câu đúng liên tiếp
- Infinite Chain: 30 câu đúng liên tiếp

### 4. Perfect Score Achievements
- Perfect Score: 1 quiz 100%
- Perfectionist: 3 quiz 100%
- Flawless Victory: 5 quiz 100%
- Perfection Incarnate: 10 quiz 100%

### 5. Subject Mastery
- HTML Expert: 100 điểm HTML
- CSS Master: 100 điểm CSS
- JavaScript Guru: 150 điểm JavaScript
- Full Stack Hero: Thành thạo 5 môn

### 6. Event Badges (Seasonal)
- Tết Champion 2025: Sự kiện Tết
- Love Scholar: Valentine 2025
- Summer Warrior: Mùa hè 2025
- Spooky Master: Halloween 2025
- Santa Helper: Giáng sinh 2025

## User Stats Tracking

### Gamification Stats Structure
```json
{
    "total_quizzes_completed": 45,
    "total_correct_answers": 234,
    "perfect_scores": 3,
    "best_streak": 15,
    "speed_answers_5s": 12,
    "speed_answers_3s": 8,
    "speed_answers_2s": 4,
    "speed_answers_1_5s": 1,
    "streaks_5_plus": 5,
    "streaks_10_plus": 2,
    "login_streak": 7,
    "subject_points": {
        "HTML": 120,
        "CSS": 95,
        "JavaScript": 180
    }
}
```

## Testing Guide

### 1. Setup Database
```bash
# Run SQL file để setup advanced badge system
psql -d your_database -f gamification_advanced_badges.sql
```

### 2. Test Achievement Tracking
```javascript
// Test speed achievement
POST /api/achievements/track
{
    "action_type": "question_answered",
    "action_data": {
        "correct": true,
        "response_time": 2000
    }
}

// Test quiz completion
POST /api/achievements/track
{
    "action_type": "quiz_completed", 
    "action_data": {
        "score": 100,
        "total_questions": 10
    }
}
```

### 3. Check Progress
```javascript
GET /api/achievements/progress
// Returns user's achievement progress with percentages
```

### 4. Test Event Badges
```javascript
POST /api/achievements/events/participate
{
    "event_type": "tet_2025",
    "event_data": {
        "total_score": 600,
        "boss_defeated": "dragon"
    }
}
```

## Integration với Quiz System

### Tích hợp vào Quiz Controller
```javascript
// Trong QuizController.completeQuiz
const result = await QuizService.completeQuiz(quizData);

// Track achievements
if (result.success) {
    const newBadges = await AchievementService.trackUserAction(
        userId, 
        'quiz_completed', 
        {
            score: result.score,
            perfect_score: result.score === 100,
            time_taken: result.time_taken
        }
    );
    
    result.new_achievements = newBadges.new_badges;
}
```

## Special Effects System (Future)

### Tier-based Name Effects
- **Onyx (61-72)**: Gray border around name
- **Sapphire (73-84)**: Blue wave effect
- **Ruby (85-96)**: Red glow effect  
- **Amethyst (97-108)**: Purple sparkle
- **Master (109+)**: Golden aura with particles

### Implementation Notes
- Effects sẽ được implement ở frontend
- Backend chỉ cần provide tier information
- CSS animations và particle effects

## Performance Considerations

### Indexing
```sql
CREATE INDEX idx_badges_type ON "Badges"("badge_type");
CREATE INDEX idx_badges_event ON "Badges"("event_type");
CREATE INDEX idx_badges_active ON "Badges"("is_active");
```

### Caching Strategy
- Cache user stats trong Redis
- Cache badge criteria để tránh query nhiều lần
- Batch update achievements thay vì real-time

## Next Steps (Task 1.3)

1. **Dynamic Quiz Scoring**: Tích hợp speed bonus, streak bonus vào quiz system
2. **Real-time Notifications**: WebSocket cho achievement notifications
3. **Leaderboard System**: Real-time leaderboards với achievements
4. **Badge Showcase**: UI để display badges và effects

## Files Created/Modified

### New Files
- `gamification_advanced_badges.sql` - Database schema
- `backend/src/services/achievementService.js` - Core achievement logic
- `backend/src/controllers/achievementController.js` - API endpoints
- `backend/src/routes/achievementRoutes.js` - Route definitions
- `backend/src/middleware/achievementMiddleware.js` - Auto-tracking middleware

### Modified Files
- `backend/src/models/badge.js` - Extended with new fields and methods
- `backend/src/app.js` - Added achievement routes

## Summary

Task 1.2 đã hoàn thành việc mở rộng hệ thống badges thành comprehensive achievement system với:

✅ **Achievement-based badges** với criteria phức tạp
✅ **Event badges** có thời hạn
✅ **Progress tracking** chi tiết
✅ **Auto-tracking middleware** 
✅ **Comprehensive API** cho frontend integration
✅ **Performance optimization** với indexing

Hệ thống đã sẵn sàng để tích hợp vào quiz system và implement special effects ở frontend.
