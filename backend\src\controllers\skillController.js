// backend/src/controllers/skillController.js
const SkillService = require('../services/skillService');

class SkillController {
    // =====================================================
    // SKILL SHOP ENDPOINTS
    // =====================================================

    /**
     * Get all available skills
     * GET /api/skills
     */
    static async getAllSkills(req, res) {
        try {
            const { category, tier, cost_type } = req.query;
            
            const filters = {};
            if (category) filters.category = category.toUpperCase();
            if (tier) filters.tier = tier.toUpperCase();
            if (cost_type) filters.costType = cost_type.toUpperCase();

            const result = await SkillService.getAllSkills(filters);
            
            if (result.success) {
                res.status(200).json({
                    success: true,
                    message: 'Skills fetched successfully',
                    data: result
                });
            } else {
                res.status(400).json({
                    success: false,
                    message: result.message,
                    error: result.error
                });
            }
        } catch (error) {
            res.status(500).json({
                success: false,
                message: 'Internal server error',
                error: error.message
            });
        }
    }

    /**
     * Get skills by category
     * GET /api/skills/category/:category
     */
    static async getSkillsByCategory(req, res) {
        try {
            const { category } = req.params;
            
            const result = await SkillService.getSkillsByCategory(category.toUpperCase());
            
            if (result.success) {
                res.status(200).json({
                    success: true,
                    message: `${category} skills fetched successfully`,
                    data: result
                });
            } else {
                res.status(400).json({
                    success: false,
                    message: result.message,
                    error: result.error
                });
            }
        } catch (error) {
            res.status(500).json({
                success: false,
                message: 'Internal server error',
                error: error.message
            });
        }
    }

    /**
     * Get user's owned skills
     * GET /api/skills/my-skills
     */
    static async getUserSkills(req, res) {
        try {
            const userId = req.user.user_id;
            const { include_details = 'true', equipped_only = 'false' } = req.query;
            
            const options = {
                includeSkillDetails: include_details === 'true',
                equippedOnly: equipped_only === 'true'
            };

            const result = await SkillService.getUserSkills(userId, options);
            
            if (result.success) {
                res.status(200).json({
                    success: true,
                    message: 'User skills fetched successfully',
                    data: result
                });
            } else {
                res.status(400).json({
                    success: false,
                    message: result.message,
                    error: result.error
                });
            }
        } catch (error) {
            res.status(500).json({
                success: false,
                message: 'Internal server error',
                error: error.message
            });
        }
    }

    /**
     * Get affordable skills for user
     * GET /api/skills/affordable
     */
    static async getAffordableSkills(req, res) {
        try {
            const userId = req.user.user_id;
            
            const result = await SkillService.getAffordableSkills(userId);
            
            if (result.success) {
                res.status(200).json({
                    success: true,
                    message: 'Affordable skills fetched successfully',
                    data: result
                });
            } else {
                res.status(400).json({
                    success: false,
                    message: result.message,
                    error: result.error
                });
            }
        } catch (error) {
            res.status(500).json({
                success: false,
                message: 'Internal server error',
                error: error.message
            });
        }
    }

    /**
     * Purchase a skill
     * POST /api/skills/purchase
     */
    static async purchaseSkill(req, res) {
        try {
            const userId = req.user.user_id;
            const { skill_id } = req.body;
            
            if (!skill_id) {
                return res.status(400).json({
                    success: false,
                    message: 'Skill ID is required'
                });
            }

            const result = await SkillService.purchaseSkill(userId, skill_id);
            
            if (result.success) {
                res.status(200).json({
                    success: true,
                    message: 'Skill purchased successfully',
                    data: result
                });
            } else {
                res.status(400).json({
                    success: false,
                    message: result.message,
                    error: result.error
                });
            }
        } catch (error) {
            res.status(500).json({
                success: false,
                message: 'Internal server error',
                error: error.message
            });
        }
    }

    // =====================================================
    // QUIZ LOADOUT ENDPOINTS
    // =====================================================

    /**
     * Create/Update quiz skill loadout
     * POST /api/skills/loadout
     */
    static async createQuizLoadout(req, res) {
        try {
            const userId = req.user.user_id;
            const { quiz_session_id, skill_ids } = req.body;
            
            if (!quiz_session_id || !skill_ids || !Array.isArray(skill_ids)) {
                return res.status(400).json({
                    success: false,
                    message: 'Quiz session ID and skill IDs array are required'
                });
            }

            const result = await SkillService.createQuizLoadout(quiz_session_id, userId, skill_ids);
            
            if (result.success) {
                res.status(200).json({
                    success: true,
                    message: 'Quiz loadout created successfully',
                    data: result
                });
            } else {
                res.status(400).json({
                    success: false,
                    message: result.message,
                    error: result.error
                });
            }
        } catch (error) {
            res.status(500).json({
                success: false,
                message: 'Internal server error',
                error: error.message
            });
        }
    }

    /**
     * Get user's quiz loadout
     * GET /api/skills/loadout/:quiz_session_id
     */
    static async getQuizLoadout(req, res) {
        try {
            const userId = req.user.user_id;
            const { quiz_session_id } = req.params;
            
            const result = await SkillService.getQuizLoadout(quiz_session_id, userId);
            
            if (result.success) {
                res.status(200).json({
                    success: true,
                    message: 'Quiz loadout fetched successfully',
                    data: result
                });
            } else {
                res.status(404).json({
                    success: false,
                    message: result.message,
                    error: result.error
                });
            }
        } catch (error) {
            res.status(500).json({
                success: false,
                message: 'Internal server error',
                error: error.message
            });
        }
    }

    /**
     * Get all loadouts for a quiz session (Teacher/Admin only)
     * GET /api/skills/loadouts/:quiz_session_id
     */
    static async getAllQuizLoadouts(req, res) {
        try {
            const { quiz_session_id } = req.params;
            
            const result = await SkillService.getAllQuizLoadouts(quiz_session_id);
            
            if (result.success) {
                res.status(200).json({
                    success: true,
                    message: 'Quiz loadouts fetched successfully',
                    data: result
                });
            } else {
                res.status(400).json({
                    success: false,
                    message: result.message,
                    error: result.error
                });
            }
        } catch (error) {
            res.status(500).json({
                success: false,
                message: 'Internal server error',
                error: error.message
            });
        }
    }

    // =====================================================
    // SKILL EXECUTION ENDPOINTS
    // =====================================================

    /**
     * Execute a skill during quiz
     * POST /api/skills/execute
     */
    static async executeSkill(req, res) {
        try {
            const userId = req.user.user_id;
            const {
                quiz_session_id,
                skill_id,
                target_user_id,
                question_number,
                energy_level,
                game_state
            } = req.body;
            
            if (!quiz_session_id || !skill_id || !question_number || energy_level === undefined) {
                return res.status(400).json({
                    success: false,
                    message: 'Quiz session ID, skill ID, question number, and energy level are required'
                });
            }

            const executionData = {
                quizSessionId: quiz_session_id,
                userId,
                skillId: skill_id,
                targetUserId: target_user_id,
                questionNumber: question_number,
                energyLevel: energy_level,
                gameState: game_state || {}
            };

            const result = await SkillService.executeSkill(executionData);
            
            if (result.success) {
                res.status(200).json({
                    success: true,
                    message: 'Skill executed successfully',
                    data: result
                });
            } else {
                res.status(400).json({
                    success: false,
                    message: result.message,
                    error: result.error
                });
            }
        } catch (error) {
            res.status(500).json({
                success: false,
                message: 'Internal server error',
                error: error.message
            });
        }
    }

    /**
     * Get random skill from user's loadout
     * GET /api/skills/random/:quiz_session_id
     */
    static async getRandomSkill(req, res) {
        try {
            const userId = req.user.user_id;
            const { quiz_session_id } = req.params;
            
            const result = await SkillService.getRandomSkillFromLoadout(quiz_session_id, userId);
            
            if (result.success) {
                res.status(200).json({
                    success: true,
                    message: 'Random skill selected',
                    data: result
                });
            } else {
                res.status(404).json({
                    success: false,
                    message: result.message,
                    error: result.error
                });
            }
        } catch (error) {
            res.status(500).json({
                success: false,
                message: 'Internal server error',
                error: error.message
            });
        }
    }

    // =====================================================
    // SKILL EFFECTS ENDPOINTS
    // =====================================================

    /**
     * Get active skill effects for quiz session
     * GET /api/skills/effects/:quiz_session_id
     */
    static async getActiveEffects(req, res) {
        try {
            const { quiz_session_id } = req.params;
            const { user_id } = req.query;
            
            const result = await SkillService.getActiveEffects(quiz_session_id, user_id);
            
            if (result.success) {
                res.status(200).json({
                    success: true,
                    message: 'Active effects fetched successfully',
                    data: result
                });
            } else {
                res.status(400).json({
                    success: false,
                    message: result.message,
                    error: result.error
                });
            }
        } catch (error) {
            res.status(500).json({
                success: false,
                message: 'Internal server error',
                error: error.message
            });
        }
    }

    // =====================================================
    // STATISTICS ENDPOINTS
    // =====================================================

    /**
     * Get skill usage statistics
     * GET /api/skills/stats/usage
     */
    static async getSkillUsageStats(req, res) {
        try {
            const { limit = 10, timeframe, quiz_session_id } = req.query;
            
            const options = { limit: parseInt(limit) };
            if (timeframe) options.timeframe = timeframe;
            if (quiz_session_id) options.quizSessionId = quiz_session_id;

            const result = await SkillService.getSkillUsageStats(options);
            
            if (result.success) {
                res.status(200).json({
                    success: true,
                    message: 'Skill usage statistics fetched successfully',
                    data: result
                });
            } else {
                res.status(400).json({
                    success: false,
                    message: result.message,
                    error: result.error
                });
            }
        } catch (error) {
            res.status(500).json({
                success: false,
                message: 'Internal server error',
                error: error.message
            });
        }
    }

    /**
     * Get user's skill statistics
     * GET /api/skills/stats/my-stats
     */
    static async getUserSkillStats(req, res) {
        try {
            const userId = req.user.user_id;
            
            const result = await SkillService.getUserSkillStats(userId);
            
            if (result.success) {
                res.status(200).json({
                    success: true,
                    message: 'User skill statistics fetched successfully',
                    data: result
                });
            } else {
                res.status(400).json({
                    success: false,
                    message: result.message,
                    error: result.error
                });
            }
        } catch (error) {
            res.status(500).json({
                success: false,
                message: 'Internal server error',
                error: error.message
            });
        }
    }
}

module.exports = SkillController;
