# Security Architecture

## Authentication & Authorization

**JWT-based Authentication**:

- **Token Storage**: localStorage (frontend)
- **Token Validation**: Middleware-based (backend)
- **Role-based Access**: Admin, Teacher, Student roles
- **Password Security**: bcrypt hashing với salt

## API Security

**Security Measures**:

- **CORS**: Configured cho multiple localhost ports
- **Rate Limiting**: Not implemented (technical debt)
- **Input Validation**: Sequelize ORM protection
- **SQL Injection**: ORM-based protection
