# 🔗 Leaderboard Socket.IO Integration

## 📋 Overview

Hệ thống Leaderboard đã được tích hợp với Socket.IO để cung cấp **real-time updates** cho Quiz Racing và Social System theo yêu cầu từ game mechanics documents.

## 🚀 Socket Events

### 1. **Leaderboard Updates**

#### `leaderboard_update`
Đ<PERSON>ợc emit khi có thay đổi tổng thể trong leaderboard system.

```javascript
// Server emits
io.emit('leaderboard_update', {
    type: 'quiz_completion',
    timestamp: '2024-01-15T10:30:00.000Z',
    data: {
        user_id: 123,
        quiz_result: { /* quiz result data */ },
        updated_rankings: ['GLOBAL', 'TIER_BASED', 'TIME_BASED']
    }
});

// Client listens
socket.on('leaderboard_update', (data) => {
    console.log('Leaderboard updated:', data);
    // Refresh leaderboard UI
    refreshLeaderboardDisplay();
});
```

### 2. **Individual Rank Changes**

#### `rank_changed` (Personal)
Đ<PERSON><PERSON><PERSON> emit đến specific user khi rank của họ thay đổi.

```javascript
// Server emits to specific user
io.to(`user_${userId}`).emit('rank_changed', {
    user_id: 123,
    leaderboard_type: 'GLOBAL',
    criteria: 'TOTAL_XP',
    tier_filter: null,
    old_rank: 52,
    new_rank: 45,
    old_score: 2300,
    new_score: 2450,
    score_change: 150,
    timestamp: '2024-01-15T10:30:00.000Z'
});

// Client listens
socket.on('rank_changed', (data) => {
    // Show rank change notification
    showRankChangeNotification(data);
    // Update user's personal rank display
    updatePersonalRank(data);
});
```

#### `user_rank_changed` (Global)
Được emit đến tất cả users để update live leaderboard.

```javascript
// Server emits globally
io.emit('user_rank_changed', {
    user_id: 123,
    new_rank: 45,
    old_rank: 52,
    criteria: 'TOTAL_XP',
    score_change: 150
});

// Client listens
socket.on('user_rank_changed', (data) => {
    // Update leaderboard display for this user
    updateLeaderboardEntry(data.user_id, data);
});
```

### 3. **Quiz Racing Integration**

#### `quiz_leaderboard_update`
Được emit đến quiz session room cho real-time racing updates.

```javascript
// Server emits to quiz room
io.to(`quiz_${quizSessionId}`).emit('quiz_leaderboard_update', {
    session_id: 'quiz_123',
    leaderboard: [
        { user_id: 1, username: 'Player1', current_score: 1250, rank: 1 },
        { user_id: 2, username: 'Player2', current_score: 1100, rank: 2 },
        // ... more players
    ],
    timestamp: '2024-01-15T10:30:00.000Z'
});

// Client listens
socket.on('quiz_leaderboard_update', (data) => {
    // Update real-time quiz racing leaderboard
    updateQuizRacingLeaderboard(data.leaderboard);
});
```

### 4. **Social Ranking Reactions**

#### `social_ranking_reaction`
Được emit khi có emoji reactions liên quan đến ranking changes.

```javascript
// Server emits
io.emit('social_ranking_reaction', {
    user_id: 123,
    reaction_type: 'rank_celebration',
    emoji: 'partying-face',
    target_rank: 10,
    timestamp: '2024-01-15T10:30:00.000Z'
});

// Client listens
socket.on('social_ranking_reaction', (data) => {
    // Show emoji reaction on leaderboard
    showSocialReaction(data);
});
```

## 🔧 Implementation Details

### Server-Side Setup

1. **LeaderboardService Initialization**
```javascript
// In app.js
const { initializeSocket } = require('./services/leaderboardService');
initializeSocket(io);
```

2. **Automatic Event Emission**
```javascript
// In LeaderboardService.updateUserScore()
if (oldRank && newRank && oldRank.rank !== newRank.rank) {
    this.emitRankChange(userId, {
        leaderboard_type: leaderboardType,
        criteria: criteria,
        old_rank: oldRank.rank,
        new_rank: newRank.rank,
        score_change: newScore - (oldRank.score_value || 0)
    });
}
```

### Client-Side Integration

1. **Socket Connection**
```javascript
const socket = io('http://localhost:3000');

// Join user-specific room
socket.emit('join_user_room', { user_id: currentUserId });

// Join quiz room when participating
socket.emit('join_quiz_room', { quiz_session_id: quizId });
```

2. **Event Listeners Setup**
```javascript
// Personal rank changes
socket.on('rank_changed', handleRankChange);

// Global leaderboard updates
socket.on('leaderboard_update', handleLeaderboardUpdate);

// Quiz racing updates
socket.on('quiz_leaderboard_update', handleQuizRacingUpdate);

// Social reactions
socket.on('social_ranking_reaction', handleSocialReaction);
```

## 🎮 Quiz Racing Integration

### Real-time Leaderboard Updates
Theo yêu cầu từ `quiz_game_flowchart_corrected.md`:

- **Line 59**: `UpdateLeaderboard[Cập nhật bảng xếp hạng real-time]`
- **Line 72**: `UpdateRaceUI[Cập nhật giao diện đua: Leaderboard + Thanh năng lượng + Streak]`

```javascript
// During quiz, after each question
socket.on('quiz_question_answered', async (data) => {
    // Update leaderboard
    await LeaderboardService.updateUserScore(
        data.user_id, 
        'QUIZ_SCORE', 
        data.current_total_score
    );
    
    // Get updated leaderboard for this quiz session
    const updatedLeaderboard = await getQuizSessionLeaderboard(data.quiz_session_id);
    
    // Emit to all participants
    LeaderboardService.emitQuizRacingUpdate(data.quiz_session_id, updatedLeaderboard);
});
```

## 🤝 Social System Integration

### Emoji Reactions to Ranking
Theo yêu cầu từ `04-social-system.md`:

```javascript
// When user reacts to ranking change
socket.on('send_ranking_reaction', (data) => {
    LeaderboardService.emitSocialRankingReaction(data.user_id, {
        type: 'rank_celebration',
        emoji: data.emoji,
        target_rank: data.target_rank
    });
});
```

## 🔄 Event Flow Examples

### Quiz Completion Flow
1. User completes quiz
2. `LeaderboardService.updateUserFromQuizResult()` called
3. Multiple rank updates trigger `emitRankChange()` events
4. Global `leaderboard_update` event emitted
5. All connected clients receive updates
6. UI refreshes with new rankings

### Real-time Racing Flow
1. User answers question in quiz racing
2. Score updated via `updateUserScore()`
3. `quiz_leaderboard_update` emitted to quiz room
4. All participants see live ranking changes
5. Social reactions can be sent in real-time

## 🧪 Testing Socket Events

### Using Postman/Socket.IO Client
```javascript
// Connect to server
const socket = io('http://localhost:3000');

// Test rank change
socket.emit('test_rank_change', {
    user_id: 123,
    criteria: 'TOTAL_XP',
    new_score: 2500
});

// Listen for events
socket.on('rank_changed', console.log);
socket.on('leaderboard_update', console.log);
```

## 📊 Performance Considerations

1. **Room Management**: Users join specific rooms to reduce unnecessary broadcasts
2. **Event Throttling**: Prevent spam by limiting event frequency
3. **Data Optimization**: Only send necessary data in events
4. **Connection Handling**: Graceful handling of disconnections

## 🔐 Security Notes

1. **Authentication**: Verify user identity before joining rooms
2. **Rate Limiting**: Prevent abuse of socket events
3. **Data Validation**: Validate all incoming socket data
4. **Room Authorization**: Ensure users can only join authorized rooms

---

**Integration Status**: ✅ **COMPLETE**
- Socket.IO initialized in LeaderboardService
- Real-time events implemented
- Quiz Racing integration ready
- Social System integration ready
