# Migration Guide: Radar Analytics to Chapter Analytics

## Overview

This guide helps you migrate from the deprecated LO-based radar analytics to the new chapter-based analytics system.

## API Migration

### 1. getCurrentUserRadarData → getDetailedAnalysis

**Old (Deprecated):**
```typescript
import { quizService } from '@/lib/services/api';

const radarData = await quizService.getCurrentUserRadarData(quizId);
```

**New (Recommended):**
```typescript
import { chapterAnalyticsService } from '@/lib/services/api';

const chapterData = await chapterAnalyticsService.getDetailedAnalysis({
  quiz_id: quizId,
  user_id: userId
});
```

### 2. getAverageRadarData → getComprehensiveAnalysis

**Old (Deprecated):**
```typescript
const averageData = await quizService.getAverageRadarData(quizId);
```

**New (Recommended):**
```typescript
const comprehensiveData = await chapterAnalyticsService.getComprehensiveAnalysis({
  subject_id: subjectId,
  user_id: userId,
  start_date: '2024-01-01', // optional
  end_date: '2024-12-31'    // optional
});
```

### 3. getTopPerformerRadarData → getTeacherAnalytics

**Old (Deprecated):**
```typescript
const topPerformerData = await quizService.getTopPerformerRadarData(quizId);
```

**New (Recommended):**
```typescript
const teacherData = await chapterAnalyticsService.getTeacherAnalytics({
  quiz_id: quizId,
  include_student_details: true,
  include_recommendations: true
});
```

### 4. getAllRadarData → Multiple Chapter Analytics Calls

**Old (Deprecated):**
```typescript
const allData = await quizService.getAllRadarData(quizId);
```

**New (Recommended):**
```typescript
// For student view
const detailedAnalysis = await chapterAnalyticsService.getDetailedAnalysis({
  quiz_id: quizId,
  user_id: userId
});

// For comprehensive subject analysis
const comprehensiveAnalysis = await chapterAnalyticsService.getComprehensiveAnalysis({
  subject_id: subjectId,
  user_id: userId
});

// For teacher dashboard
const teacherAnalytics = await chapterAnalyticsService.getTeacherAnalytics({
  quiz_id: quizId
});
```

## Data Structure Changes

### Learning Outcomes → Chapters & Sections

**Old Structure:**
```typescript
interface RadarChartData {
  learning_outcomes: Record<string, {
    accuracy: number;
    questions_count: number;
    average_response_time: number;
  }>;
}
```

**New Structure:**
```typescript
interface ChapterAnalysisData {
  chapters: Array<{
    chapter_id: number;
    chapter_name: string;
    performance_score: number;
    sections: Array<{
      section_id: number;
      section_name: string;
      content_type: "text" | "video" | "exercise";
      mastery_level: number;
    }>;
  }>;
}
```

### Performance Metrics Enhancement

**Old:**
```typescript
interface PerformanceMetrics {
  average_response_time: number;
  completion_rate: number;
  first_attempt_accuracy: number;
  overall_accuracy: number;
}
```

**New:**
```typescript
interface ChapterPerformanceMetrics {
  chapter_id: number;
  chapter_name: string;
  performance_score: number;
  mastery_level: number;
  completion_rate: number;
  average_response_time: number;
  total_questions: number;
  correct_answers: number;
}
```

## Component Migration

### RadarChart Components

**Keep existing components working** - The radar chart components will continue to function with the old API during the transition period.

**Gradual migration approach:**
1. Keep existing RadarChart components unchanged
2. Create new ChapterAnalyticsChart components
3. Gradually replace RadarChart usage with ChapterAnalyticsChart
4. Remove RadarChart components in future releases

### Error Handling

**Old:**
```typescript
try {
  const data = await quizService.getCurrentUserRadarData(quizId);
} catch (error) {
  console.error('Radar data fetch failed:', error);
}
```

**New:**
```typescript
try {
  const data = await chapterAnalyticsService.getDetailedAnalysis({
    quiz_id: quizId,
    user_id: userId
  });
} catch (error) {
  console.error('Chapter analytics fetch failed:', error);
  // Error messages are more specific and user-friendly
}
```

## Loading States & Retry Logic

The new chapter analytics service includes built-in retry logic:

```typescript
// Automatic retry with exponential backoff
const dataWithRetry = await chapterAnalyticsService.getDetailedAnalysisWithRetry({
  quiz_id: quizId,
  user_id: userId
}, 3); // max 3 retries
```

## Timeline

- **Phase 1** (Current): Both systems available, deprecation warnings active
- **Phase 2** (Next release): New components using chapter analytics
- **Phase 3** (Future): Remove radar analytics system completely

## Benefits of Migration

1. **Better User Understanding**: Chapter-based structure is more intuitive
2. **Enhanced Recommendations**: Section-specific learning suggestions
3. **Improved Performance**: Optimized API endpoints
4. **Better Error Handling**: More specific error messages and retry logic
5. **Future-Proof**: Aligned with new educational content structure

## Support

If you encounter issues during migration:
1. Check console warnings for specific guidance
2. Refer to TypeScript types for data structure details
3. Test with both old and new APIs during transition
4. Contact development team for complex migration scenarios
