{"info": {"_postman_id": "quick-login-test", "name": "Quick Login Test", "description": "Quick login to get fresh token", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "base_url", "value": "http://localhost:8888", "type": "string"}], "item": [{"name": "<PERSON><PERSON> and <PERSON> Token", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Login successful', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.success).to.be.true;", "    pm.expect(responseJson.data).to.have.property('token');", "});", "", "// Store token for use in other requests", "const responseJson = pm.response.json();", "if (responseJson.success && responseJson.data && responseJson.data.token) {", "    pm.environment.set('auth_token', responseJson.data.token);", "    pm.environment.set('user_id', responseJson.data.user.user_id);", "    console.log('Token stored:', responseJson.data.token.substring(0, 20) + '...');", "    console.log('User ID:', responseJson.data.user.user_id);", "    console.log('User Level:', responseJson.data.user.current_level);", "    console.log('User Email:', responseJson.data.user.email);", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\"\n}"}, "url": {"raw": "{{base_url}}/api/users/login", "host": ["{{base_url}}"], "path": ["api", "users", "login"]}}, "response": []}, {"name": "Test Token - Get Frame Shop", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Token works - got shop data', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.success).to.be.true;", "    pm.expect(responseJson.data).to.have.property('user_info');", "});", "", "console.log('Frame shop response:', JSON.stringify(pm.response.json(), null, 2));"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/avatar/frames/shop", "host": ["{{base_url}}"], "path": ["api", "avatar", "frames", "shop"]}}, "response": []}]}