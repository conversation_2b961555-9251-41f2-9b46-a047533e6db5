"use client";

import React, { useState, useMemo, useCallback } from "react";
import { Search, Filter, Check, Lock, Star, Crown, Gem, Sparkles } from "lucide-react";
import { Input } from "@/components/ui/forms";
import { Button } from "@/components/ui/forms";
import { Badge } from "@/components/ui/feedback";
import { Card, CardContent } from "@/components/ui/layout";
import { Skeleton } from "@/components/ui/feedback";
import { cn } from "@/lib/utils";
import { EmojiData, AvatarRarity } from "@/lib/types/avatar";
import { useAvatar } from "@/lib/hooks/use-avatar";

// Item state types for visual indicators
export type ItemState = "owned" | "unlockable" | "locked";

// Category types for emoji organization
export type EmojiCategory = "GENERAL" | "HAPPY" | "SAD" | "ANGRY" | "SURPRISED" | "LOVE" | "CELEBRATION" | "ANIMALS" | "SPECIAL";

// Props interface for EmojiGrid component
export interface EmojiGridProps {
  // Data
  ownedEmojis: EmojiData[];
  unlockableEmojis: EmojiData[];
  lockedEmojis: EmojiData[];
  
  // Current equipped emoji (if any)
  equippedEmojiId?: number;
  
  // Actions
  onEquipEmoji: (emojiId: number) => Promise<void>;
  
  // Loading states
  isLoading?: boolean;
  isEquipping?: boolean;
  
  // Styling
  className?: string;
}

// Individual emoji item component
interface EmojiItemProps {
  emoji: EmojiData;
  state: ItemState;
  isEquipped: boolean;
  isEquipping: boolean;
  onEquip: (emojiId: number) => Promise<void>;
}

const EmojiItem: React.FC<EmojiItemProps> = ({
  emoji,
  state,
  isEquipped,
  isEquipping,
  onEquip,
}) => {
  const { getRarityColor, getRarityDisplayName } = useAvatar();
  const [imageLoading, setImageLoading] = useState(true);
  const [imageError, setImageError] = useState(false);

  const rarityColor = getRarityColor(emoji.rarity);
  const rarityDisplayName = getRarityDisplayName(emoji.rarity);

  const handleEquip = useCallback(async () => {
    if (state === "owned" && !isEquipped && !isEquipping) {
      await onEquip(emoji.emoji_id);
    }
  }, [state, isEquipped, isEquipping, onEquip, emoji.emoji_id]);

  const getStateIcon = () => {
    if (isEquipped) return <Check className="w-4 h-4 text-green-500" />;
    if (state === "locked") return <Lock className="w-4 h-4 text-gray-400" />;
    if (state === "unlockable") return <Star className="w-4 h-4 text-yellow-500" />;
    return null;
  };

  const getUnlockText = () => {
    if (state === "unlockable") return emoji.unlock_description;
    if (state === "locked") return emoji.unlock_description;
    return null;
  };

  return (
    <Card
      className={cn(
        "relative cursor-pointer transition-all duration-200 hover:shadow-md",
        state === "owned" && !isEquipped && "hover:scale-105",
        isEquipped && "ring-2 ring-green-500 bg-green-50",
        state === "locked" && "opacity-60",
        isEquipping && "pointer-events-none opacity-50"
      )}
      onClick={handleEquip}
    >
      <CardContent className="p-4">
        {/* State Indicator */}
        <div className="absolute top-2 right-2 z-10">
          {getStateIcon()}
        </div>

        {/* Premium Badge */}
        {emoji.is_premium && (
          <div className="absolute top-2 left-2 z-10">
            <Badge
              variant="outline"
              className="text-xs px-1.5 py-0.5 bg-gradient-to-r from-yellow-400 to-orange-500 text-white border-none"
            >
              Premium
            </Badge>
          </div>
        )}

        {/* Emoji Display */}
        <div className="relative mb-4 p-4 bg-muted/30 rounded-lg overflow-hidden min-h-[80px] flex items-center justify-center">
          {emoji.emoji_image ? (
            <div className="relative w-12 h-12">
              {imageLoading && (
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin" />
                </div>
              )}
              <img
                src={emoji.emoji_image}
                alt={emoji.emoji_name}
                className={cn(
                  "w-full h-full object-contain transition-opacity duration-200",
                  imageLoading && "opacity-0",
                  imageError && "hidden"
                )}
                onLoad={() => setImageLoading(false)}
                onError={() => {
                  setImageError(true);
                  setImageLoading(false);
                }}
              />
              {imageError && (
                <div className="w-full h-full flex items-center justify-center text-2xl">
                  {emoji.emoji_display || emoji.emoji_unicode}
                </div>
              )}
            </div>
          ) : (
            <div className="text-3xl">
              {emoji.emoji_display || emoji.emoji_unicode}
            </div>
          )}
        </div>

        {/* Emoji Info */}
        <div className="space-y-2">
          <h4 className="font-medium text-sm truncate">{emoji.emoji_name}</h4>

          {emoji.description && (
            <p className="text-xs text-muted-foreground line-clamp-2">
              {emoji.description}
            </p>
          )}

          {/* Category and Rarity */}
          <div className="flex items-center justify-between">
            <Badge
              variant="outline"
              className="text-xs"
            >
              {emoji.category_display}
            </Badge>

            <Badge
              variant="outline"
              className="text-xs"
              style={{ borderColor: rarityColor, color: rarityColor }}
            >
              {rarityDisplayName}
            </Badge>
          </div>

          {/* Unlock Requirements */}
          {getUnlockText() && (
            <p className="text-xs text-muted-foreground italic">
              {getUnlockText()}
            </p>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

// Loading skeleton component
const EmojiSkeleton: React.FC = () => (
  <Card>
    <CardContent className="p-4">
      <Skeleton className="h-20 w-full mb-4 rounded-lg" />
      <Skeleton className="h-4 w-3/4 mb-2" />
      <Skeleton className="h-3 w-full mb-2" />
      <div className="flex justify-between">
        <Skeleton className="h-5 w-16" />
        <Skeleton className="h-5 w-16" />
      </div>
    </CardContent>
  </Card>
);

// Main EmojiGrid component
export const EmojiGrid: React.FC<EmojiGridProps> = ({
  ownedEmojis,
  unlockableEmojis,
  lockedEmojis,
  equippedEmojiId,
  onEquipEmoji,
  isLoading = false,
  isEquipping = false,
  className,
}) => {
  // State for search and filters
  const [searchQuery, setSearchQuery] = useState("");
  const [categoryFilter, setCategoryFilter] = useState<EmojiCategory | "all">("all");
  const [rarityFilter, setRarityFilter] = useState<AvatarRarity | "all">("all");

  // Combine all emojis with their states
  const allEmojis = useMemo(() => {
    const emojisWithState = [
      ...ownedEmojis.map((emoji) => ({
        emoji,
        state: "owned" as ItemState,
      })),
      ...unlockableEmojis.map((emoji) => ({
        emoji,
        state: "unlockable" as ItemState,
      })),
      ...lockedEmojis.map((emoji) => ({
        emoji,
        state: "locked" as ItemState,
      })),
    ];

    return emojisWithState;
  }, [ownedEmojis, unlockableEmojis, lockedEmojis]);

  // Filter emojis based on search and filters
  const filteredEmojis = useMemo(() => {
    let filtered = allEmojis;

    // Search filter
    if (searchQuery) {
      const searchLower = searchQuery.toLowerCase();
      filtered = filtered.filter(
        ({ emoji }) =>
          emoji.emoji_name.toLowerCase().includes(searchLower) ||
          (emoji.description &&
            emoji.description.toLowerCase().includes(searchLower)) ||
          emoji.category_display.toLowerCase().includes(searchLower)
      );
    }

    // Category filter
    if (categoryFilter !== "all") {
      filtered = filtered.filter(({ emoji }) => emoji.category === categoryFilter);
    }

    // Rarity filter
    if (rarityFilter !== "all") {
      filtered = filtered.filter(({ emoji }) => emoji.rarity === rarityFilter);
    }

    // Sort by category, then rarity, then name
    filtered.sort((a, b) => {
      // First by category
      if (a.emoji.category !== b.emoji.category) {
        return a.emoji.category.localeCompare(b.emoji.category);
      }

      // Then by rarity
      const rarityOrder = ["LEGENDARY", "EPIC", "RARE", "UNCOMMON", "COMMON"];
      const aRarityIndex = rarityOrder.indexOf(a.emoji.rarity);
      const bRarityIndex = rarityOrder.indexOf(b.emoji.rarity);

      if (aRarityIndex !== bRarityIndex) {
        return aRarityIndex - bRarityIndex;
      }

      // Finally by name
      return a.emoji.emoji_name.localeCompare(b.emoji.emoji_name);
    });

    return filtered;
  }, [allEmojis, searchQuery, categoryFilter, rarityFilter]);

  // Get unique categories for filter
  const availableCategories = useMemo(() => {
    const categories = new Set(allEmojis.map(({ emoji }) => emoji.category));
    return Array.from(categories).sort();
  }, [allEmojis]);

  if (isLoading) {
    return (
      <div className={cn("space-y-4", className)}>
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
          {Array.from({ length: 12 }).map((_, index) => (
            <EmojiSkeleton key={index} />
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className={cn("space-y-4", className)}>
      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
          <Input
            placeholder="Tìm kiếm emoji..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>

        <div className="flex gap-2">
          <select
            value={categoryFilter}
            onChange={(e) => setCategoryFilter(e.target.value as EmojiCategory | "all")}
            className="px-3 py-2 border border-input bg-background rounded-md text-sm"
          >
            <option value="all">Tất cả danh mục</option>
            {availableCategories.map((category) => (
              <option key={category} value={category}>
                {category}
              </option>
            ))}
          </select>

          <select
            value={rarityFilter}
            onChange={(e) => setRarityFilter(e.target.value as AvatarRarity | "all")}
            className="px-3 py-2 border border-input bg-background rounded-md text-sm"
          >
            <option value="all">Tất cả độ hiếm</option>
            <option value="COMMON">Phổ biến</option>
            <option value="UNCOMMON">Không phổ biến</option>
            <option value="RARE">Hiếm</option>
            <option value="EPIC">Sử thi</option>
            <option value="LEGENDARY">Huyền thoại</option>
          </select>
        </div>
      </div>

      {/* Emojis Grid */}
      {filteredEmojis.length > 0 ? (
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
          {filteredEmojis.map(({ emoji, state }) => (
            <EmojiItem
              key={emoji.emoji_id}
              emoji={emoji}
              state={state}
              isEquipped={equippedEmojiId === emoji.emoji_id}
              isEquipping={isEquipping}
              onEquip={onEquipEmoji}
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <div className="text-muted-foreground mb-2">
            <Sparkles className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p className="text-lg font-medium">Không tìm thấy emoji</p>
            <p className="text-sm">Thử thay đổi bộ lọc hoặc từ khóa tìm kiếm</p>
          </div>
        </div>
      )}
    </div>
  );
};
