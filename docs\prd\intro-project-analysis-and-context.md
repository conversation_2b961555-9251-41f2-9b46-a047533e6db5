# Intro Project Analysis and Context

## Analysis Source

**Document-project output available**: Brownfield architecture document tại `docs/brownfield-architecture.md` với phân tích chi tiết về frontend structure hiện tại.

## Current Project State

**Frontend hiện tại**: Next.js 15 application với App Router, React 19, TypeScript, Radix UI components, Tailwind CSS. Cấu trúc hiện tại có các vấn đề về organization:

- Components rải rác trong nhiều thư mục
- Cách đặt tên không nhất quán
- Business logic lẫn với UI components
- Không tách biệt rõ ràng các concerns
- C<PERSON> thể có code trùng lặp trong UI components

## Available Documentation Analysis

✅ **Document-project analysis available** - sử dụng tài liệu kỹ thuật hiện có

**Tài liệu chính từ document-project**:

- ✅ Tài liệu Tech Stack
- ✅ Cấu trúc Source Tree/Architecture
- ✅ Tài liệu API
- ✅ Tài liệu Technical Debt

## Enhancement Scope Definition

**Enhancement Type**:

- ✅ **Cải tiến UI/UX** (tái tổ chức cấu trúc)
- ✅ **Cải thiện Performance/Scalability** (tối ưu code)
- ✅ **Nâng cấp Technology Stack** (patterns tổ chức tốt hơn)

**Enhancement Description**:
Tái cấu trúc toàn bộ frontend codebase để có cấu trúc thư mục logic hơn, loại bỏ code thừa, tối ưu hóa tổ chức component, và cải thiện khả năng maintain mà không thay đổi UI/UX hoặc business logic hiện có.

**Impact Assessment**: ✅ **Tác động Đáng kể** (thay đổi substantial code hiện có)

## Goals and Background Context

**Goals**:

- Tái tổ chức cấu trúc thư mục frontend theo best practices
- Loại bỏ code trùng lặp và code không dùng
- Cải thiện khả năng tái sử dụng component và maintain
- Tối ưu hóa import paths và dependencies
- Đảm bảo cách đặt tên nhất quán
- **CRITICAL**: Loại bỏ HOÀN TOÀN tất cả test files và test-related configurations (NO TESTING POLICY)

**Background Context**:
Frontend hiện tại hoạt động tốt về chức năng nhưng có technical debt về tổ chức code. Với sự phát triển của dự án, cần cấu trúc rõ ràng hơn để dễ maintain và scale.

## Change Log

| Thay đổi    | Ngày       | Phiên bản | Mô tả                                     | Tác giả         |
| ----------- | ---------- | --------- | ----------------------------------------- | --------------- |
| PRD ban đầu | 2025-01-23 | 1.0       | Tạo PRD tái cấu trúc frontend             | Product Manager |
| Epic 2 thêm | 2025-07-26 | 2.0       | Thêm Epic 2 - Chapter Analytics           | John (PM)       |
| Epic 3 thêm | 2025-07-29 | 3.0       | Thêm Epic 3 - Level/XP System Integration | John (PM)       |
