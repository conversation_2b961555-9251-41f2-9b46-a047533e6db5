```mermaid
flowchart TD
    Start([Bắt đầu]) --> PrepSkills[Sinh viên trang bị 4 kỹ năng từ kho]
    PrepSkills --> JoinRoom[Tham gia phòng chờ bằng mã PIN]
    JoinRoom --> ShowLoadout[Hiển thị trang bị kỹ năng của mọi người]
    ShowLoadout --> WaitStart[Chờ giáo viên bắt đầu]

    WaitStart --> QuizStart[Giáo viên bắt đầu - Backend gửi quizStarted]
    QuizStart --> InitRace[Hiển thị giao diện đua:<br/>Thanh năng lượng + Bảng xếp hạng real-time]

    InitRace --> NewQuestion[Nhận câu hỏi mới]
    NewQuestion --> StartTimer[Bắt đầu đếm ngược thời gian]
    StartTimer --> PlayerChoice{Sinh viên chọn}

    PlayerChoice -->|Tr<PERSON> lời| Answer[Chọn đáp án]
    PlayerChoice -->|Bỏ qua câu hỏi này| SkipQuestion[Đ<PERSON>h dấu câu bỏ qua - 0 điểm]

    Answer --> SubmitAnswer[Gửi đáp án + timestamp lên server]
    SubmitAnswer --> ValidateAnswer[Backend xác thực đáp án]

    ValidateAnswer --> CalcScore{Tính điểm}
    CalcScore -->|Đúng| BasePoints[ĐIỂM CƠ BẢN VÒNG 1:<br/>Dễ: +60 điểm<br/>Trung bình: +100 điểm<br/>Khó: +140 điểm<br/><br/>VÒNG 2+: 50% điểm vòng 1]
    CalcScore -->|Sai| NoPoints[0 điểm + Reset streak]

    BasePoints --> CheckRoundForBonus{Đang ở vòng nào?}
    CheckRoundForBonus -->|Vòng 1| CalcSpeedBonus[Tính thưởng tốc độ<br/>Tối đa +50% điểm cơ bản nếu trả lời trong 5s đầu<br/>VD: Câu khó +70 điểm thưởng tối đa]
    CheckRoundForBonus -->|Vòng 2+| SkipBonus[KHÔNG có thưởng tốc độ/streak]

    CalcSpeedBonus --> CheckStreak{Đang có chuỗi thắng?}
    CheckStreak -->|Có| AddStreakBonus[Thêm điểm thưởng streak<br/>+10, +15, +20... tăng dần]
    CheckStreak -->|Không| UpdateStreak[Bắt đầu streak mới = 1]

    AddStreakBonus --> IncreaseStreak[Tăng chuỗi thắng +1]
    IncreaseStreak --> UpdateEnergy[Cập nhật thanh năng lượng<br/>+20% đúng, +10% tốc độ, +5% streak]
    UpdateStreak --> UpdateEnergy
    SkipBonus --> UpdateEnergy

    NoPoints --> ResetStreak[Reset chuỗi thắng = 0]
    ResetStreak --> SkipEnergy[Không cộng năng lượng]
    SkipQuestion --> SkipEnergy

    UpdateEnergy --> CheckEnergyFull{Năng lượng = 100%?}
    CheckEnergyFull -->|Có| RandomSkill[Server chọn ngẫu nhiên<br/>1/4 kỹ năng đã trang bị]
    CheckEnergyFull -->|Không| SkipSkill[Tiếp tục không có kỹ năng]
    SkipEnergy --> SkipSkill

    RandomSkill --> ShowSkillReady[Hiển thị nút kỹ năng sáng lên]
    ShowSkillReady --> SkillDecision{Sinh viên dùng kỹ năng?}

    SkillDecision -->|Dùng| SelectTarget[Chọn mục tiêu nếu cần<br/>VD: Hố đen → Top 1<br/>Cắp điểm → Người trên mình]
    SkillDecision -->|Không dùng| SaveSkill[Giữ kỹ năng để dùng sau]

    SelectTarget --> ExecuteSkill[Server thực thi kỹ năng]
    ExecuteSkill --> ApplyEffect[Áp dụng hiệu ứng:<br/>Tấn công/Phòng thủ/Bứt phá]
    ApplyEffect --> BroadcastSkill[Thông báo kỹ năng cho tất cả người chơi]
    BroadcastSkill --> ResetEnergy[Reset thanh năng lượng = 0%]

    ResetEnergy --> UpdateLeaderboard
    SkipSkill --> UpdateLeaderboard[Cập nhật bảng xếp hạng real-time]
    SaveSkill --> UpdateLeaderboard

    UpdateLeaderboard --> CheckGlobalEvent{Sự kiện toàn cục?}
    CheckGlobalEvent -->|Câu hỏi vàng| GoldenQuestion[Điểm số nhân đôi cho câu này]
    CheckGlobalEvent -->|Vùng tăng tốc| SpeedZone[Thưởng tốc độ x2 cho 3 câu tiếp]
    CheckGlobalEvent -->|Không| NormalFlow[Tiếp tục bình thường]

    GoldenQuestion --> BroadcastEvent[Thông báo sự kiện cho tất cả]
    SpeedZone --> BroadcastEvent
    BroadcastEvent --> NormalFlow

    NormalFlow --> ShowResults[Hiển thị kết quả câu trả lời + điểm nhận được]
    ShowResults --> UpdateRaceUI[Cập nhật giao diện đua:<br/>Leaderboard + Thanh năng lượng + Streak]

    UpdateRaceUI --> CheckRound{Đang ở vòng nào?}

    %% VÒNG 1: Hiện tất cả câu hỏi tuần tự
    CheckRound -->|Vòng 1| CheckRound1End{Hết tất cả câu vòng 1?}
    CheckRound1End -->|Còn câu| NewQuestion
    CheckRound1End -->|Hết| PrepareNextRound[Chuẩn bị vòng tiếp theo]

    %% LOGIC VÒNG 2+ - Áp dụng nguyên tắc chung
    PrepareNextRound --> FilterQuestions[LỌC CÂU HỎI VÒNG TIẾP THEO:<br/><br/>✅ HIỆN: Câu CHƯA LÀM ở các vòng trước<br/>✅ HIỆN: Câu làm SAI LẦN 1 ở các vòng trước<br/>❌ LOẠI BỎ: Câu ĐÚNG ở bất kỳ vòng nào<br/>❌ LOẠI BỎ: Câu đã SAI 2+ LẦN]

    FilterQuestions --> CheckEligible{Còn câu đủ điều kiện?}
    CheckEligible -->|Có| StartNextRound[Bắt đầu vòng tiếp theo]
    CheckEligible -->|Không| QuizCompleted[Kết thúc quiz - Không còn câu]

    StartNextRound --> MarkQuestionStatus[Đánh dấu trạng thái câu hỏi:<br/>- Lần đầu làm<br/>- Đã sai 1 lần<br/>- Đã sai 2+ lần]
    MarkQuestionStatus --> NewQuestion

    %% KẾT THÚC VÀ TRAO THƯỞNG
    QuizCompleted --> ShowPodium[Hiển thị bục vinh quang Top 3]
    ShowPodium --> CalcFinalRewards[Tính toán phần thưởng cuối game]

    CalcFinalRewards --> GiveXP[Trao XP dựa trên:<br/>Điểm số tổng, Thứ hạng<br/>Chuỗi thắng dài nhất]
    GiveXP --> GiveSynCoin[Trao SynCoin dựa trên:<br/>Số câu đúng, Kỹ năng sử dụng<br/>Thành tích đặc biệt]
    GiveSynCoin --> CheckEggReward{Nhận được trứng?}

    CheckEggReward -->|Có| GiveEgg[Trao trứng thưởng:<br/>- Hoàn thành quiz<br/>- Đạt 100%<br/>- Chuỗi thắng dài<br/>- Thứ hạng cao]
    CheckEggReward -->|Không| NoEgg[Không có trứng]

    GiveEgg --> SaveProgress[Lưu tiến trình vào Database]
    NoEgg --> SaveProgress

    SaveProgress --> SyncData[Đồng bộ Firebase → PostgreSQL]
    SyncData --> ShowFinalResults[Hiển thị kết quả cuối:<br/>XP + SynCoin + Trứng + Thành tích nổi bật]
    ShowFinalResults --> End([Kết thúc - Quay về lobby])

    class PrepSkills,JoinRoom,ShowLoadout,WaitStart preparation
    class NewQuestion,StartTimer,PlayerChoice,Answer,SubmitAnswer gameplay
    class CalcScore,BasePoints,CalcSpeedBonus,CheckStreak,AddStreakBonus,UpdateStreak,CheckRoundForBonus,SkipBonus scoring
    class RandomSkill,ShowSkillReady,SelectTarget,ExecuteSkill,ApplyEffect,BroadcastSkill skills
    class UpdateLeaderboard,UpdateRaceUI,BroadcastEvent,CheckGlobalEvent realtime
    class CalcFinalRewards,GiveXP,GiveSynCoin,CheckEggReward,GiveEgg,ShowFinalResults rewards
    class FilterQuestions,CheckEligible,MarkQuestionStatus logic
```
