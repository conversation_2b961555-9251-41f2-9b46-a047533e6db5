{"info": {"_postman_id": "gamification-debug-test-2025", "name": "🎮 Gamification Debug & Test Collection", "description": "Collection để test và debug các vấn đề về Avatar, Title, Badge không đồng bộ trong hệ thống Synlearnia", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "🔐 Authentication", "item": [{"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.success && response.data.token) {", "        pm.environment.set('authToken', response.data.token);", "        pm.environment.set('userId', response.data.user.user_id);", "        console.log('✅ Login successful, token saved');", "        console.log('👤 User ID:', response.data.user.user_id);", "        console.log('📧 Email:', response.data.user.email);", "    }", "} else {", "    console.log('❌ <PERSON><PERSON> failed');", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/login", "host": ["{{baseUrl}}"], "path": ["api", "auth", "login"]}}, "response": []}]}, {"name": "🔍 Debug Current State", "item": [{"name": "1. Get User Gamification Info", "event": [{"listen": "test", "script": {"exec": ["const response = pm.response.json();", "if (response.success) {", "    console.log('✅ Gamification Info Retrieved');", "    console.log('📊 Level:', response.data.level_info.current_level);", "    console.log('🎯 Points:', response.data.level_info.total_points);", "    console.log('🏆 Tier:', response.data.level_info.tier_name);", "    console.log('👑 Titles:', response.data.title_stats.unlocked + '/' + response.data.title_stats.total_available);", "    console.log('🎖️ Badges:', response.data.badge_stats.unlocked + '/' + response.data.badge_stats.total_available);", "    ", "    if (response.data.title_stats.active_title) {", "        console.log('👑 Active Title:', response.data.title_stats.active_title.title_name);", "    } else {", "        console.log('❌ No active title');", "    }", "} else {", "    console.log('❌ Failed to get gamification info');", "}"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/gamification/me", "host": ["{{baseUrl}}"], "path": ["api", "gamification", "me"]}}, "response": []}, {"name": "2. Get Avatar Data", "event": [{"listen": "test", "script": {"exec": ["const response = pm.response.json();", "if (response.success) {", "    console.log('✅ Avatar Data Retrieved');", "    ", "    if (response.data.equipped_avatar) {", "        console.log('🎭 Equipped Avatar:', response.data.equipped_avatar.avatar_name);", "    } else {", "        console.log('❌ No avatar equipped');", "    }", "    ", "    if (response.data.equipped_frame) {", "        console.log('🖼️ Equipped Frame:', response.data.equipped_frame.frame_name);", "    } else {", "        console.log('❌ No frame equipped');", "    }", "    ", "    console.log('🎒 Avatar Inventory:', response.data.inventory.avatars.length + ' items');", "    console.log('🖼️ Frame Inventory:', response.data.inventory.frames.length + ' items');", "    console.log('😀 Emoji Inventory:', response.data.inventory.emojis.length + ' items');", "} else {", "    console.log('❌ Failed to get avatar data:', response.message);", "}"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/avatar/my-data", "host": ["{{baseUrl}}"], "path": ["api", "avatar", "my-data"]}}, "response": []}, {"name": "2.5. Test Available Items API", "event": [{"listen": "test", "script": {"exec": ["const response = pm.response.json();", "if (response.success) {", "    console.log('✅ Available Items Retrieved');", "    console.log('🎭 Available Avatars:', response.data.avatars.owned.length + ' owned, ' + response.data.avatars.unlockable.length + ' unlockable, ' + response.data.avatars.locked.length + ' locked');", "    console.log('🖼️ Available Frames:', response.data.frames.owned.length + ' owned, ' + response.data.frames.unlockable.length + ' unlockable, ' + response.data.frames.locked.length + ' locked');", "    console.log('✨ Available Name Effects:', response.data.name_effects.owned.length + ' owned, ' + response.data.name_effects.unlockable.length + ' unlockable, ' + response.data.name_effects.locked.length + ' locked');", "    console.log('😀 Available Emojis:', response.data.emojis.owned.length + ' owned, ' + response.data.emojis.unlockable.length + ' unlockable, ' + response.data.emojis.locked.length + ' locked');", "} else {", "    console.log('❌ Failed to get available items:', response.message);", "    console.log('Error:', response.error);", "}"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/avatar/available-items", "host": ["{{baseUrl}}"], "path": ["api", "avatar", "available-items"]}}, "response": []}, {"name": "3. Get Display Info", "event": [{"listen": "test", "script": {"exec": ["const response = pm.response.json();", "if (response.success) {", "    console.log('✅ Display Info Retrieved');", "    console.log('👤 User:', response.data.user_name);", "    ", "    if (response.data.avatar) {", "        console.log('🎭 Display Avatar:', response.data.avatar.avatar_name);", "        console.log('📁 Image Path:', response.data.avatar.image_path);", "    } else {", "        console.log('❌ No avatar in display info');", "    }", "    ", "    if (response.data.frame) {", "        console.log('🖼️ Display Frame:', response.data.frame.frame_name);", "    } else {", "        console.log('❌ No frame in display info');", "    }", "} else {", "    console.log('❌ Failed to get display info:', response.message);", "}"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/avatar/display-info", "host": ["{{baseUrl}}"], "path": ["api", "avatar", "display-info"]}}, "response": []}, {"name": "4. Get Active Title", "event": [{"listen": "test", "script": {"exec": ["const response = pm.response.json();", "if (response.success) {", "    if (response.data.active_title) {", "        console.log('✅ Active Title Found');", "        console.log('👑 Title:', response.data.active_title.Title.title_name);", "        console.log('🏆 Tier:', response.data.active_title.Title.tier_name);", "        console.log('🎨 Color:', response.data.active_title.Title.color);", "    } else {", "        console.log('❌ No active title found');", "    }", "} else {", "    console.log('❌ Failed to get active title:', response.message);", "}"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/titles/my-active-title", "host": ["{{baseUrl}}"], "path": ["api", "titles", "my-active-title"]}}, "response": []}, {"name": "5. Get User Titles", "event": [{"listen": "test", "script": {"exec": ["const response = pm.response.json();", "if (response.success) {", "    console.log('✅ User Titles Retrieved');", "    console.log('👑 Total Titles:', response.data.user_titles.length);", "    console.log('📊 Completion Rate:', response.data.stats.completion_rate + '%');", "    ", "    response.data.user_titles.forEach((userTitle, index) => {", "        const active = userTitle.is_active ? ' (ACTIVE)' : '';", "        console.log(`  ${index + 1}. ${userTitle.Title.title_name}${active}`);", "    });", "} else {", "    console.log('❌ Failed to get user titles:', response.message);", "}"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/titles/my-titles", "host": ["{{baseUrl}}"], "path": ["api", "titles", "my-titles"]}}, "response": []}, {"name": "6. <PERSON> User Badges", "event": [{"listen": "test", "script": {"exec": ["const response = pm.response.json();", "if (response.success) {", "    console.log('✅ User Badges Retrieved');", "    console.log('🎖️ Total Badges:', response.data.user_badges.length);", "    console.log('📊 Completion Rate:', response.data.stats.completion_rate + '%');", "    ", "    const rarityCount = {};", "    response.data.user_badges.forEach((userBadge) => {", "        const rarity = userBadge.Badge.rarity;", "        rarityCount[rarity] = (rarityCount[rarity] || 0) + 1;", "    });", "    ", "    console.log('🏆 Badges by <PERSON><PERSON>:');", "    Object.keys(rarityCount).forEach(rarity => {", "        console.log(`  ${rarity}: ${rarityCount[rarity]}`);", "    });", "} else {", "    console.log('❌ Failed to get user badges:', response.message);", "}"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/titles/my-badges", "host": ["{{baseUrl}}"], "path": ["api", "titles", "my-badges"]}}, "response": []}]}, {"name": "🔧 Fix Issues", "item": [{"name": "1. Initialize Avatar System", "event": [{"listen": "test", "script": {"exec": ["const response = pm.response.json();", "if (response.success) {", "    console.log('✅ Avatar System Initialized');", "    console.log('🎭 Default Avatars:', response.data.default_avatars_count);", "    console.log('🖼️ Default Frame:', response.data.default_frame);", "    console.log('😀 Default Emojis:', response.data.default_emojis_count);", "    console.log('👤 Equipped Avatar:', response.data.equipped_avatar);", "} else {", "    console.log('❌ Failed to initialize avatar system:', response.message);", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/avatar/initialize", "host": ["{{baseUrl}}"], "path": ["api", "avatar", "initialize"]}}, "response": []}, {"name": "2. Sync Gamification", "event": [{"listen": "test", "script": {"exec": ["const response = pm.response.json();", "if (response.success) {", "    console.log('✅ Gamification Synced Successfully');", "    console.log('📊 User Level:', response.data.user_level);", "    console.log('🎯 Total Points:', response.data.total_points);", "    console.log('👑 New Titles:', response.data.titles_unlocked);", "    console.log('🎖️ New Badges:', response.data.badges_unlocked);", "    ", "    if (response.data.new_titles && response.data.new_titles.length > 0) {", "        console.log('🆕 Unlocked Titles:');", "        response.data.new_titles.forEach(title => {", "            console.log(`  - ${title.Title.title_name}`);", "        });", "    }", "    ", "    if (response.data.new_badges && response.data.new_badges.length > 0) {", "        console.log('🆕 Unlocked Badges:');", "        response.data.new_badges.forEach(badge => {", "            console.log(`  - ${badge.Badge.badge_name}`);", "        });", "    }", "} else {", "    console.log('❌ Failed to sync gamification:', response.message);", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/gamification/sync-gamification", "host": ["{{baseUrl}}"], "path": ["api", "gamification", "sync-gamification"]}}, "response": []}]}, {"name": "🧪 Additional Tests", "item": [{"name": "Add Points (Test Level Up)", "event": [{"listen": "test", "script": {"exec": ["const response = pm.response.json();", "if (response.success) {", "    console.log('✅ Points Added Successfully');", "    console.log('🎯 New Total Points:', response.data.new_total_points);", "    console.log('📊 New Level:', response.data.new_level);", "    console.log('🆙 Level Up:', response.data.level_up ? 'YES' : 'NO');", "    ", "    if (response.data.level_up) {", "        console.log('🎉 LEVEL UP DETECTED!');", "        console.log('👑 New Titles Unlocked:', response.data.new_titles_unlocked || 0);", "        console.log('🎖️ New Badges Unlocked:', response.data.new_badges_unlocked || 0);", "    }", "} else {", "    console.log('❌ Failed to add points:', response.message);", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"points\": 100,\n    \"reason\": \"Testing level up and sync\"\n}"}, "url": {"raw": "{{baseUrl}}/api/gamification/add-points", "host": ["{{baseUrl}}"], "path": ["api", "gamification", "add-points"]}}, "response": []}, {"name": "Get Leaderboard", "event": [{"listen": "test", "script": {"exec": ["const response = pm.response.json();", "if (response.success) {", "    console.log('✅ Leaderboard Retrieved');", "    console.log('🏆 Top Players:');", "    response.data.leaderboard.forEach((player, index) => {", "        const rank = index + 1;", "        const medal = rank === 1 ? '🥇' : rank === 2 ? '🥈' : rank === 3 ? '🥉' : '🏅';", "        console.log(`${medal} ${rank}. ${player.name} - Level ${player.current_level} (${player.total_points} pts)`);", "    });", "} else {", "    console.log('❌ Failed to get leaderboard:', response.message);", "}"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/gamification/leaderboard?limit=10", "host": ["{{baseUrl}}"], "path": ["api", "gamification", "leaderboard"], "query": [{"key": "limit", "value": "10"}]}}, "response": []}]}], "event": [{"listen": "prerequest", "script": {"id": "global-prerequest", "exec": ["// Global pre-request script", "console.log('🚀 Starting request to:', pm.request.url.toString());"], "type": "text/javascript"}}], "variable": [{"key": "baseUrl", "value": "http://localhost:8888", "type": "string"}, {"key": "authToken", "value": "", "type": "string"}, {"key": "userId", "value": "", "type": "string"}, {"key": "firstTitleId", "value": "", "type": "string"}, {"key": "firstAvatarId", "value": "", "type": "string"}]}