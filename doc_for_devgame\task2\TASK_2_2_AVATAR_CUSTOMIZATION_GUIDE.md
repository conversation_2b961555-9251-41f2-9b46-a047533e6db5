# Task 2.2: Avatar & Customization System - Implementation Guide

## Tổng Quan
Task 2.2 triển khai hệ thống Avatar & Customization hoàn chỉnh cho Synlearnia, bao gồm 30 avatar động vật, khung avatar, hi<PERSON><PERSON> <PERSON><PERSON> tên, v<PERSON> hệ thống emoji cho tương tác xã hội.

## Cấu Trúc Database

### Tables Chính
1. **Avatars** - 30 avatar động vật với các cơ chế mở khóa khác nhau
2. **AvatarFrames** - Khung trang trí đại diện cho thành tích và rank
3. **NameEffects** - Hiệu ứng CSS cho tên người chơi ở tier cao
4. **Emojis** - Hệ thống emoji cho tương tác xã hội
5. **UserInventory** - Quản lý vật phẩm của người dùng
6. **UserCustomization** - <PERSON><PERSON><PERSON><PERSON> lý trang bị hiện tại của người dùng

### Views & Functions
- **UserAvatarStats** - Thống kê avatar của người dùng
- **CollectionProgress** - Tiến độ sưu tập
- **get_user_equipped_items()** - Lấy vật phẩm đang trang bị
- **initialize_user_avatar_system()** - Khởi tạo hệ thống cho user mới

## Cơ Chế Mở Khóa

### Avatar System (30 Avatars)
- **DEFAULT**: Avatar mặc định (Chó, Mèo, Thỏ)
- **LEVEL**: Mở khóa theo level (Level 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 60, 70, 80, 90, 100)
- **EGG**: Từ trứng thưởng (Rồng, Phượng Hoàng, Kỳ Lân)
- **SHOP**: Mua bằng currency
- **ACHIEVEMENT**: Từ thành tích đặc biệt
- **SPECIAL**: Sự kiện đặc biệt

### Frame System
- **DEFAULT**: Khung cơ bản
- **TIER**: Theo tier (Wood, Bronze, Silver, Gold, Platinum, Onyx, Sapphire, Ruby, Amethyst, Master)
- **EGG**: Khung đặc biệt từ trứng
- **ACHIEVEMENT**: Khung thành tích

### Name Effects
- **Onyx Tier** (Level 61-70): Hiệu ứng vàng
- **Sapphire Tier** (Level 71-80): Hiệu ứng xanh dương
- **Ruby Tier** (Level 81-90): Hiệu ứng đỏ
- **Amethyst Tier** (Level 91-100): Hiệu ứng tím
- **Master Tier** (Level 101+): Hiệu ứng cầu vồng

### Emoji System
- **Categories**: GENERAL, HAPPY, SAD, ANGRY, SURPRISED, LOVE, CELEBRATION, ANIMALS, SPECIAL
- **Unlock**: Level-based và egg-based

## Hệ Thống Rarity

### Độ Hiếm
- **COMMON**: Vật phẩm cơ bản, dễ có
- **UNCOMMON**: Hơi hiếm
- **RARE**: Hiếm, điều kiện mở khóa đặc biệt
- **EPIC**: Rất hiếm, thường từ trứng hoặc level cao
- **LEGENDARY**: Cực hiếm, phần thưởng tier cao nhất

### Decomposition Values
- **COMMON**: 5 Kristal
- **UNCOMMON**: 10 Kristal
- **RARE**: 25 Kristal
- **EPIC**: 50 Kristal
- **LEGENDARY**: 100 Kristal

## API Endpoints

### User Management
- `POST /api/avatar/initialize` - Khởi tạo hệ thống avatar cho user mới
- `GET /api/avatar/my-data` - Lấy dữ liệu avatar hoàn chỉnh của user

### Inventory Management
- `GET /api/avatar/available-items` - Lấy items có thể mở khóa
- `GET /api/avatar/inventory/:itemType` - Lấy inventory theo loại
- `GET /api/avatar/collection-progress` - Tiến độ sưu tập

### Customization
- `POST /api/avatar/equip` - Trang bị item
- `POST /api/avatar/unequip` - Gỡ trang bị
- `GET /api/avatar/customization` - Lấy cài đặt tùy chỉnh
- `PUT /api/avatar/customization` - Cập nhật cài đặt

### Social Features
- `GET /api/avatar/display-info/:userId?` - Thông tin hiển thị cho leaderboard

### Browsing
- `GET /api/avatar/avatars` - Duyệt tất cả avatars
- `GET /api/avatar/frames` - Duyệt tất cả frames
- `GET /api/avatar/emojis` - Duyệt tất cả emojis

## Tích Hợp Với Hệ Thống Khác

### Level System Integration
- Tự động mở khóa items khi user lên level
- Tích hợp trong `User.addPoints()` method
- Trả về `new_avatar_items` trong response

### Currency System Integration
- Mua items bằng SynCoin/Kristal
- Decomposition system cho duplicate items
- Tích hợp với shop system

### Achievement System Integration
- Mở khóa items đặc biệt qua achievements
- Badge tracking cho collection milestones

## Models & Services

### Models
- `Avatar` - Quản lý 30 avatar động vật
- `AvatarFrame` - Quản lý khung avatar
- `NameEffect` - Quản lý hiệu ứng tên
- `Emoji` - Quản lý emoji system
- `UserInventory` - Quản lý inventory của user
- `UserCustomization` - Quản lý trang bị hiện tại

### Services
- `AvatarCustomizationService` - Business logic chính
  - `initializeUserAvatarSystem()` - Khởi tạo cho user mới
  - `getUserAvatarData()` - Lấy dữ liệu hoàn chỉnh
  - `getAvailableItems()` - Items có thể mở khóa
  - `unlockItemsByLevel()` - Mở khóa theo level
  - `equipItem()` / `unequipItem()` - Trang bị/gỡ trang bị
  - `getCollectionProgress()` - Tiến độ sưu tập

### Controllers
- `AvatarCustomizationController` - API endpoints
  - Authentication required cho tất cả endpoints
  - Error handling và validation
  - Pagination cho browsing endpoints

## Cách Sử Dụng

### 1. Khởi Tạo Cho User Mới
```javascript
POST /api/avatar/initialize
// Tự động thêm default avatars, frames, emojis vào inventory
// Trang bị avatar và frame đầu tiên
```

### 2. Lấy Dữ Liệu Avatar
```javascript
GET /api/avatar/my-data
// Trả về: customization, inventory, statistics, user_level, user_tier
```

### 3. Trang Bị Avatar
```javascript
POST /api/avatar/equip
{
  "itemType": "avatar",
  "itemId": 5
}
```

### 4. Xem Items Có Thể Mở Khóa
```javascript
GET /api/avatar/available-items
// Trả về: owned, unlockable, locked items theo từng category
```

### 5. Theo Dõi Tiến Độ Sưu Tập
```javascript
GET /api/avatar/collection-progress
// Trả về: percentage completion cho từng loại item
```

## Testing

### Postman Collection
1. **Initialize System**: POST `/api/avatar/initialize`
2. **Get User Data**: GET `/api/avatar/my-data`
3. **Browse Avatars**: GET `/api/avatar/avatars`
4. **Equip Avatar**: POST `/api/avatar/equip`
5. **Check Progress**: GET `/api/avatar/collection-progress`

### Test Scenarios
1. User mới khởi tạo hệ thống
2. Level up tự động unlock items
3. Trang bị/gỡ trang bị items
4. Browse và filter items
5. Collection progress tracking

## Lưu Ý Kỹ Thuật

### Performance
- Indexes trên user_id, item_type, is_equipped
- Pagination cho browsing endpoints
- Efficient queries với proper includes

### Security
- Authentication required cho tất cả endpoints
- Validation ownership trước khi equip
- Proper error handling

### Scalability
- JSONB cho flexible metadata storage
- Polymorphic associations cho UserInventory
- Modular service architecture

## Kết Luận
Task 2.2 hoàn thành hệ thống Avatar & Customization với đầy đủ tính năng:
- 30 avatar động vật với unlock mechanisms đa dạng
- Hệ thống khung và hiệu ứng tên theo tier
- Emoji system cho tương tác xã hội
- Inventory management hoàn chỉnh
- Tích hợp với level và currency systems
- API endpoints đầy đủ cho frontend integration
