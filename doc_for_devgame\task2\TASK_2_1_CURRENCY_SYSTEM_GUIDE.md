# Task 2.1: <PERSON><PERSON> thống tiền tệ (SynCoin & Kristal) - Implementation Guide

## 📋 Tổng quan

Task 2.1 triển khai hệ thống tiền tệ hai cấp cho Synlearnia vớ<PERSON>yn<PERSON> (ti<PERSON><PERSON> tệ cơ bản) v<PERSON> (tiề<PERSON> tệ cao cấp). <PERSON><PERSON> thống bao gồm cơ chế kiếm tiền từ các hoạt động, gi<PERSON><PERSON> hạn hàng ng<PERSON>, lịch sử giao dịch và tích hợp với quiz completion.

## 🏗️ Kiến trúc hệ thống

### Database Schema
- **Currencies**: <PERSON><PERSON><PERSON> nghĩa các loại tiền tệ
- **UserCurrencies**: Số dư tiền tệ của user
- **CurrencyTransactions**: Lịch sử giao dịch chi tiết
- **CurrencyEarningRules**: <PERSON><PERSON> tắc kiếm tiề<PERSON> từ cá<PERSON> ho<PERSON> độ<PERSON>
- **ItemDecomposition**: <PERSON><PERSON><PERSON>r<PERSON> phân gi<PERSON>i vật phẩm thành <PERSON>

### Models
- **Currency**: Model cho loại tiền tệ
- **UserCurrency**: Model cho số dư user
- **CurrencyTransaction**: Model cho giao dịch

### Services
- **CurrencyService**: Logic nghiệp vụ chính

### Controllers & Routes
- **CurrencyController**: API endpoints
- **currencyRoutes**: Định tuyến API

## 💰 Loại tiền tệ

### SynCoin (SYNC)
- **Mô tả**: Tiền tệ cơ bản, dễ kiếm
- **Nguồn kiếm**: Quiz completion, daily login, achievements, level up
- **Giới hạn**: 1000 SynCoin/ngày
- **Sử dụng**: Mua eggs cơ bản, emojis thông thường

### Kristal (KRIS)
- **Mô tả**: Tiền tệ cao cấp, khó kiếm hơn
- **Nguồn kiếm**: Item decomposition, title unlock, leaderboard rewards, perfect quiz streaks
- **Giới hạn**: 100 Kristal/ngày
- **Sử dụng**: Mua items hiếm, eggs cao cấp
- **Tỷ giá**: 1 Kristal = 10 SynCoin

## 🎯 Cơ chế kiếm tiền

### Quiz Completion Rewards
```javascript
// SynCoin calculation
let synCoinAmount = 10; // Base amount
synCoinAmount += correct_answers * 2; // +2 per correct answer
if (is_perfect_score) synCoinAmount += 20; // Perfect score bonus
if (has_speed_bonus) synCoinAmount += 5; // Speed bonus

// Kristal for perfect scores
if (is_perfect_score) {
    kristalAmount = 5; // Perfect quiz streak bonus
}
```

### Daily Login Bonus
```javascript
let synCoinAmount = 50; // Base daily bonus
if (consecutiveDays >= 30) synCoinAmount += 50;
else if (consecutiveDays >= 7) synCoinAmount += 25;
else if (consecutiveDays >= 3) synCoinAmount += 10;
```

### Achievement & Level Up
- **Achievement unlock**: 25 SynCoin + rarity multiplier
- **Level up**: 100 SynCoin + level multiplier
- **Title unlock**: 10 Kristal + tier multiplier

## 🔧 API Endpoints

### Public Endpoints (Authenticated Users)

#### Get User Balances
```http
GET /api/currency/balance
```
**Response:**
```json
{
  "success": true,
  "data": {
    "user_id": 1,
    "currencies": {
      "SYNC": {
        "balance": 1500,
        "total_earned": 2000,
        "total_spent": 500,
        "daily_earned_today": 150
      },
      "KRIS": {
        "balance": 50,
        "total_earned": 75,
        "total_spent": 25,
        "daily_earned_today": 5
      }
    },
    "total_wealth_in_syncoin": 2000
  }
}
```

#### Get Transaction History
```http
GET /api/currency/transactions?currency_code=SYNC&limit=20
```

#### Spend Currency
```http
POST /api/currency/spend
Content-Type: application/json

{
  "currency_code": "SYNC",
  "amount": 100,
  "source_type": "SHOP_PURCHASE",
  "source_id": 1,
  "description": "Mua trứng cơ bản"
}
```

#### Daily Login Bonus
```http
POST /api/currency/daily-login
Content-Type: application/json

{
  "consecutive_days": 5
}
```

### Admin Endpoints

#### Award Currency
```http
POST /api/currency/award
Content-Type: application/json

{
  "user_id": 123,
  "currency_code": "KRIS",
  "amount": 50,
  "description": "Thưởng đặc biệt từ admin"
}
```

### Analytics Endpoints

#### Currency Statistics
```http
GET /api/currency/stats
```

#### Wealth Leaderboard
```http
GET /api/currency/leaderboard?limit=50
```

#### Top Earning Sources
```http
GET /api/currency/earning-sources?currency_code=SYNC
```

## 🔄 Tích hợp với Quiz System

### Automatic Currency Awards
Hệ thống tự động thưởng tiền khi user hoàn thành quiz thông qua `quizResultController.js`:

```javascript
// Trong quizResultController.js
const quizCompletionData = {
    quiz_id: quiz_id,
    correct_answers: correct_answers || 0,
    total_questions: total_questions || 0,
    is_perfect_score: score === 100,
    average_response_time: average_response_time || 0,
    has_speed_bonus: average_response_time && average_response_time < 5000
};

CurrencyService.awardQuizCompletionCurrency(user_id, quizCompletionData);
```

### Integration Points
1. **Quiz Completion**: Tự động thưởng SynCoin + Kristal (nếu perfect score)
2. **Achievement System**: Thưởng tiền khi unlock achievements
3. **Level System**: Thưởng tiền khi lên level
4. **Daily Login**: Manual trigger cho daily bonus

## 📊 Database Views

### UserCurrencyStats
Tổng hợp thống kê tiền tệ của user:
```sql
SELECT 
    u.user_id,
    u.name,
    uc_sync.balance as syncoin_balance,
    uc_kris.balance as kristal_balance,
    (uc_sync.balance + (uc_kris.balance * 10)) as total_wealth_in_syncoin
FROM "Users" u
LEFT JOIN "UserCurrencies" uc_sync ON ...
LEFT JOIN "UserCurrencies" uc_kris ON ...
```

### WealthLeaderboard
Bảng xếp hạng giàu có:
```sql
SELECT 
    u.user_id,
    u.name,
    uc_sync.balance as syncoin,
    uc_kris.balance as kristal,
    (uc_sync.balance + (uc_kris.balance * 10)) as total_wealth,
    RANK() OVER (ORDER BY total_wealth DESC) as wealth_rank
FROM "Users" u ...
```

## 🚀 Deployment Instructions

### 1. Run Database Migration
```bash
psql -U your_username -d your_database -f doc_for_devgame/task2/gamification_currency_system.sql
```

### 2. Verify Tables Created
```sql
-- Check if tables exist
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('Currencies', 'UserCurrencies', 'CurrencyTransactions', 'CurrencyEarningRules', 'ItemDecomposition');

-- Check initial data
SELECT * FROM "Currencies";
SELECT * FROM "CurrencyEarningRules";
```

### 3. Test API Endpoints
```bash
# Initialize user currencies
curl -X POST http://localhost:3000/api/currency/initialize \
  -H "Authorization: Bearer YOUR_TOKEN"

# Get user balance
curl -X GET http://localhost:3000/api/currency/balance \
  -H "Authorization: Bearer YOUR_TOKEN"

# Award daily login bonus
curl -X POST http://localhost:3000/api/currency/daily-login \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"consecutive_days": 1}'
```

## 🔍 Testing Scenarios

### 1. Quiz Completion Flow
1. User completes a quiz with perfect score
2. System automatically awards SynCoin + Kristal
3. Check transaction history
4. Verify daily limits

### 2. Daily Login Bonus
1. Call daily login endpoint
2. Check consecutive days bonus calculation
3. Verify daily limit enforcement

### 3. Currency Spending
1. Try to spend more than balance (should fail)
2. Spend valid amount
3. Check transaction record
4. Verify balance update

### 4. Admin Functions
1. Admin awards currency to user
2. Check transaction with admin metadata
3. Verify user balance update

## 📈 Performance Considerations

### Indexes
- User-based queries: `(user_id, currency_id)`
- Transaction history: `(user_id, created_at)`
- Leaderboard: `(balance)` for each currency

### Caching Opportunities
- User balances (frequently accessed)
- Currency definitions (rarely change)
- Daily earning rules (static data)

### Async Processing
- Currency awards are processed asynchronously
- Analytics and leaderboard updates can be batched

## 🔐 Security Features

### Daily Limits
- Automatic enforcement of daily earning limits
- Prevents exploitation of earning mechanisms

### Transaction Logging
- Complete audit trail of all currency movements
- Metadata tracking for admin actions

### Role-based Access
- Admin-only endpoints for manual currency awards
- User isolation (can only access own data)

## 🎯 Next Steps

Task 2.1 hoàn thành cung cấp nền tảng vững chắc cho:
- **Task 2.2**: Avatar & Customization system (sử dụng currency để mua items)
- **Task 2.3**: Egg reward system (sử dụng currency để mua eggs)
- **Phase 3**: Shop system và item decomposition

Hệ thống currency đã sẵn sàng để tích hợp với các tính năng gamification khác và cung cấp động lực kinh tế cho toàn bộ hệ thống game.
