-- =====================================================
-- GAMIFICATION AVATAR & CUSTOMIZATION SYSTEM FOR SYNLEARNIA
-- Task 2.2: Hệ thống Avatar & Customization
-- =====================================================

-- Bắt đầu transaction
BEGIN;

-- =====================================================
-- 1. BẢNG AVATARS - Định nghĩa các avatar động vật
-- =====================================================

CREATE TABLE IF NOT EXISTS "Avatars" (
    "avatar_id" SERIAL PRIMARY KEY,
    "avatar_name" VARCHAR(50) NOT NULL,
    "avatar_code" VARCHAR(20) NOT NULL UNIQUE,
    "description" TEXT,
    "image_path" VARCHAR(255) NOT NULL,
    "rarity" VARCHAR(20) NOT NULL DEFAULT 'COMMON',
    "unlock_type" VARCHAR(30) NOT NULL DEFAULT 'LEVEL',
    "unlock_condition" JSONB NOT NULL DEFAULT '{}',
    "is_default" BOOLEAN NOT NULL DEFAULT FALSE,
    "is_active" BOOLEAN NOT NULL DEFAULT TRUE,
    "sort_order" INTEGER NOT NULL DEFAULT 0,
    "created_at" TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT "avatars_rarity_check" CHECK ("rarity" IN ('COMMON', 'UNCOMMON', 'RARE', 'EPIC', 'LEGENDARY')),
    CONSTRAINT "avatars_unlock_type_check" CHECK ("unlock_type" IN ('DEFAULT', 'LEVEL', 'EGG', 'SHOP', 'ACHIEVEMENT', 'SPECIAL'))
);

-- =====================================================
-- 2. BẢNG AVATAR_FRAMES - Khung viền avatar
-- =====================================================

CREATE TABLE IF NOT EXISTS "AvatarFrames" (
    "frame_id" SERIAL PRIMARY KEY,
    "frame_name" VARCHAR(50) NOT NULL,
    "frame_code" VARCHAR(20) NOT NULL UNIQUE,
    "description" TEXT,
    "image_path" VARCHAR(255) NOT NULL,
    "rarity" VARCHAR(20) NOT NULL DEFAULT 'COMMON',
    "unlock_type" VARCHAR(30) NOT NULL DEFAULT 'TIER',
    "unlock_condition" JSONB NOT NULL DEFAULT '{}',
    "tier_name" VARCHAR(20),
    "is_default" BOOLEAN NOT NULL DEFAULT FALSE,
    "is_active" BOOLEAN NOT NULL DEFAULT TRUE,
    "sort_order" INTEGER NOT NULL DEFAULT 0,
    "created_at" TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT "frames_rarity_check" CHECK ("rarity" IN ('COMMON', 'UNCOMMON', 'RARE', 'EPIC', 'LEGENDARY')),
    CONSTRAINT "frames_unlock_type_check" CHECK ("unlock_type" IN ('DEFAULT', 'TIER', 'EGG', 'SHOP', 'ACHIEVEMENT', 'SPECIAL'))
);

-- =====================================================
-- 3. BẢNG NAME_EFFECTS - Hiệu ứng tên
-- =====================================================

CREATE TABLE IF NOT EXISTS "NameEffects" (
    "effect_id" SERIAL PRIMARY KEY,
    "effect_name" VARCHAR(50) NOT NULL,
    "effect_code" VARCHAR(20) NOT NULL UNIQUE,
    "description" TEXT,
    "css_class" VARCHAR(100),
    "css_style" TEXT,
    "tier_name" VARCHAR(20) NOT NULL,
    "unlock_level" INTEGER NOT NULL,
    "is_active" BOOLEAN NOT NULL DEFAULT TRUE,
    "sort_order" INTEGER NOT NULL DEFAULT 0,
    "created_at" TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 4. BẢNG EMOJIS - Biểu tượng cảm xúc
-- =====================================================

CREATE TABLE IF NOT EXISTS "Emojis" (
    "emoji_id" SERIAL PRIMARY KEY,
    "emoji_name" VARCHAR(50) NOT NULL,
    "emoji_code" VARCHAR(20) NOT NULL UNIQUE,
    "emoji_unicode" VARCHAR(20),
    "emoji_image" VARCHAR(255),
    "description" TEXT,
    "rarity" VARCHAR(20) NOT NULL DEFAULT 'COMMON',
    "unlock_type" VARCHAR(30) NOT NULL DEFAULT 'DEFAULT',
    "unlock_condition" JSONB NOT NULL DEFAULT '{}',
    "category" VARCHAR(30) NOT NULL DEFAULT 'GENERAL',
    "is_default" BOOLEAN NOT NULL DEFAULT FALSE,
    "is_active" BOOLEAN NOT NULL DEFAULT TRUE,
    "sort_order" INTEGER NOT NULL DEFAULT 0,
    "created_at" TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT "emojis_rarity_check" CHECK ("rarity" IN ('COMMON', 'UNCOMMON', 'RARE', 'EPIC', 'LEGENDARY')),
    CONSTRAINT "emojis_unlock_type_check" CHECK ("unlock_type" IN ('DEFAULT', 'LEVEL', 'EGG', 'SHOP', 'ACHIEVEMENT', 'SPECIAL')),
    CONSTRAINT "emojis_category_check" CHECK ("category" IN ('GENERAL', 'HAPPY', 'SAD', 'ANGRY', 'SURPRISED', 'LOVE', 'CELEBRATION', 'ANIMALS', 'SPECIAL'))
);

-- =====================================================
-- 5. BẢNG USER_INVENTORY - Kho đồ của user
-- =====================================================

CREATE TABLE IF NOT EXISTS "UserInventory" (
    "inventory_id" SERIAL PRIMARY KEY,
    "user_id" INTEGER NOT NULL,
    "item_type" VARCHAR(20) NOT NULL,
    "item_id" INTEGER NOT NULL,
    "quantity" INTEGER NOT NULL DEFAULT 1,
    "obtained_from" VARCHAR(30) NOT NULL DEFAULT 'UNKNOWN',
    "obtained_at" TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    "is_equipped" BOOLEAN NOT NULL DEFAULT FALSE,
    "metadata" JSONB DEFAULT '{}',
    
    FOREIGN KEY ("user_id") REFERENCES "Users"("user_id") ON DELETE CASCADE,
    CONSTRAINT "inventory_item_type_check" CHECK ("item_type" IN ('AVATAR', 'FRAME', 'EMOJI', 'NAME_EFFECT')),
    CONSTRAINT "inventory_obtained_from_check" CHECK ("obtained_from" IN ('DEFAULT', 'LEVEL_UP', 'EGG', 'SHOP', 'ACHIEVEMENT', 'ADMIN', 'TIER_UNLOCK', 'SPECIAL')),
    CONSTRAINT "inventory_quantity_positive" CHECK ("quantity" > 0),
    
    -- Unique constraint để tránh duplicate items
    UNIQUE ("user_id", "item_type", "item_id")
);

-- =====================================================
-- 6. BẢNG USER_CUSTOMIZATION - Cấu hình hiển thị của user
-- =====================================================

CREATE TABLE IF NOT EXISTS "UserCustomization" (
    "customization_id" SERIAL PRIMARY KEY,
    "user_id" INTEGER NOT NULL UNIQUE,
    "equipped_avatar_id" INTEGER,
    "equipped_frame_id" INTEGER,
    "equipped_name_effect_id" INTEGER,
    "customization_settings" JSONB DEFAULT '{}',
    "last_updated" TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY ("user_id") REFERENCES "Users"("user_id") ON DELETE CASCADE,
    FOREIGN KEY ("equipped_avatar_id") REFERENCES "Avatars"("avatar_id") ON DELETE SET NULL,
    FOREIGN KEY ("equipped_frame_id") REFERENCES "AvatarFrames"("frame_id") ON DELETE SET NULL,
    FOREIGN KEY ("equipped_name_effect_id") REFERENCES "NameEffects"("effect_id") ON DELETE SET NULL
);

-- =====================================================
-- 7. INDEXES cho hiệu suất
-- =====================================================

-- Avatars indexes
CREATE INDEX IF NOT EXISTS "idx_avatars_rarity" ON "Avatars"("rarity");
CREATE INDEX IF NOT EXISTS "idx_avatars_unlock_type" ON "Avatars"("unlock_type");
CREATE INDEX IF NOT EXISTS "idx_avatars_active" ON "Avatars"("is_active");
CREATE INDEX IF NOT EXISTS "idx_avatars_default" ON "Avatars"("is_default");

-- Avatar Frames indexes
CREATE INDEX IF NOT EXISTS "idx_frames_rarity" ON "AvatarFrames"("rarity");
CREATE INDEX IF NOT EXISTS "idx_frames_unlock_type" ON "AvatarFrames"("unlock_type");
CREATE INDEX IF NOT EXISTS "idx_frames_tier" ON "AvatarFrames"("tier_name");
CREATE INDEX IF NOT EXISTS "idx_frames_active" ON "AvatarFrames"("is_active");

-- Name Effects indexes
CREATE INDEX IF NOT EXISTS "idx_name_effects_tier" ON "NameEffects"("tier_name");
CREATE INDEX IF NOT EXISTS "idx_name_effects_level" ON "NameEffects"("unlock_level");
CREATE INDEX IF NOT EXISTS "idx_name_effects_active" ON "NameEffects"("is_active");

-- Emojis indexes
CREATE INDEX IF NOT EXISTS "idx_emojis_rarity" ON "Emojis"("rarity");
CREATE INDEX IF NOT EXISTS "idx_emojis_category" ON "Emojis"("category");
CREATE INDEX IF NOT EXISTS "idx_emojis_unlock_type" ON "Emojis"("unlock_type");
CREATE INDEX IF NOT EXISTS "idx_emojis_active" ON "Emojis"("is_active");

-- User Inventory indexes
CREATE INDEX IF NOT EXISTS "idx_inventory_user_id" ON "UserInventory"("user_id");
CREATE INDEX IF NOT EXISTS "idx_inventory_item_type" ON "UserInventory"("item_type");
CREATE INDEX IF NOT EXISTS "idx_inventory_equipped" ON "UserInventory"("is_equipped");
CREATE INDEX IF NOT EXISTS "idx_inventory_user_type" ON "UserInventory"("user_id", "item_type");
CREATE INDEX IF NOT EXISTS "idx_inventory_obtained_from" ON "UserInventory"("obtained_from");

-- User Customization indexes
CREATE INDEX IF NOT EXISTS "idx_customization_user_id" ON "UserCustomization"("user_id");
CREATE INDEX IF NOT EXISTS "idx_customization_avatar" ON "UserCustomization"("equipped_avatar_id");
CREATE INDEX IF NOT EXISTS "idx_customization_frame" ON "UserCustomization"("equipped_frame_id");

-- =====================================================
-- 8. VIEWS cho analytics và leaderboard
-- =====================================================

-- View: User Avatar Stats
CREATE OR REPLACE VIEW "UserAvatarStats" AS
SELECT 
    u.user_id,
    u.name,
    uc.equipped_avatar_id,
    uc.equipped_frame_id,
    uc.equipped_name_effect_id,
    a.avatar_name,
    a.rarity as avatar_rarity,
    af.frame_name,
    af.rarity as frame_rarity,
    ne.effect_name,
    ne.tier_name as effect_tier,
    COUNT(CASE WHEN ui.item_type = 'AVATAR' THEN 1 END) as total_avatars,
    COUNT(CASE WHEN ui.item_type = 'FRAME' THEN 1 END) as total_frames,
    COUNT(CASE WHEN ui.item_type = 'EMOJI' THEN 1 END) as total_emojis,
    COUNT(ui.inventory_id) as total_items
FROM "Users" u
LEFT JOIN "UserCustomization" uc ON u.user_id = uc.user_id
LEFT JOIN "Avatars" a ON uc.equipped_avatar_id = a.avatar_id
LEFT JOIN "AvatarFrames" af ON uc.equipped_frame_id = af.frame_id
LEFT JOIN "NameEffects" ne ON uc.equipped_name_effect_id = ne.effect_id
LEFT JOIN "UserInventory" ui ON u.user_id = ui.user_id
GROUP BY u.user_id, u.name, uc.equipped_avatar_id, uc.equipped_frame_id, 
         uc.equipped_name_effect_id, a.avatar_name, a.rarity, af.frame_name, 
         af.rarity, ne.effect_name, ne.tier_name;

-- View: Collection Progress
CREATE OR REPLACE VIEW "CollectionProgress" AS
SELECT 
    u.user_id,
    u.name,
    -- Avatar collection
    COUNT(CASE WHEN ui.item_type = 'AVATAR' THEN 1 END) as avatars_owned,
    (SELECT COUNT(*) FROM "Avatars" WHERE is_active = true) as total_avatars,
    ROUND(
        COUNT(CASE WHEN ui.item_type = 'AVATAR' THEN 1 END) * 100.0 / 
        NULLIF((SELECT COUNT(*) FROM "Avatars" WHERE is_active = true), 0), 2
    ) as avatar_completion_percent,
    
    -- Frame collection
    COUNT(CASE WHEN ui.item_type = 'FRAME' THEN 1 END) as frames_owned,
    (SELECT COUNT(*) FROM "AvatarFrames" WHERE is_active = true) as total_frames,
    ROUND(
        COUNT(CASE WHEN ui.item_type = 'FRAME' THEN 1 END) * 100.0 / 
        NULLIF((SELECT COUNT(*) FROM "AvatarFrames" WHERE is_active = true), 0), 2
    ) as frame_completion_percent,
    
    -- Emoji collection
    COUNT(CASE WHEN ui.item_type = 'EMOJI' THEN 1 END) as emojis_owned,
    (SELECT COUNT(*) FROM "Emojis" WHERE is_active = true) as total_emojis,
    ROUND(
        COUNT(CASE WHEN ui.item_type = 'EMOJI' THEN 1 END) * 100.0 / 
        NULLIF((SELECT COUNT(*) FROM "Emojis" WHERE is_active = true), 0), 2
    ) as emoji_completion_percent,
    
    -- Overall collection
    COUNT(ui.inventory_id) as total_items_owned,
    (
        (SELECT COUNT(*) FROM "Avatars" WHERE is_active = true) +
        (SELECT COUNT(*) FROM "AvatarFrames" WHERE is_active = true) +
        (SELECT COUNT(*) FROM "Emojis" WHERE is_active = true)
    ) as total_items_available,
    ROUND(
        COUNT(ui.inventory_id) * 100.0 / 
        NULLIF((
            (SELECT COUNT(*) FROM "Avatars" WHERE is_active = true) +
            (SELECT COUNT(*) FROM "AvatarFrames" WHERE is_active = true) +
            (SELECT COUNT(*) FROM "Emojis" WHERE is_active = true)
        ), 0), 2
    ) as overall_completion_percent
FROM "Users" u
LEFT JOIN "UserInventory" ui ON u.user_id = ui.user_id
GROUP BY u.user_id, u.name;

-- =====================================================
-- 9. FUNCTIONS hỗ trợ
-- =====================================================

-- Function: Get user's equipped items
CREATE OR REPLACE FUNCTION get_user_equipped_items(p_user_id INTEGER)
RETURNS TABLE (
    avatar_id INTEGER,
    avatar_name VARCHAR,
    avatar_image VARCHAR,
    frame_id INTEGER,
    frame_name VARCHAR,
    frame_image VARCHAR,
    effect_id INTEGER,
    effect_name VARCHAR,
    effect_css VARCHAR
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        a.avatar_id,
        a.avatar_name,
        a.image_path,
        af.frame_id,
        af.frame_name,
        af.image_path,
        ne.effect_id,
        ne.effect_name,
        ne.css_class
    FROM "UserCustomization" uc
    LEFT JOIN "Avatars" a ON uc.equipped_avatar_id = a.avatar_id
    LEFT JOIN "AvatarFrames" af ON uc.equipped_frame_id = af.frame_id
    LEFT JOIN "NameEffects" ne ON uc.equipped_name_effect_id = ne.effect_id
    WHERE uc.user_id = p_user_id;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 10. DỮ LIỆU MẪU
-- =====================================================

-- Thêm 30 avatar động vật
INSERT INTO "Avatars" ("avatar_name", "avatar_code", "description", "image_path", "rarity", "unlock_type", "unlock_condition", "is_default", "sort_order") VALUES
-- Default avatars (5 avatars)
('Mèo Cam', 'CAT_ORANGE', 'Avatar mèo cam dễ thương', '/avatars/cat_orange.png', 'COMMON', 'DEFAULT', '{}', true, 1),
('Chó Vàng', 'DOG_GOLDEN', 'Avatar chó vàng thân thiện', '/avatars/dog_golden.png', 'COMMON', 'DEFAULT', '{}', true, 2),
('Thỏ Trắng', 'RABBIT_WHITE', 'Avatar thỏ trắng ngọt ngào', '/avatars/rabbit_white.png', 'COMMON', 'DEFAULT', '{}', true, 3),
('Gấu Nâu', 'BEAR_BROWN', 'Avatar gấu nâu đáng yêu', '/avatars/bear_brown.png', 'COMMON', 'DEFAULT', '{}', true, 4),
('Cáo Đỏ', 'FOX_RED', 'Avatar cáo đỏ thông minh', '/avatars/fox_red.png', 'COMMON', 'DEFAULT', '{}', true, 5),

-- Level unlock avatars (15 avatars)
('Sư Tử Vàng', 'LION_GOLDEN', 'Vua của muôn loài', '/avatars/lion_golden.png', 'UNCOMMON', 'LEVEL', '{"required_level": 10}', false, 6),
('Hổ Trắng', 'TIGER_WHITE', 'Sức mạnh và uy quyền', '/avatars/tiger_white.png', 'UNCOMMON', 'LEVEL', '{"required_level": 15}', false, 7),
('Voi Xám', 'ELEPHANT_GRAY', 'Trí tuệ và sức mạnh', '/avatars/elephant_gray.png', 'UNCOMMON', 'LEVEL', '{"required_level": 20}', false, 8),
('Ngựa Đen', 'HORSE_BLACK', 'Tốc độ và sự tự do', '/avatars/horse_black.png', 'UNCOMMON', 'LEVEL', '{"required_level": 25}', false, 9),
('Sói Xám', 'WOLF_GRAY', 'Tinh thần đồng đội', '/avatars/wolf_gray.png', 'RARE', 'LEVEL', '{"required_level": 30}', false, 10),
('Đại Bàng', 'EAGLE_BROWN', 'Tầm nhìn xa và sự tự do', '/avatars/eagle_brown.png', 'RARE', 'LEVEL', '{"required_level": 35}', false, 11),
('Cú Mèo', 'OWL_BROWN', 'Trí tuệ và sự thông thái', '/avatars/owl_brown.png', 'RARE', 'LEVEL', '{"required_level": 40}', false, 12),
('Báo Đốm', 'LEOPARD_SPOTTED', 'Sự nhanh nhẹn và uy lực', '/avatars/leopard_spotted.png', 'RARE', 'LEVEL', '{"required_level": 45}', false, 13),
('Gấu Trúc', 'PANDA_BLACK_WHITE', 'Sự dễ thương và hiếm có', '/avatars/panda_black_white.png', 'EPIC', 'LEVEL', '{"required_level": 50}', false, 14),
('Rùa Biển', 'TURTLE_SEA', 'Sự bền bỉ và trường thọ', '/avatars/turtle_sea.png', 'EPIC', 'LEVEL', '{"required_level": 60}', false, 15),
('Cá Heo', 'DOLPHIN_BLUE', 'Trí tuệ và sự thân thiện', '/avatars/dolphin_blue.png', 'EPIC', 'LEVEL', '{"required_level": 70}', false, 16),
('Chim Hồng Hạc', 'FLAMINGO_PINK', 'Sự thanh lịch và độc đáo', '/avatars/flamingo_pink.png', 'EPIC', 'LEVEL', '{"required_level": 80}', false, 17),
('Khỉ Vàng', 'MONKEY_GOLDEN', 'Sự thông minh và nhanh nhẹn', '/avatars/monkey_golden.png', 'EPIC', 'LEVEL', '{"required_level": 90}', false, 18),
('Công Xanh', 'PEACOCK_BLUE', 'Vẻ đẹp và sự kiêu hãnh', '/avatars/peacock_blue.png', 'LEGENDARY', 'LEVEL', '{"required_level": 100}', false, 19),
('Kỳ Lân', 'UNICORN_WHITE', 'Huyền thoại và phép màu', '/avatars/unicorn_white.png', 'LEGENDARY', 'LEVEL', '{"required_level": 120}', false, 20),

-- Egg exclusive avatars (10 avatars)
('Rồng Vàng', 'DRAGON_GOLDEN', 'Rồng huyền thoại mang lại may mắn', '/avatars/dragon_golden.png', 'LEGENDARY', 'EGG', '{"egg_types": ["LEGENDARY_EGG", "DRAGON_EGG"]}', false, 21),
('Phượng Hoàng', 'PHOENIX_FIRE', 'Chim lửa bất tử', '/avatars/phoenix_fire.png', 'LEGENDARY', 'EGG', '{"egg_types": ["LEGENDARY_EGG", "MYTHICAL_EGG"]}', false, 22),
('Sói Băng', 'WOLF_ICE', 'Sói từ vùng đất băng giá', '/avatars/wolf_ice.png', 'EPIC', 'EGG', '{"egg_types": ["ICE_EGG", "RARE_EGG"]}', false, 23),
('Mèo Ninja', 'CAT_NINJA', 'Mèo với kỹ năng ninja', '/avatars/cat_ninja.png', 'EPIC', 'EGG', '{"egg_types": ["SHADOW_EGG", "RARE_EGG"]}', false, 24),
('Cá Mập Robot', 'SHARK_ROBOT', 'Cá mập cơ khí tương lai', '/avatars/shark_robot.png', 'EPIC', 'EGG', '{"egg_types": ["TECH_EGG", "RARE_EGG"]}', false, 25),
('Bướm Thiên Thần', 'BUTTERFLY_ANGEL', 'Bướm với đôi cánh thiên thần', '/avatars/butterfly_angel.png', 'RARE', 'EGG', '{"egg_types": ["NATURE_EGG", "BASIC_EGG"]}', false, 26),
('Ong Vàng', 'BEE_GOLDEN', 'Ong vàng chăm chỉ', '/avatars/bee_golden.png', 'RARE', 'EGG', '{"egg_types": ["NATURE_EGG", "BASIC_EGG"]}', false, 27),
('Chuột Hamster', 'HAMSTER_BROWN', 'Chuột hamster đáng yêu', '/avatars/hamster_brown.png', 'UNCOMMON', 'EGG', '{"egg_types": ["BASIC_EGG"]}', false, 28),
('Chim Cánh Cụt', 'PENGUIN_BLACK_WHITE', 'Chim cánh cụt từ Nam Cực', '/avatars/penguin_black_white.png', 'UNCOMMON', 'EGG', '{"egg_types": ["ICE_EGG", "BASIC_EGG"]}', false, 29),
('Tắc Kè Hoa', 'CHAMELEON_COLORFUL', 'Tắc kè hoa đổi màu', '/avatars/chameleon_colorful.png', 'UNCOMMON', 'EGG', '{"egg_types": ["NATURE_EGG", "BASIC_EGG"]}', false, 30)
ON CONFLICT (avatar_code) DO NOTHING;

-- Thêm khung avatar theo tier
INSERT INTO "AvatarFrames" ("frame_name", "frame_code", "description", "image_path", "rarity", "unlock_type", "unlock_condition", "tier_name", "is_default", "sort_order") VALUES
-- Default frame
('Khung Cơ Bản', 'FRAME_BASIC', 'Khung avatar cơ bản', '/frames/basic.png', 'COMMON', 'DEFAULT', '{}', null, true, 1),

-- Tier frames
('Khung Gỗ', 'FRAME_WOOD', 'Khung gỗ cho tầng Wood', '/frames/wood.png', 'COMMON', 'TIER', '{"tier": "Wood", "level_range": [1, 12]}', 'Wood', false, 2),
('Khung Đồng', 'FRAME_BRONZE', 'Khung đồng cho tầng Bronze', '/frames/bronze.png', 'UNCOMMON', 'TIER', '{"tier": "Bronze", "level_range": [13, 24]}', 'Bronze', false, 3),
('Khung Bạc', 'FRAME_SILVER', 'Khung bạc cho tầng Silver', '/frames/silver.png', 'UNCOMMON', 'TIER', '{"tier": "Silver", "level_range": [25, 36]}', 'Silver', false, 4),
('Khung Vàng', 'FRAME_GOLD', 'Khung vàng cho tầng Gold', '/frames/gold.png', 'RARE', 'TIER', '{"tier": "Gold", "level_range": [37, 48]}', 'Gold', false, 5),
('Khung Bạch Kim', 'FRAME_PLATINUM', 'Khung bạch kim cho tầng Platinum', '/frames/platinum.png', 'RARE', 'TIER', '{"tier": "Platinum", "level_range": [49, 60]}', 'Platinum', false, 6),
('Khung Onyx', 'FRAME_ONYX', 'Khung onyx cho tầng Onyx', '/frames/onyx.png', 'EPIC', 'TIER', '{"tier": "Onyx", "level_range": [61, 72]}', 'Onyx', false, 7),
('Khung Sapphire', 'FRAME_SAPPHIRE', 'Khung sapphire cho tầng Sapphire', '/frames/sapphire.png', 'EPIC', 'TIER', '{"tier": "Sapphire", "level_range": [73, 84]}', 'Sapphire', false, 8),
('Khung Ruby', 'FRAME_RUBY', 'Khung ruby cho tầng Ruby', '/frames/ruby.png', 'LEGENDARY', 'TIER', '{"tier": "Ruby", "level_range": [85, 96]}', 'Ruby', false, 9),
('Khung Amethyst', 'FRAME_AMETHYST', 'Khung amethyst cho tầng Amethyst', '/frames/amethyst.png', 'LEGENDARY', 'TIER', '{"tier": "Amethyst", "level_range": [97, 108]}', 'Amethyst', false, 10),
('Khung Master', 'FRAME_MASTER', 'Khung master cho tầng Master', '/frames/master.png', 'LEGENDARY', 'TIER', '{"tier": "Master", "level_range": [109, 120]}', 'Master', false, 11),

-- Special frames from eggs
('Khung Rồng', 'FRAME_DRAGON', 'Khung rồng huyền thoại', '/frames/dragon.png', 'LEGENDARY', 'EGG', '{"egg_types": ["LEGENDARY_EGG", "DRAGON_EGG"]}', null, false, 12),
('Khung Thiên Thần', 'FRAME_ANGEL', 'Khung thiên thần thánh thiện', '/frames/angel.png', 'EPIC', 'EGG', '{"egg_types": ["MYTHICAL_EGG", "RARE_EGG"]}', null, false, 13),
('Khung Băng Giá', 'FRAME_ICE', 'Khung băng giá lạnh lẽo', '/frames/ice.png', 'EPIC', 'EGG', '{"egg_types": ["ICE_EGG", "RARE_EGG"]}', null, false, 14),
('Khung Lửa', 'FRAME_FIRE', 'Khung lửa bùng cháy', '/frames/fire.png', 'EPIC', 'EGG', '{"egg_types": ["FIRE_EGG", "RARE_EGG"]}', null, false, 15),
('Khung Tự Nhiên', 'FRAME_NATURE', 'Khung tự nhiên xanh tươi', '/frames/nature.png', 'RARE', 'EGG', '{"egg_types": ["NATURE_EGG", "BASIC_EGG"]}', null, false, 16)
ON CONFLICT (frame_code) DO NOTHING;

-- Thêm hiệu ứng tên theo tier
INSERT INTO "NameEffects" ("effect_name", "effect_code", "description", "css_class", "css_style", "tier_name", "unlock_level") VALUES
('Hiệu Ứng Onyx', 'EFFECT_ONYX', 'Viền tên màu xám đậm', 'name-effect-onyx', 'color: #2d3748; text-shadow: 1px 1px 2px rgba(0,0,0,0.5);', 'Onyx', 61),
('Hiệu Ứng Sapphire', 'EFFECT_SAPPHIRE', 'Tên có hiệu ứng sóng nước xanh lam', 'name-effect-sapphire', 'color: #3182ce; animation: wave 2s ease-in-out infinite;', 'Sapphire', 73),
('Hiệu Ứng Ruby', 'EFFECT_RUBY', 'Tên có viền đỏ rực', 'name-effect-ruby', 'color: #e53e3e; text-shadow: 0 0 10px rgba(229, 62, 62, 0.8);', 'Ruby', 85),
('Hiệu Ứng Amethyst', 'EFFECT_AMETHYST', 'Tên có hiệu ứng tím huyền bí', 'name-effect-amethyst', 'color: #805ad5; text-shadow: 0 0 15px rgba(128, 90, 213, 0.9);', 'Amethyst', 97),
('Hiệu Ứng Master', 'EFFECT_MASTER', 'Tên có hào quang vàng lấp lánh', 'name-effect-master', 'color: #d69e2e; text-shadow: 0 0 20px rgba(214, 158, 46, 1); animation: sparkle 3s ease-in-out infinite;', 'Master', 109)
ON CONFLICT (effect_code) DO NOTHING;

-- Thêm emoji cơ bản
INSERT INTO "Emojis" ("emoji_name", "emoji_code", "emoji_unicode", "description", "rarity", "unlock_type", "category", "is_default", "sort_order") VALUES
-- Default emojis
('Mặt Cười', 'SMILE', '😊', 'Biểu tượng mặt cười', 'COMMON', 'DEFAULT', 'HAPPY', true, 1),
('Cười Lớn', 'LAUGH', '😂', 'Biểu tượng cười lớn', 'COMMON', 'DEFAULT', 'HAPPY', true, 2),
('Thích', 'LIKE', '👍', 'Biểu tượng thích', 'COMMON', 'DEFAULT', 'GENERAL', true, 3),
('Trái Tim', 'HEART', '❤️', 'Biểu tượng trái tim', 'COMMON', 'DEFAULT', 'LOVE', true, 4),
('Buồn', 'SAD', '😢', 'Biểu tượng buồn', 'COMMON', 'DEFAULT', 'SAD', true, 5),
('Tức Giận', 'ANGRY', '😠', 'Biểu tượng tức giận', 'COMMON', 'DEFAULT', 'ANGRY', true, 6),
('Ngạc Nhiên', 'SURPRISED', '😮', 'Biểu tượng ngạc nhiên', 'COMMON', 'DEFAULT', 'SURPRISED', true, 7),
('Chúc Mừng', 'CELEBRATE', '🎉', 'Biểu tượng chúc mừng', 'COMMON', 'DEFAULT', 'CELEBRATION', true, 8),

-- Level unlock emojis
('Mặt Mát', 'COOL', '😎', 'Biểu tượng mặt mát', 'UNCOMMON', 'LEVEL', 'GENERAL', false, 9),
('Wink', 'WINK', '😉', 'Biểu tượng nháy mắt', 'UNCOMMON', 'LEVEL', 'HAPPY', false, 10),
('Suy Nghĩ', 'THINKING', '🤔', 'Biểu tượng suy nghĩ', 'UNCOMMON', 'LEVEL', 'GENERAL', false, 11),
('Hoàn Hảo', 'PERFECT', '👌', 'Biểu tượng hoàn hảo', 'RARE', 'LEVEL', 'GENERAL', false, 12),
('Chiến Thắng', 'VICTORY', '✌️', 'Biểu tượng chiến thắng', 'RARE', 'LEVEL', 'CELEBRATION', false, 13),

-- Egg exclusive emojis
('Rồng Mặt', 'DRAGON_FACE', '🐲', 'Biểu tượng mặt rồng', 'LEGENDARY', 'EGG', 'ANIMALS', false, 14),
('Kỳ Lân Mặt', 'UNICORN_FACE', '🦄', 'Biểu tượng mặt kỳ lân', 'LEGENDARY', 'EGG', 'ANIMALS', false, 15),
('Lửa', 'FIRE', '🔥', 'Biểu tượng lửa', 'EPIC', 'EGG', 'SPECIAL', false, 16),
('Sét', 'LIGHTNING', '⚡', 'Biểu tượng sét', 'EPIC', 'EGG', 'SPECIAL', false, 17),
('Ngôi Sao', 'STAR', '⭐', 'Biểu tượng ngôi sao', 'EPIC', 'EGG', 'SPECIAL', false, 18),
('Vương Miện', 'CROWN', '👑', 'Biểu tượng vương miện', 'EPIC', 'EGG', 'SPECIAL', false, 19),
('Báu Vật', 'TREASURE', '💎', 'Biểu tượng báu vật', 'RARE', 'EGG', 'SPECIAL', false, 20)
ON CONFLICT (emoji_code) DO NOTHING;

-- =====================================================
-- 11. TRIGGER FUNCTIONS
-- =====================================================

-- Function: Auto-update UserCustomization when user gets new items
CREATE OR REPLACE FUNCTION auto_equip_first_items()
RETURNS TRIGGER AS $$
BEGIN
    -- Auto-equip first avatar if user doesn't have any equipped
    IF NEW.item_type = 'AVATAR' THEN
        INSERT INTO "UserCustomization" (user_id, equipped_avatar_id)
        VALUES (NEW.user_id, NEW.item_id)
        ON CONFLICT (user_id)
        DO UPDATE SET
            equipped_avatar_id = CASE
                WHEN "UserCustomization".equipped_avatar_id IS NULL
                THEN NEW.item_id
                ELSE "UserCustomization".equipped_avatar_id
            END;
    END IF;

    -- Auto-equip first frame if user doesn't have any equipped
    IF NEW.item_type = 'FRAME' THEN
        INSERT INTO "UserCustomization" (user_id, equipped_frame_id)
        VALUES (NEW.user_id, NEW.item_id)
        ON CONFLICT (user_id)
        DO UPDATE SET
            equipped_frame_id = CASE
                WHEN "UserCustomization".equipped_frame_id IS NULL
                THEN NEW.item_id
                ELSE "UserCustomization".equipped_frame_id
            END;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger
DROP TRIGGER IF EXISTS trigger_auto_equip_items ON "UserInventory";
CREATE TRIGGER trigger_auto_equip_items
    AFTER INSERT ON "UserInventory"
    FOR EACH ROW
    EXECUTE FUNCTION auto_equip_first_items();

-- Function: Update last_updated timestamp
CREATE OR REPLACE FUNCTION update_customization_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    NEW.last_updated = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for UserCustomization
DROP TRIGGER IF EXISTS trigger_update_customization_timestamp ON "UserCustomization";
CREATE TRIGGER trigger_update_customization_timestamp
    BEFORE UPDATE ON "UserCustomization"
    FOR EACH ROW
    EXECUTE FUNCTION update_customization_timestamp();

-- =====================================================
-- 12. INITIAL SETUP FUNCTIONS
-- =====================================================

-- Function: Initialize user with default items
CREATE OR REPLACE FUNCTION initialize_user_avatar_system(p_user_id INTEGER)
RETURNS VOID AS $$
DECLARE
    default_avatar_id INTEGER;
    default_frame_id INTEGER;
BEGIN
    -- Get default avatar and frame
    SELECT avatar_id INTO default_avatar_id
    FROM "Avatars"
    WHERE is_default = true
    ORDER BY sort_order
    LIMIT 1;

    SELECT frame_id INTO default_frame_id
    FROM "AvatarFrames"
    WHERE is_default = true
    ORDER BY sort_order
    LIMIT 1;

    -- Add default avatars to inventory
    INSERT INTO "UserInventory" (user_id, item_type, item_id, obtained_from)
    SELECT p_user_id, 'AVATAR', avatar_id, 'DEFAULT'
    FROM "Avatars"
    WHERE is_default = true
    ON CONFLICT (user_id, item_type, item_id) DO NOTHING;

    -- Add default frame to inventory
    INSERT INTO "UserInventory" (user_id, item_type, item_id, obtained_from)
    VALUES (p_user_id, 'FRAME', default_frame_id, 'DEFAULT')
    ON CONFLICT (user_id, item_type, item_id) DO NOTHING;

    -- Add default emojis to inventory
    INSERT INTO "UserInventory" (user_id, item_type, item_id, obtained_from)
    SELECT p_user_id, 'EMOJI', emoji_id, 'DEFAULT'
    FROM "Emojis"
    WHERE is_default = true
    ON CONFLICT (user_id, item_type, item_id) DO NOTHING;

    -- Create customization record
    INSERT INTO "UserCustomization" (user_id, equipped_avatar_id, equipped_frame_id)
    VALUES (p_user_id, default_avatar_id, default_frame_id)
    ON CONFLICT (user_id) DO NOTHING;

END;
$$ LANGUAGE plpgsql;

-- Commit transaction
COMMIT;
