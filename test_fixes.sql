-- =====================================================
-- TEST SCRIPT: REMOVE DAILY LIMITS + POPULATE AVATAR DATA
-- =====================================================

-- PART 1: REMOVE DAILY CURRENCY LIMITS
-- =====================================================

-- 1. Bỏ giới hạn cấp Currency (toàn bộ loại tiền)
UPDATE "Currencies" 
SET "max_daily_earn" = NULL 
WHERE "max_daily_earn" IS NOT NULL;

-- 2. Bỏ giới hạn cấp Rule (từng hoạt động)
UPDATE "CurrencyEarningRules" 
SET "daily_limit" = NULL 
WHERE "daily_limit" IS NOT NULL;

-- 3. Reset daily earned counter cho tất cả users (optional)
UPDATE "UserCurrencies" 
SET "daily_earned_today" = 0;

-- Verify currency limits removal
SELECT 
    'CURRENCY LIMITS REMOVED' as status,
    currency_code,
    currency_name,
    max_daily_earn
FROM "Currencies"
WHERE max_daily_earn IS NOT NULL;

-- Should return no rows if successful

-- PART 2: POPULATE AVATAR DATA (QUICK VERSION)
-- =====================================================

-- Clear existing data
DELETE FROM "UserInventory" WHERE item_type IN ('AVATAR', 'FRAME', 'EMOJI');
DELETE FROM "UserCustomization";
DELETE FROM "Avatars";
DELETE FROM "AvatarFrames";
DELETE FROM "Emojis";

-- Reset sequences
ALTER SEQUENCE "Avatars_avatar_id_seq" RESTART WITH 1;
ALTER SEQUENCE "AvatarFrames_frame_id_seq" RESTART WITH 1;
ALTER SEQUENCE "Emojis_emoji_id_seq" RESTART WITH 1;

-- Insert 10 sample avatars (quick test)
INSERT INTO "Avatars" ("avatar_name", "avatar_code", "description", "image_path", "rarity", "unlock_type", "unlock_condition", "is_default", "sort_order") VALUES
('Chó', 'DOG', 'Avatar chó đáng yêu', '/avatar-animal-pack/dog.png', 'COMMON', 'DEFAULT', '{}', true, 1),
('Thỏ', 'RABBIT', 'Avatar thỏ xinh', '/avatar-animal-pack/rabbit.png', 'COMMON', 'DEFAULT', '{}', true, 2),
('Gấu', 'BEAR', 'Avatar gấu nâu', '/avatar-animal-pack/bear.png', 'COMMON', 'DEFAULT', '{}', true, 3),
('Mèo', 'CAT', 'Avatar mèo dễ thương', '/avatar-animal-pack/cat.png', 'COMMON', 'TIER', '{"tier": "Wood", "min_level": 5}', false, 4),
('Cáo', 'FOX', 'Avatar cáo thông minh', '/avatar-animal-pack/fox.png', 'UNCOMMON', 'TIER', '{"tier": "Bronze", "min_level": 10}', false, 5),
('Sói', 'WOLF', 'Avatar sói mạnh mẽ', '/avatar-animal-pack/wolf.png', 'RARE', 'TIER', '{"tier": "Silver", "min_level": 20}', false, 6),
('Sư Tử', 'LION', 'Avatar sư tử ouy mạnh', '/avatar-animal-pack/lion.png', 'EPIC', 'TIER', '{"tier": "Gold", "min_level": 30}', false, 7),
('Rồng', 'DRAGON', 'Avatar rồng huyền thoại', '/avatar-animal-pack/dragon.png', 'LEGENDARY', 'TIER', '{"tier": "Diamond", "min_level": 50}', false, 8),
('Cá Heo', 'DOLPHIN', 'Avatar cá heo thông minh', '/avatar-animal-pack/dolphin.png', 'UNCOMMON', 'EGG', '{"egg_types": ["WATER_EGG"]}', false, 9),
('Chim Ưng', 'EAGLE', 'Avatar chim ưng bay cao', '/avatar-animal-pack/eagle.png', 'RARE', 'SHOP', '{"price_syncoin": 1000}', false, 10);

-- Insert 16 frames: 10 tier frames + 6 premium frames
INSERT INTO "AvatarFrames" ("frame_name", "frame_code", "description", "image_path", "rarity", "unlock_type", "tier_name", "tier_min_level", "tier_max_level", "is_default", "sort_order") VALUES

-- TIER RANK FRAMES (10 frames - auto unlock by tier progression)
('Khung Gỗ', 'WOOD_FRAME', 'Khung gỗ cơ bản', '/vector-ranks-pack/wood/diamond-wood-1.png', 'COMMON', 'DEFAULT', 'Wood', 1, 10, true, 1),
('Khung Đồng', 'BRONZE_FRAME', 'Khung đồng', '/vector-ranks-pack/bronze/diamond-bronze-1.png', 'COMMON', 'TIER', 'Bronze', 11, 20, false, 2),
('Khung Bạc', 'SILVER_FRAME', 'Khung bạc', '/vector-ranks-pack/silver/diamond-silver-1.png', 'UNCOMMON', 'TIER', 'Silver', 21, 30, false, 3),
('Khung Vàng', 'GOLD_FRAME', 'Khung vàng', '/vector-ranks-pack/gold/diamond-gold-1.png', 'UNCOMMON', 'TIER', 'Gold', 31, 40, false, 4),
('Khung Bạch Kim', 'PLATINUM_FRAME', 'Khung bạch kim', '/vector-ranks-pack/platinum/diamond-platinum-1.png', 'RARE', 'TIER', 'Platinum', 41, 50, false, 5),
('Khung Onyx', 'ONYX_FRAME', 'Khung onyx', '/vector-ranks-pack/onyx/diamond-onyx-1.png', 'RARE', 'TIER', 'Onyx', 51, 60, false, 6),
('Khung Sapphire', 'SAPPHIRE_FRAME', 'Khung sapphire', '/vector-ranks-pack/sapphire/diamond-sapphire-1.png', 'EPIC', 'TIER', 'Sapphire', 61, 70, false, 7),
('Khung Ruby', 'RUBY_FRAME', 'Khung ruby', '/vector-ranks-pack/ruby/diamond-ruby-1.png', 'EPIC', 'TIER', 'Ruby', 71, 80, false, 8),
('Khung Amethyst', 'AMETHYST_FRAME', 'Khung amethyst', '/vector-ranks-pack/amethyst/diamond-amethyst-1.png', 'LEGENDARY', 'TIER', 'Amethyst', 81, 90, false, 9),
('Khung Master', 'MASTER_FRAME', 'Khung master', '/vector-ranks-pack/master/diamond-master-1.png', 'LEGENDARY', 'TIER', 'Master', 91, 999, false, 10),

-- PREMIUM FRAMES (6 frames - purchase with Kristal + tier requirement)
('Khung Đại Dương', 'OCEAN_SONG_FRAME', 'Khung bài ca đại dương', '/avatar-frame-pack/ocean-song-frame.png', 'RARE', 'SHOP', NULL, NULL, NULL, false, 11),
('Khung Lễ Hội', 'DRUMALONG_FESTIVAL_FRAME', 'Khung lễ hội sôi động', '/avatar-frame-pack/drumalong-festival-frame.png', 'EPIC', 'SHOP', NULL, NULL, NULL, false, 12),
('Khung Sao Tím', 'VIOLET_STARLIGHT_FRAME', 'Khung ánh sao tím', '/avatar-frame-pack/violet-starlight-frame.png', 'LEGENDARY', 'SHOP', NULL, NULL, NULL, false, 13),
('Khung Cyber', 'CYBER_GLITCH_FRAME', 'Khung cyber tương lai', '/avatar-frame-pack/cyber-glitch-frame.png', 'LEGENDARY', 'SHOP', NULL, NULL, NULL, false, 14),
('Khung Lửa', 'NATION_OF_PYRO_FRAME', 'Khung ngọn lửa mạnh mẽ', '/avatar-frame-pack/nation-of-pyro-frame.png', 'EPIC', 'SHOP', NULL, NULL, NULL, false, 15),
('Khung Phượng Hoàng', 'CRIMSON_PHOENIX_FRAME', 'Khung phượng hoàng đỏ rực', '/avatar-frame-pack/crimson-phoenix-frame.png', 'LEGENDARY', 'SHOP', NULL, NULL, NULL, false, 16);

-- Insert 8 sample emojis
INSERT INTO "Emojis" ("emoji_name", "emoji_code", "emoji_unicode", "emoji_image", "category", "rarity", "unlock_type", "unlock_condition", "is_default", "sort_order") VALUES
('Mặt Cười', 'SMILE', '😊', '/vector-emojis-pack/smile.png', 'HAPPY', 'COMMON', 'DEFAULT', '{}', true, 1),
('Cười Lớn', 'LAUGH', '😂', '/vector-emojis-pack/laugh.png', 'HAPPY', 'COMMON', 'DEFAULT', '{}', true, 2),
('Thích', 'THUMBS_UP', '👍', '/vector-emojis-pack/thumbs-up.png', 'GENERAL', 'COMMON', 'DEFAULT', '{}', true, 3),
('Trái Tim', 'HEART', '❤️', '/vector-emojis-pack/heart.png', 'LOVE', 'COMMON', 'DEFAULT', '{}', true, 4),
('Buồn', 'SAD', '😢', '/vector-emojis-pack/sad.png', 'SAD', 'COMMON', 'TIER', '{"tier": "Bronze", "min_level": 10}', false, 5),
('Tức Giận', 'ANGRY', '😠', '/vector-emojis-pack/angry.png', 'ANGRY', 'UNCOMMON', 'TIER', '{"tier": "Silver", "min_level": 20}', false, 6),
('Ngạc Nhiên', 'SURPRISED', '😲', '/vector-emojis-pack/surprised.png', 'GENERAL', 'RARE', 'EGG', '{"egg_types": ["BASIC_EGG"]}', false, 7),
('Mặt Hề', 'CLOWN', '🤡', '/vector-emojis-pack/clown.png', 'SPECIAL', 'LEGENDARY', 'SHOP', '{"price_kristal": 50}', false, 8);

-- Verify data insertion
SELECT 
    'AVATAR DATA POPULATED' as status,
    (SELECT COUNT(*) FROM "Avatars") as avatars_count,
    (SELECT COUNT(*) FROM "AvatarFrames") as frames_count,
    (SELECT COUNT(*) FROM "Emojis") as emojis_count;

-- Initialize avatar system for existing users (user_id = 1)
-- Add default items to inventory (simple approach)
INSERT INTO "UserInventory" (user_id, item_type, item_id, obtained_from)
SELECT 1, 'AVATAR', avatar_id, 'DEFAULT'
FROM "Avatars"
WHERE is_default = true
  AND NOT EXISTS (
    SELECT 1 FROM "UserInventory"
    WHERE user_id = 1 AND item_type = 'AVATAR' AND item_id = "Avatars".avatar_id
  );

INSERT INTO "UserInventory" (user_id, item_type, item_id, obtained_from)
SELECT 1, 'FRAME', frame_id, 'DEFAULT'
FROM "AvatarFrames"
WHERE is_default = true
  AND NOT EXISTS (
    SELECT 1 FROM "UserInventory"
    WHERE user_id = 1 AND item_type = 'FRAME' AND item_id = "AvatarFrames".frame_id
  );

INSERT INTO "UserInventory" (user_id, item_type, item_id, obtained_from)
SELECT 1, 'EMOJI', emoji_id, 'DEFAULT'
FROM "Emojis"
WHERE is_default = true
  AND NOT EXISTS (
    SELECT 1 FROM "UserInventory"
    WHERE user_id = 1 AND item_type = 'EMOJI' AND item_id = "Emojis".emoji_id
  );

-- Create customization record (user_id = 1, first avatar = 1, first frame = 1)
IF NOT EXISTS (SELECT 1 FROM "UserCustomization" WHERE user_id = 1)
BEGIN
    INSERT INTO "UserCustomization" (user_id, equipped_avatar_id, equipped_frame_id)
    VALUES (1, 1, 1);
END;

-- Final verification
SELECT 
    'TEST COMPLETED' as status,
    'Currency limits removed, Avatar data populated, User initialized' as message;
