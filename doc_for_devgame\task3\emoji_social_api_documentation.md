# Emoji & Social Interaction System API Documentation

## Overview
This document provides comprehensive API documentation for the Emoji & Social Interaction System implemented in Task 3.1 of the Synlearnia gamification project.

## Base URL
```
http://localhost:3000/api
```

## Authentication
All endpoints require authentication via <PERSON><PERSON><PERSON> token in the Authorization header:
```
Authorization: Bearer <jwt_token>
```

---

## 🎭 EMOJI ENDPOINTS

### 1. Initialize User Emoji System
**POST** `/emojis/initialize`

Initialize emoji system for a user (unlock basic emojis based on current tier).

**Response:**
```json
{
  "success": true,
  "message": "Emoji system initialized with 12 emojis",
  "data": {
    "unlocked_emojis": [...],
    "user_tier": "BRONZE"
  }
}
```

### 2. Get User Emoji Collection
**GET** `/emojis/collection`

Get user's emoji collection with optional filtering.

**Query Parameters:**
- `category` (optional): BASIC, REACTION, EMOTION, SPECIAL, PREMIUM
- `rarity` (optional): COMMON, RARE, <PERSON>IC, LEGENDARY
- `is_favorite` (optional): true/false

**Response:**
```json
{
  "success": true,
  "message": "User emoji collection retrieved successfully",
  "data": {
    "emojis": [
      {
        "user_emoji_id": 1,
        "emoji_type_id": 5,
        "is_favorite": false,
        "usage_count": 15,
        "unlock_source": "TIER_PROGRESSION",
        "EmojiType": {
          "emoji_name": "Slightly Smiling Face",
          "emoji_code": "slightly-smiling-face",
          "emoji_image_path": "/vector-emojis-pack/slightly-smiling-face.svg",
          "category": "BASIC",
          "rarity": "COMMON"
        }
      }
    ],
    "stats": {
      "total_emojis": 12,
      "by_category": {...},
      "by_rarity": {...}
    }
  }
}
```

### 3. Get Available Emojis
**GET** `/emojis/available`

Get emojis available for user's tier.

**Query Parameters:**
- `tier` (optional): Override user's current tier

**Response:**
```json
{
  "success": true,
  "message": "Available emojis retrieved successfully",
  "data": {
    "emojis": [...],
    "user_tier": "BRONZE"
  }
}
```

### 4. Get Emojis by Category
**GET** `/emojis/category/:category`

Get all emojis in a specific category.

**Response:**
```json
{
  "success": true,
  "message": "Emojis in BASIC category retrieved successfully",
  "data": [...]
}
```

### 5. Get Emoji Shop
**GET** `/emojis/shop`

Get purchasable emojis for user's tier.

**Response:**
```json
{
  "success": true,
  "message": "Emoji shop retrieved successfully",
  "data": {
    "available_emojis": [...],
    "user_kristal_balance": 150,
    "user_tier": "BRONZE"
  }
}
```

### 6. Purchase Emoji
**POST** `/emojis/purchase`

Purchase an emoji with Kristal currency.

**Request Body:**
```json
{
  "emoji_type_id": 25
}
```

**Response:**
```json
{
  "success": true,
  "message": "Emoji purchased successfully",
  "data": {
    "user_emoji": {...},
    "kristal_spent": 50,
    "remaining_balance": 100
  }
}
```

### 7. Use Emoji
**POST** `/emojis/use`

Use an emoji in a specific context.

**Request Body:**
```json
{
  "emoji_type_id": 5,
  "context": "POST_QUIZ",
  "target_user_id": 123,
  "quiz_session_id": 456,
  "metadata": {
    "quiz_score": 95,
    "celebration": true
  }
}
```

**Response:**
```json
{
  "success": true,
  "message": "Emoji usage recorded successfully",
  "data": {
    "usage_record": {...},
    "social_interaction": {...}
  }
}
```

### 8. Get Emoji Usage History
**GET** `/emojis/usage/history`

Get user's emoji usage history.

**Query Parameters:**
- `context` (optional): PRE_QUIZ, DURING_QUIZ, POST_QUIZ, PROFILE, SOCIAL_REACTION, ACHIEVEMENT_CELEBRATION
- `timeframe` (optional): 1d, 7d, 30d
- `limit` (optional): Number of records

**Response:**
```json
{
  "success": true,
  "message": "Emoji usage history retrieved successfully",
  "data": [...]
}
```

### 9. Get Emoji Usage Stats
**GET** `/emojis/usage/stats`

Get user's emoji usage statistics.

**Query Parameters:**
- `timeframe` (optional): 1d, 7d, 30d

**Response:**
```json
{
  "success": true,
  "message": "Emoji usage stats retrieved successfully",
  "data": {
    "by_context": {...},
    "by_emoji": {...},
    "total_usage": 150,
    "timeframe": "7d"
  }
}
```

### 10. Set Favorite Emoji
**POST** `/emojis/favorite`

Set user's favorite emoji.

**Request Body:**
```json
{
  "emoji_type_id": 5
}
```

### 11. Get Emoji Details
**GET** `/emojis/:emoji_id`

Get detailed information about a specific emoji.

---

## 🤝 SOCIAL INTERACTION ENDPOINTS

### 1. Send Emoji Reaction
**POST** `/social/emoji-reaction`

Send an emoji reaction to another user.

**Request Body:**
```json
{
  "to_user_id": 123,
  "emoji_type_id": 5,
  "context": "quiz_completion",
  "context_id": 456,
  "metadata": {
    "message": "Great job!"
  }
}
```

**Response:**
```json
{
  "success": true,
  "message": "Social interaction recorded successfully",
  "data": {
    "interaction_id": 789,
    "from_user_id": 1,
    "to_user_id": 123,
    "interaction_type": "EMOJI_REACTION"
  }
}
```

### 2. Send Encouragement
**POST** `/social/encouragement`

Send encouragement to another user.

**Request Body:**
```json
{
  "to_user_id": 123,
  "context": "quiz_attempt",
  "context_id": 456,
  "message": "You can do it!"
}
```

### 3. Celebrate Achievement
**POST** `/social/celebrate`

Celebrate another user's achievement.

**Request Body:**
```json
{
  "to_user_id": 123,
  "achievement_type": "tier_promotion",
  "emoji_type_id": 15,
  "message": "Congratulations on reaching Silver tier!"
}
```

### 4. Get User Social Stats
**GET** `/social/stats`

Get user's social statistics.

**Query Parameters:**
- `timeframe` (optional): 1d, 7d, 30d

**Response:**
```json
{
  "success": true,
  "message": "User social stats retrieved successfully",
  "data": {
    "overall_stats": {
      "total_emojis_unlocked": 25,
      "total_emoji_usage": 150,
      "positive_interactions_sent": 45,
      "positive_interactions_received": 38,
      "social_reputation_score": 67.5,
      "reputation_level": "INTERMEDIATE"
    },
    "interaction_stats": {...},
    "usage_stats": {...},
    "timeframe": "7d"
  }
}
```

### 5. Get Social Interaction History
**GET** `/social/interactions/history`

Get user's social interaction history.

**Query Parameters:**
- `type` (optional): sent, received, both
- `interaction_type` (optional): EMOJI_REACTION, ENCOURAGEMENT, CELEBRATION, APPRECIATION, SUPPORT
- `limit` (optional): Number of records

### 6. Get Top Social Users
**GET** `/social/top-users`

Get users with most positive social interactions.

**Query Parameters:**
- `timeframe` (optional): 1d, 7d, 30d
- `limit` (optional): Number of users (default: 10)

### 7. Get Social Leaderboard
**GET** `/social/leaderboard`

Get social leaderboard based on different criteria.

**Query Parameters:**
- `criteria` (optional): reputation, emojis, usage, kindness, popularity
- `limit` (optional): Number of users (default: 10)

**Response:**
```json
{
  "success": true,
  "message": "Social leaderboard retrieved successfully",
  "data": {
    "leaderboard": [
      {
        "user_id": 123,
        "social_reputation_score": 89.5,
        "total_emojis_unlocked": 45,
        "User": {
          "username": "student123",
          "display_name": "John Doe"
        },
        "FavoriteEmoji": {
          "emoji_name": "Star Eyes",
          "emoji_code": "star-eyes"
        }
      }
    ],
    "criteria": "reputation",
    "limit": 10
  }
}
```

### 8. Get User Social Rank
**GET** `/social/rank`

Get user's rank in social leaderboard.

**Query Parameters:**
- `criteria` (optional): reputation, emojis, usage, kindness, popularity

### 9. Set Favorite Emoji (Social Profile)
**POST** `/social/favorite-emoji`

Set favorite emoji for social profile display.

**Request Body:**
```json
{
  "emoji_type_id": 15
}
```

### 10. Get User Social Profile
**GET** `/social/profile/:user_id?`

Get social profile for user (own or another user's).

**Response:**
```json
{
  "success": true,
  "message": "User social profile retrieved successfully",
  "data": {
    "stats": {
      "user_id": 123,
      "total_emojis_unlocked": 25,
      "social_reputation_score": 67.5,
      "reputation_level": "INTERMEDIATE",
      "FavoriteEmoji": {...}
    },
    "recent_interactions": [...]
  }
}
```

---

## 📊 USAGE CONTEXTS

The system supports the following emoji usage contexts:

- **PRE_QUIZ**: Before starting a quiz (motivation, preparation)
- **DURING_QUIZ**: During quiz session (reactions to questions)
- **POST_QUIZ**: After completing quiz (celebration, disappointment)
- **PROFILE**: On user profile or social interactions
- **SOCIAL_REACTION**: Reacting to other users' activities
- **ACHIEVEMENT_CELEBRATION**: Celebrating achievements and milestones

---

## 🎯 INTEGRATION POINTS

### With Existing Systems:
1. **Tier Progression**: Emojis unlock based on user tier advancement
2. **Currency System**: Premium emojis purchasable with Kristal
3. **Egg Rewards**: Emojis can be unlocked through egg drops
4. **Quiz System**: Emoji usage tracked during quiz sessions
5. **Achievement System**: Social interactions contribute to achievements

### Database Integration:
- All emoji and social data stored in PostgreSQL
- Real-time updates via existing WebSocket connections
- Analytics integration for tracking social engagement

---

## 🔧 ERROR HANDLING

All endpoints return consistent error responses:

```json
{
  "success": false,
  "message": "Error description",
  "error": "Detailed error information"
}
```

Common HTTP status codes:
- `200`: Success
- `400`: Bad Request (validation errors)
- `401`: Unauthorized (authentication required)
- `404`: Not Found (resource doesn't exist)
- `500`: Internal Server Error
