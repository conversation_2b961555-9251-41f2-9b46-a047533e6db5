-- =====================================================
-- GAMIFICATION EGG REWARD SYSTEM - DATABASE SCHEMA
-- Task 2.3: <PERSON><PERSON> thống Trứng thưởng & <PERSON><PERSON><PERSON> tầm
-- =====================================================

-- 1. EGG TYPES TABLE - <PERSON><PERSON><PERSON> lo<PERSON> trứng
CREATE TABLE IF NOT EXISTS "EggTypes" (
    "egg_type_id" SERIAL PRIMARY KEY,
    "egg_name" VARCHAR(100) NOT NULL UNIQUE,
    "egg_code" VARCHAR(50) NOT NULL UNIQUE,
    "description" TEXT,
    "image_path" VARCHAR(255),
    "rarity" VARCHAR(20) DEFAULT 'COMMON' CHECK ("rarity" IN ('COMMON', 'UNCOMMON', 'RARE', 'EPIC', 'LEGENDARY', 'MYTHICAL')),
    "base_price_syncoin" INTEGER DEFAULT 0,
    "base_price_kristal" INTEGER DEFAULT 0,
    "is_purchasable" BOOLEAN DEFAULT true,
    "is_active" BOOLEAN DEFAULT true,
    "sort_order" INTEGER DEFAULT 0,
    "created_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 2. EGG REWARDS TABLE - Phần thưởng có thể có trong trứng
CREATE TABLE IF NOT EXISTS "EggRewards" (
    "reward_id" SERIAL PRIMARY KEY,
    "egg_type_id" INTEGER REFERENCES "EggTypes"("egg_type_id") ON DELETE CASCADE,
    "reward_type" VARCHAR(20) NOT NULL CHECK ("reward_type" IN ('AVATAR', 'FRAME', 'EMOJI', 'NAME_EFFECT', 'SYNCOIN', 'KRISTAL', 'XP')),
    "reward_item_id" INTEGER, -- ID của item (avatar_id, frame_id, etc.) - NULL cho currency/XP
    "reward_amount" INTEGER DEFAULT 1, -- Số lượng (cho currency/XP)
    "drop_rate" DECIMAL(5,4) NOT NULL DEFAULT 0.1000, -- Tỉ lệ rơi (0.0001 = 0.01%)
    "is_guaranteed" BOOLEAN DEFAULT false, -- Phần thưởng đảm bảo
    "rarity_weight" INTEGER DEFAULT 1, -- Trọng số độ hiếm
    "is_active" BOOLEAN DEFAULT true,
    "created_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 3. USER EGGS TABLE - Trứng người dùng sở hữu
CREATE TABLE IF NOT EXISTS "UserEggs" (
    "user_egg_id" SERIAL PRIMARY KEY,
    "user_id" INTEGER NOT NULL REFERENCES "Users"("user_id") ON DELETE CASCADE,
    "egg_type_id" INTEGER NOT NULL REFERENCES "EggTypes"("egg_type_id") ON DELETE CASCADE,
    "quantity" INTEGER DEFAULT 1,
    "obtained_from" VARCHAR(50) DEFAULT 'UNKNOWN' CHECK ("obtained_from" IN ('QUIZ_COMPLETION', 'STREAK_BONUS', 'PERFECT_SCORE', 'LEVEL_UP', 'SHOP_PURCHASE', 'DAILY_LOGIN', 'ACHIEVEMENT', 'ADMIN', 'UNKNOWN')),
    "obtained_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "is_opened" BOOLEAN DEFAULT false,
    "opened_at" TIMESTAMP NULL,
    "created_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 4. EGG OPENING HISTORY TABLE - Lịch sử mở trứng
CREATE TABLE IF NOT EXISTS "EggOpeningHistory" (
    "opening_id" SERIAL PRIMARY KEY,
    "user_id" INTEGER NOT NULL REFERENCES "Users"("user_id") ON DELETE CASCADE,
    "egg_type_id" INTEGER NOT NULL REFERENCES "EggTypes"("egg_type_id") ON DELETE CASCADE,
    "rewards_received" JSONB NOT NULL DEFAULT '[]', -- Array of rewards received
    "total_value_syncoin" INTEGER DEFAULT 0, -- Tổng giá trị SynCoin
    "total_value_kristal" INTEGER DEFAULT 0, -- Tổng giá trị Kristal
    "was_duplicate" BOOLEAN DEFAULT false, -- Có phần thưởng trùng lặp không
    "kristal_from_duplicates" INTEGER DEFAULT 0, -- Kristal nhận từ duplicate
    "opened_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 5. EGG DROP RULES TABLE - Quy tắc rơi trứng
CREATE TABLE IF NOT EXISTS "EggDropRules" (
    "rule_id" SERIAL PRIMARY KEY,
    "rule_name" VARCHAR(100) NOT NULL,
    "rule_code" VARCHAR(50) NOT NULL UNIQUE,
    "trigger_type" VARCHAR(30) NOT NULL CHECK ("trigger_type" IN ('QUIZ_COMPLETION', 'STREAK_ACHIEVEMENT', 'PERFECT_SCORE', 'LEVEL_UP', 'DAILY_LOGIN')),
    "trigger_condition" JSONB NOT NULL DEFAULT '{}', -- Điều kiện trigger
    "egg_type_id" INTEGER REFERENCES "EggTypes"("egg_type_id") ON DELETE CASCADE,
    "drop_rate" DECIMAL(5,4) NOT NULL DEFAULT 0.1000,
    "max_per_day" INTEGER DEFAULT NULL, -- Giới hạn số lượng/ngày
    "is_active" BOOLEAN DEFAULT true,
    "created_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- INDEXES for performance
CREATE INDEX IF NOT EXISTS "idx_user_eggs_user_id" ON "UserEggs"("user_id");
CREATE INDEX IF NOT EXISTS "idx_user_eggs_egg_type" ON "UserEggs"("egg_type_id");
CREATE INDEX IF NOT EXISTS "idx_user_eggs_is_opened" ON "UserEggs"("is_opened");
CREATE INDEX IF NOT EXISTS "idx_egg_rewards_egg_type" ON "EggRewards"("egg_type_id");
CREATE INDEX IF NOT EXISTS "idx_egg_rewards_type" ON "EggRewards"("reward_type");
CREATE INDEX IF NOT EXISTS "idx_egg_opening_history_user" ON "EggOpeningHistory"("user_id");
CREATE INDEX IF NOT EXISTS "idx_egg_drop_rules_trigger" ON "EggDropRules"("trigger_type");

-- VIEWS for analytics
CREATE OR REPLACE VIEW "UserEggStats" AS
SELECT 
    u."user_id",
    u."username",
    COUNT(ue."user_egg_id") as total_eggs,
    COUNT(CASE WHEN ue."is_opened" = false THEN 1 END) as unopened_eggs,
    COUNT(CASE WHEN ue."is_opened" = true THEN 1 END) as opened_eggs,
    COUNT(DISTINCT ue."egg_type_id") as unique_egg_types,
    MAX(ue."obtained_at") as last_egg_obtained
FROM "Users" u
LEFT JOIN "UserEggs" ue ON u."user_id" = ue."user_id"
GROUP BY u."user_id", u."username";

CREATE OR REPLACE VIEW "EggTypeStats" AS
SELECT 
    et."egg_type_id",
    et."egg_name",
    et."rarity",
    COUNT(ue."user_egg_id") as total_obtained,
    COUNT(CASE WHEN ue."is_opened" = false THEN 1 END) as currently_owned,
    COUNT(CASE WHEN ue."is_opened" = true THEN 1 END) as total_opened,
    COUNT(DISTINCT ue."user_id") as unique_owners
FROM "EggTypes" et
LEFT JOIN "UserEggs" ue ON et."egg_type_id" = ue."egg_type_id"
GROUP BY et."egg_type_id", et."egg_name", et."rarity";

-- FUNCTIONS
CREATE OR REPLACE FUNCTION get_user_egg_inventory(p_user_id INTEGER)
RETURNS TABLE (
    egg_type_id INTEGER,
    egg_name VARCHAR,
    egg_code VARCHAR,
    image_path VARCHAR,
    rarity VARCHAR,
    quantity BIGINT,
    latest_obtained TIMESTAMP
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        et."egg_type_id",
        et."egg_name",
        et."egg_code",
        et."image_path",
        et."rarity",
        COUNT(ue."user_egg_id")::BIGINT as quantity,
        MAX(ue."obtained_at") as latest_obtained
    FROM "EggTypes" et
    INNER JOIN "UserEggs" ue ON et."egg_type_id" = ue."egg_type_id"
    WHERE ue."user_id" = p_user_id 
    AND ue."is_opened" = false
    GROUP BY et."egg_type_id", et."egg_name", et."egg_code", et."image_path", et."rarity"
    ORDER BY et."rarity" DESC, et."sort_order" ASC;
END;
$$ LANGUAGE plpgsql;

-- TRIGGERS
CREATE OR REPLACE FUNCTION update_egg_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    NEW."updated_at" = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_egg_types_timestamp
    BEFORE UPDATE ON "EggTypes"
    FOR EACH ROW EXECUTE FUNCTION update_egg_timestamp();

CREATE TRIGGER trigger_update_user_eggs_timestamp
    BEFORE UPDATE ON "UserEggs"
    FOR EACH ROW EXECUTE FUNCTION update_egg_timestamp();

-- INSERT INITIAL EGG TYPES DATA (24 egg types from frontend assets)
INSERT INTO "EggTypes" ("egg_name", "egg_code", "description", "image_path", "rarity", "base_price_syncoin", "base_price_kristal", "is_purchasable", "sort_order") VALUES

-- COMMON EGGS (Easy to get)
('Trứng Cơ Bản', 'BASIC_EGG', 'Trứng phổ thông với phần thưởng cơ bản', '/eggs-icon-pack/basic-egg', 'COMMON', 100, 0, true, 1),
('Trứng Nứt', 'CRACKED_EGG', 'Trứng có vết nứt, chứa phần thưởng bất ngờ', '/eggs-icon-pack/cracked-egg', 'COMMON', 150, 0, true, 2),
('Trứng Đốm', 'SPOTTED_EGG', 'Trứng có đốm màu với phần thưởng đa dạng', '/eggs-icon-pack/spotted-egg', 'COMMON', 200, 0, true, 3),

-- UNCOMMON EGGS
('Trứng Dừa', 'COCONUT_EGG', 'Trứng cứng như dừa với phần thưởng tốt', '/eggs-icon-pack/coconut-egg', 'UNCOMMON', 300, 0, true, 4),
('Trứng Mèo', 'CAT_EGG', 'Trứng chủ đề mèo với avatar đặc biệt', '/eggs-icon-pack/cat-egg', 'UNCOMMON', 400, 0, true, 5),
('Trứng Chó', 'DOG_EGG', 'Trứng chủ đề chó với avatar thú cưng', '/eggs-icon-pack/dog-egg', 'UNCOMMON', 400, 0, true, 6),
('Trứng Tiệc Tùng', 'PARTY_EGG', 'Trứng lễ hội với emoji vui nhộn', '/eggs-icon-pack/party-egg', 'UNCOMMON', 500, 0, true, 7),

-- RARE EGGS
('Trứng Băng', 'ICE_EGG', 'Trứng băng giá với hiệu ứng lạnh lùng', '/eggs-icon-pack/ice-egg', 'RARE', 0, 5, true, 8),
('Trứng Hoàng Gia', 'ROYAL_EGG', 'Trứng hoàng gia với phần thưởng quý giá', '/eggs-icon-pack/royal-egg', 'RARE', 0, 8, true, 9),
('Trứng Lâu Đài Cát', 'SANDCASTLE_EGG', 'Trứng chủ đề biển với avatar độc đáo', '/eggs-icon-pack/sandcastle-egg', 'RARE', 0, 10, true, 10),
('Trứng Mỏ', 'MINE_EGG', 'Trứng từ lòng đất với kho báu ẩn giấu', '/eggs-icon-pack/mine-egg', 'RARE', 0, 12, true, 11),
('Trứng Yeti', 'YETI_EGG', 'Trứng từ vùng núi tuyết với avatar hiếm', '/eggs-icon-pack/yeti-egg', 'RARE', 0, 15, true, 12),

-- EPIC EGGS
('Trứng Rồng', 'DRAGON_EGG', 'Trứng rồng huyền thoại với sức mạnh khủng khiếp', '/eggs-icon-pack/dragon-egg', 'EPIC', 0, 25, true, 13),
('Trứng Cầu Vồng', 'RAINBOW_EGG', 'Trứng đa sắc với phần thưởng rực rỡ', '/eggs-icon-pack/rainbow-egg', 'EPIC', 0, 30, true, 14),
('Trứng Thiên Thần', 'ANGEL_EGG', 'Trứng thiêng liêng với ơn phước từ trên cao', '/eggs-icon-pack/angel-egg', 'EPIC', 0, 35, true, 15),
('Trứng Ác Quỷ', 'DEMON_EGG', 'Trứng hắc ám với sức mạnh đen tối', '/eggs-icon-pack/demon-egg', 'EPIC', 0, 35, true, 16),
('Trứng Kraken', 'KRAKEN_EGG', 'Trứng quái vật biển sâu với avatar khủng khiếp', '/eggs-icon-pack/kraken-egg', 'EPIC', 0, 40, true, 17),

-- LEGENDARY EGGS
('Trứng Huyền Thoại', 'LEGENDARY_EGG', 'Trứng huyền thoại với phần thưởng tối thượng', '/eggs-icon-pack/legendary-egg', 'LEGENDARY', 0, 50, true, 18),
('Trứng Thiên Thần Ác Quỷ', 'ANGEL_DEMON_EGG', 'Trứng kết hợp thiện ác với sức mạnh tuyệt đối', '/eggs-icon-pack/angel-demon-egg', 'LEGENDARY', 0, 75, true, 19),
('Trứng Dominus', 'DOMINUS_EGG', 'Trứng chúa tể với quyền năng tối cao', '/eggs-icon-pack/dominus-egg', 'LEGENDARY', 0, 100, true, 20),
('Trứng Hố Đen', 'BLACK_HOLE_EGG', 'Trứng hố đen với sức hút vô tận', '/eggs-icon-pack/black-hole-egg', 'LEGENDARY', 0, 150, true, 21),

-- MYTHICAL EGGS (Rarest)
('Trứng Huyền Bí', 'MYTHICAL_EGG', 'Trứng huyền bí với phần thưởng không tưởng', '/eggs-icon-pack/mythical-egg', 'MYTHICAL', 0, 200, true, 22),
('Trứng Bí Mật', 'SECRET_EGG', 'Trứng bí ẩn chỉ dành cho những người được chọn', '/eggs-icon-pack/secret-egg', 'MYTHICAL', 0, 300, false, 23),
('Trứng Lỗi', 'GLITCHED_EGG', 'Trứng lỗi hệ thống với phần thưởng không thể đoán trước', '/eggs-icon-pack/glitched-egg', 'MYTHICAL', 0, 500, false, 24);

-- INSERT EGG REWARDS DATA
-- Common Egg Rewards (Basic Egg)
INSERT INTO "EggRewards" ("egg_type_id", "reward_type", "reward_item_id", "reward_amount", "drop_rate", "rarity_weight") VALUES
-- Basic Egg (ID: 1)
(1, 'SYNCOIN', NULL, 50, 0.4000, 1),   -- 40% chance for 50 SynCoin
(1, 'SYNCOIN', NULL, 100, 0.2000, 1),  -- 20% chance for 100 SynCoin
(1, 'XP', NULL, 25, 0.3000, 1),        -- 30% chance for 25 XP
(1, 'AVATAR', 1, 1, 0.0500, 2),        -- 5% chance for Dog avatar
(1, 'EMOJI', 1, 1, 0.0500, 2),         -- 5% chance for basic emoji

-- Cracked Egg (ID: 2)
(2, 'SYNCOIN', NULL, 75, 0.3000, 1),
(2, 'XP', NULL, 50, 0.2500, 1),
(2, 'AVATAR', 2, 1, 0.0800, 2),        -- 8% chance for Cat avatar
(2, 'EMOJI', 2, 1, 0.0700, 2),
(2, 'KRISTAL', NULL, 5, 0.3000, 3),    -- 30% chance for 5 Kristal

-- Spotted Egg (ID: 3)
(3, 'SYNCOIN', NULL, 100, 0.2500, 1),
(3, 'XP', NULL, 75, 0.2000, 1),
(3, 'AVATAR', 3, 1, 0.1000, 2),        -- 10% chance for Rabbit avatar
(3, 'EMOJI', 3, 1, 0.1000, 2),
(3, 'KRISTAL', NULL, 10, 0.3500, 3);

-- INSERT EGG DROP RULES
INSERT INTO "EggDropRules" ("rule_name", "rule_code", "trigger_type", "trigger_condition", "egg_type_id", "drop_rate", "max_per_day") VALUES

-- Quiz Completion Rules
('Hoàn Thành Quiz Cơ Bản', 'QUIZ_BASIC_DROP', 'QUIZ_COMPLETION', '{"min_score": 0, "min_correct": 1}', 1, 0.1000, 5),
('Hoàn Thành Quiz Tốt', 'QUIZ_GOOD_DROP', 'QUIZ_COMPLETION', '{"min_score": 70, "min_correct": 7}', 2, 0.0800, 3),
('Hoàn Thành Quiz Xuất Sắc', 'QUIZ_EXCELLENT_DROP', 'QUIZ_COMPLETION', '{"min_score": 90, "min_correct": 9}', 3, 0.0500, 2),

-- Perfect Score Rules
('Điểm Tuyệt Đối Cơ Bản', 'PERFECT_BASIC', 'PERFECT_SCORE', '{"score": 100, "total_questions": 10}', 2, 1.0000, 1),
('Điểm Tuyệt Đối Khó', 'PERFECT_HARD', 'PERFECT_SCORE', '{"score": 100, "total_questions": 15}', 4, 1.0000, 1),

-- Streak Achievement Rules
('Chuỗi Thắng 5', 'STREAK_5', 'STREAK_ACHIEVEMENT', '{"streak_count": 5}', 1, 0.3000, 3),
('Chuỗi Thắng 10', 'STREAK_10', 'STREAK_ACHIEVEMENT', '{"streak_count": 10}', 2, 0.5000, 2),
('Chuỗi Thắng 15', 'STREAK_15', 'STREAK_ACHIEVEMENT', '{"streak_count": 15}', 3, 0.8000, 1),

-- Level Up Rules
('Lên Cấp Mốc 5', 'LEVEL_UP_5', 'LEVEL_UP', '{"level_milestone": 5}', 1, 1.0000, NULL),
('Lên Cấp Mốc 10', 'LEVEL_UP_10', 'LEVEL_UP', '{"level_milestone": 10}', 2, 1.0000, NULL),
('Lên Cấp Mốc 15', 'LEVEL_UP_15', 'LEVEL_UP', '{"level_milestone": 15}', 4, 1.0000, NULL),
('Lên Cấp Mốc 20', 'LEVEL_UP_20', 'LEVEL_UP', '{"level_milestone": 20}', 5, 1.0000, NULL),

-- Daily Login Rules
('Đăng Nhập Hàng Ngày', 'DAILY_LOGIN', 'DAILY_LOGIN', '{"consecutive_days": 1}', 1, 0.2000, 1),
('Đăng Nhập 7 Ngày', 'WEEKLY_LOGIN', 'DAILY_LOGIN', '{"consecutive_days": 7}', 2, 1.0000, NULL),
('Đăng Nhập 30 Ngày', 'MONTHLY_LOGIN', 'DAILY_LOGIN', '{"consecutive_days": 30}', 8, 1.0000, NULL);

-- Summary
SELECT CONCAT('Egg system initialized successfully! Total egg types: ', COUNT(*)) FROM "EggTypes";
SELECT CONCAT('Total egg rewards configured: ', COUNT(*)) FROM "EggRewards";
SELECT CONCAT('Total drop rules configured: ', COUNT(*)) FROM "EggDropRules";
