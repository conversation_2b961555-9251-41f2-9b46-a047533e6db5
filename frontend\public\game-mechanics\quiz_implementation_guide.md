# 🏁 QUIZ RACING - HƯỚNG DẪN TRIỂN KHAI HOÀN CHỈNH

## 🎯 Mục <PERSON>i<PERSON> & <PERSON><PERSON> Trí Trong Hệ Thống

**Quiz Racing là trung tâm của toàn bộ platform Synlearnia:**

- **Nguồn XP duy nhất:** 100% XP đến từ quiz performance
- **Nguồn currency chính:** Thu nhập SynCoin và Kristal
- **Trung tâm skills:** Sử dụng 17 skills từ icon-skill-pack
- **Engine thu thập:** Tất cả 24 loại eggs từ quiz performance
- **Hệ thống xã hội:** Real-time interaction với emoji system

> **Dependencies:** Quiz Racing kết nối với tất cả 5 systems khác: [01-Progression](01-progression-system.md), [02-Customization](02-customization-system.md), [03-Collection](03-collection-system.md), [04-Social](04-social-system.md), [05-Economy](05-economy-system.md)

## 📋 TỔNG QUAN KIẾN TRÚC HỆ THỐNG

```mermaid
graph TB
    subgraph "CLIENT - Student Interface"
        UI[Quiz Racing UI]
        Skills[17 Skills System]
        Energy[Energy Management]
        Leaderboard[Real-time Rankings]
        Social[Emoji Interactions]
    end

    subgraph "SERVER - Game Engine"
        API[REST APIs]
        WS[WebSocket Real-time]
        Rewards[Reward Calculator]
        GameLogic[Quiz Game Logic]
        DB[(PostgreSQL)]
        Cache[(Redis Cache)]
    end

    subgraph "INTEGRATIONS - Game Systems"
        Progression[10-Tier System]
        Collection[24 Egg Types]
        Economy[SynCoin + Kristal]
        Customization[30 Avatars + 6 Frames]
    end

    UI --> Skills
    Skills --> Energy
    Energy --> WS
    WS --> GameLogic
    GameLogic --> Rewards
    Rewards --> Progression
    Rewards --> Collection
    Rewards --> Economy
    Economy --> Customization
    Cache --> WS
    DB --> GameLogic
```

---

## 🔄 QUY TRÌNH QUIZ RACING - 7 MODULE TÍCH HỢP

### MODULE 1: KHỞI TẠO & THIẾT LẬP SKILLS

```mermaid
flowchart TD
    A[🎮 Bắt đầu Quiz Racing] --> B[Tải danh sách 17 skills có sẵn]
    B --> C[Chọn 4 skills từ 5 categories]
    C --> D[Xác thực loadout hợp lệ]
    D --> E[Nhập mã PIN phòng]
    E --> F[Tham gia phòng qua WebSocket]
    F --> G[Hiển thị lobby với skill loadouts]
    G --> H[Chờ giáo viên khởi động]
    H --> I[Khởi tạo giao diện đua với thanh energy]

    style A fill:#e1f5fe
    style I fill:#c8e6c9
```

**17 Skills trong 5 Categories:**

**🗡️ Attack Skills (4):** `blackhole`, `steal`, `break`, `slow`
**🛡️ Defense Skills (3):** `shield`, `lock`, `cleanse`  
**💎 Burst Skills (5):** `double`, `lucky`, `triple`, `perfect`, `quintuple`
**🎲 Special Skills (3):** `swap`, `dice`, `energy`
**🏆 Ultimate Skills (2):** `king`, `phoenix`

**Loadout Requirements:**

- Mỗi player chọn đúng 4 skills từ 17 options
- Không giới hạn category (có thể chọn 4 attack, hoặc mix)
- Skills phải owned (purchased từ Economy System)
- Loadout locked sau khi join room

**Các bước thực hiện:**

- Load skill inventory từ player database
- Display skill selection UI với categories
- Validate 4-skill selection
- Save loadout cho quiz session
- Sync loadouts trong lobby

### MODULE 2: LUỒNG TÍNH ĐIỂM & ENERGY

```mermaid
flowchart TD
    A[📝 Nhận câu hỏi mới] --> B[Hiển thị câu hỏi - Không giới hạn thời gian]
    B --> C{Hành động của người chơi}

    C -->|Trả lời| D[Gửi đáp án + timestamp]
    C -->|Bỏ qua câu| E[Skip - 0 điểm]
    C -->|Hết thời gian quiz| F[Force end - lưu progress]

    D --> G[Kiểm tra đáp án]
    G --> H{Đúng hay sai?}

    H -->|✅ ĐÚNG| I[Tính điểm cơ bản theo độ khó]
    H -->|❌ SAI| J[0 điểm + reset streak]

    I --> K{Đang ở vòng nào?}
    K -->|Vòng 1| L[Full scoring với bonuses]
    K -->|Vòng 2+| M[50% điểm cơ bản - NO bonuses]

    L --> N[Tính speed bonus: tối đa +50%]
    N --> O[Tính streak bonus: +10,+15,+20...]
    O --> P[Cập nhật energy: +20% base + bonuses]

    M --> Q[Chỉ 50% điểm cơ bản]
    Q --> P

    J --> R[Reset streak = 0]
    R --> S[Energy KHÔNG reset]

    E --> T[0 điểm, no energy gain]
    P --> U[Check energy = 100%?]
    S --> U
    T --> U

    style A fill:#fff3e0
    style P fill:#c8e6c9
    style J fill:#ffcdd2
    style F fill:#ff9800
```

**Quy tắc tính điểm chi tiết:**

**🎯 Điểm cơ bản theo độ khó:**

- **Dễ:** 60 điểm (vòng 1) → 30 điểm (vòng 2+)
- **Trung bình:** 100 điểm (vòng 1) → 50 điểm (vòng 2+)
- **Khó:** 140 điểm (vòng 1) → 70 điểm (vòng 2+)

**⚡ Speed bonus (chỉ vòng 1):**

- Trả lời trong 5s đầu: tối đa +50% điểm cơ bản
- Linear scaling: 5s = +50%, 10s = +25%, 15s+ = 0%

**🔥 Streak bonus (chỉ vòng 1):**

- Streak 1: +10 điểm
- Streak 2: +15 điểm
- Streak 3: +20 điểm
- Streak n: +(10 + n×5) điểm

**⚡ Energy system:**

- **Đúng:** +20% energy
- **Speed bonus:** +10% energy thêm
- **Streak bonus:** +5% energy thêm
- **Sai:** Chỉ reset streak, KHÔNG reset energy
- **100% energy:** Trigger skill selection

### MODULE 3: HỆ THỐNG 17 SKILLS CHIẾN THUẬT

```mermaid
flowchart TD
    A[⚡ Energy = 100%] --> B[Server random 1/4 skill đã trang bị]
    B --> C[Hiển thị skill available]
    C --> D{Player action}

    D -->|Use now| E[Chọn target nếu cần]
    D -->|Save for later| F[Keep skill ready]

    E --> G{Skill category?}
    G -->|Attack| H[Target: Leader/Specific player]
    G -->|Defense| I[Self-target auto]
    G -->|Burst| J[Self-effect với risk]
    G -->|Special| K[Auto-effect hoặc random target]
    G -->|Ultimate| L[Self với powerful effects]

    H --> M[Execute skill effect]
    I --> M
    J --> M
    K --> M
    L --> M

    M --> N[Broadcast skill usage]
    N --> O[Apply effects to targets]
    O --> P[Reset energy to 0%]

    F --> Q[Continue game với skill ready]
    P --> Q

    style A fill:#fff3e0
    style M fill:#e8f5e8
    style P fill:#ffcdd2
```

**17 Skills Chi Tiết:**

**🗡️ ATTACK SKILLS (Tấn công đối thủ):**

1. **Blackhole (Hố Đen)** - 150 SynCoin

   - Mục tiêu: Leader hiện tại
   - Hiệu ứng: Leader nhận 0 điểm trong 3 câu tiếp theo
   - Dùng khi: Ngăn leader tạo khoảng cách xa

2. **Steal (Cướp Điểm)** - 120 SynCoin

   - Mục tiêu: Player đứng ngay trên bạn
   - Hiệu ứng: Lấy 50% điểm của họ nếu họ trả lời đúng
   - Rủi ro: Mất 10% điểm của bạn nếu họ trả lời sai

3. **Break (Phá Streak)** - 100 SynCoin

   - Mục tiêu: Player có streak cao nhất (≥3)
   - Hiệu ứng: Reset streak của họ về 0
   - Điều kiện: Chỉ hoạt động khi có ai đó có streak ≥3

4. **Slow (Làm Chậm)** - 160 SynCoin
   - Mục tiêu: Tất cả players khác
   - Hiệu ứng: Giảm thời gian speed bonus từ 5s xuống 3s trong 2 câu
   - Thời gian: 2 câu hỏi

**🛡️ DEFENSE SKILLS (Phòng thủ bản thân):**

5. **Shield (Khiên)** - 150 SynCoin

   - Hiệu ứng: Miễn nhiễm TẤT CẢ attack skills trong 45 giây
   - Lưu ý: KHÔNG chặn được Special skills (Swap, Dice)

6. **Lock (Khóa Streak)** - 110 SynCoin

   - Hiệu ứng: Streak không thể bị reset trong 4 câu tiếp theo
   - Yêu cầu: Phải có streak ≥2 mới dùng được

7. **Cleanse (Thanh Lọc)** - 130 SynCoin
   - Hiệu ứng: Loại bỏ tất cả debuffs + hồi 50% energy
   - Dùng khi: Chống lại nhiều attack effects

**💎 BURST SKILLS (Rủi ro cao/Phần thưởng lớn):**

8. **Double (Nhân Đôi)** - 180 SynCoin

   - Hiệu ứng: Câu tiếp theo ×2 điểm nếu đúng, -25% tổng điểm nếu sai
   - Rủi ro: Trung bình (⭐⭐⭐)

9. **Lucky (May Mắn)** - 160 SynCoin

   - Hiệu ứng: 60% cơ hội trả lời sai vẫn được điểm (không có streak)
   - Rủi ro: Thấp (⭐)

10. **Triple (Nhân Ba)** - 50 Kristal

    - Hiệu ứng: Câu tiếp theo ×3 điểm nếu đúng, -40% tổng + reset streak nếu sai
    - Rủi ro: Cao (⭐⭐⭐⭐)

11. **Perfect (Hoàn Hảo)** - 40 Kristal

    - Hiệu ứng: Câu tiếp theo luôn được 100% điểm + tăng streak
    - Rủi ro: Không có (An toàn)

12. **Quintuple (Nhân Năm)** - 80 Kristal
    - Hiệu ứng: Câu tiếp theo ×5 điểm nếu đúng, -50% tổng + reset streak nếu sai
    - Rủi ro: Tối đa (⭐⭐⭐⭐⭐)

**🎲 SPECIAL SKILLS (Bỏ qua phòng thủ):**

13. **Swap (Hoán Đổi)** - 200 SynCoin

    - Hiệu ứng: Hoán đổi tổng điểm của bạn với 1 player ngẫu nhiên
    - Rủi ro: Có thể hoán đổi với người có điểm thấp hơn
    - Lưu ý: Bỏ qua tất cả phòng thủ

14. **Dice (Xúc Xắc)** - 140 SynCoin

    - Hiệu ứng: Tung xúc xắc 1-6 để nhận lợi ích ngẫu nhiên:
      - 1-2: +30% điểm câu tiếp theo
      - 3-4: +40% energy
      - 5: ×2 streak hiện tại
      - 6: Miễn nhiễm 25 giây
    - Rủi ro: Không có (tất cả kết quả đều tích cực)

15. **Energy (Hồi Energy)** - 190 SynCoin
    - Hiệu ứng: +100% energy (cho phép combo skills)
    - Dùng khi: Chuẩn bị cho skill khác ngay lập tức

**🏆 ULTIMATE SKILLS (Thay đổi cuộc chơi):**

16. **King (Vua Quiz)** - 150 Kristal

    - Hiệu ứng: 3 câu tiếp theo có TẤT CẢ:
      - ×2 điểm
      - Luôn được điểm (không thể sai)
      - Miễn nhiễm tấn công
    - Thời gian: 3 câu hỏi

17. **Phoenix (Hồi Sinh)** - 200 Kristal
    - Hiệu ứng: Nếu trong top 3 cuối → nhảy lên top 3 đầu + ×2 điểm trong 2 câu
    - Rủi ro: Nếu không trong top 3 cuối → mất 30% điểm
    - Rủi ro: Cao (⭐⭐⭐)

**Cơ Chế Skills:**

- **Dùng một lần:** Mỗi skill chỉ dùng được 1 lần trong 1 quiz
- **Chi phí energy:** Tất cả skills tốn 100% energy để kích hoạt
- **Chọn ngẫu nhiên:** Server ngẫu nhiên đưa ra 1 trong 4 skills đã trang bị
- **Thời điểm chiến thuật:** Players có thể save skills cho những thời điểm quan trọng
- **Bỏ qua phòng thủ:** Special skills bỏ qua Shield protection

### MODULE 4: QUẢN LÝ THỜI GIAN & TIMER

```mermaid
flowchart TD
    A[🕐 Quiz bắt đầu] --> B[Thiết lập timer tổng cho quiz]
    B --> C[Hiển thị đồng hồ đếm ngược trên UI]
    C --> D{Kiểm tra trạng thái timer}

    D -->|Còn thời gian| E[Tiếp tục gameplay bình thường]
    D -->|Còn 5 phút| F[Thông báo cảnh báo]
    D -->|Hết thời gian| G[Buộc kết thúc quiz]

    E --> H[Cập nhật hiển thị timer trên UI]
    F --> I[Hiển thị cảnh báo khẩn cấp]
    G --> J[Lưu tiến trình hiện tại]

    H --> C
    I --> C
    J --> K[Tính toán phần thưởng dựa trên tiến trình]
    K --> L[Hiển thị kết quả cuối cùng]

    style A fill:#fff3e0
    style G fill:#ff5722
    style L fill:#c8e6c9
```

**Tính Năng Timer System:**

- **Timer toàn quiz:** Giới hạn thời gian tổng do giáo viên đặt
- **Timer từng câu:** Các câu hỏi riêng lẻ KHÔNG có giới hạn thời gian
- **Hệ thống cảnh báo:** Báo động players 5 phút trước deadline
- **Tự động hoàn thành:** Buộc kết thúc khi hết thời gian
- **Bảo toàn tiến trình:** Lưu tất cả câu trả lời và tiến trình khi hết thời gian
- **Phần thưởng công bằng:** Tính toán dựa trên số câu đã hoàn thành

**Ghi Chú Quan Trọng:**

- **Từng câu hỏi:** Không có áp lực vội vàng, học sinh có thể suy nghĩ kỹ
- **Deadline tổng thể:** Tạo cảm giác khẩn cấp cho việc hoàn thành quiz
- **Tốc độ linh hoạt:** Học sinh tự kiểm soát tốc độ trả lời
- **An toàn:** Không mất tiến trình do giới hạn thời gian

### MODULE 5: BẢNG XẾP HẠNG & TƯƠNG TÁC XÃ HỘI

```mermaid
flowchart TD
    A[📊 Cập nhật điểm số] --> B[Tính lại thứ hạng]
    B --> C[Áp dụng hiệu ứng skills đang hoạt động]
    C --> D[Cập nhật trạng thái leaderboard]
    D --> E[Phát sóng tới tất cả clients]
    E --> F[Animation cập nhật UI]

    G[😀 Tương tác emoji] --> H{Ngữ cảnh emoji}
    H -->|Trước game| I[Hiển thị sự tự tin]
    H -->|Trong quiz| J[Phản ứng thời gian thực]
    H -->|Sau trả lời| K[Emoji khích lệ]

    I --> L[Định vị xã hội]
    J --> L
    K --> L
    L --> M[Xây dựng cộng đồng]

    N[🌟 Sự kiện toàn cục] --> O{Loại sự kiện}
    O -->|Golden Question| P[×2 điểm cho câu này]
    O -->|Speed Zone| Q[×2 speed bonus trong 3 câu]

    P --> R[Thông báo sự kiện]
    Q --> R
    R --> S[Chỉ báo sự kiện trên UI]

    style A fill:#fff3e0
    style E fill:#e3f2fd
    style R fill:#fff8e1
    style M fill:#e8f5e8
```

**Tính Năng Real-time:**

**📊 Bảng Xếp Hạng Động:**

- Cập nhật vị trí ngay lập tức sau mỗi câu trả lời
- Animation UI mượt mà cho thay đổi vị trí
- Chỉ báo hiệu ứng skills trên bảng xếp hạng
- Hiển thị thanh energy cho tất cả players

**😀 Hệ Thống Emoji (100+ emojis):**

- **Mở khóa theo tier:** Wood tier → 3 emojis, Master tier → 12+ emojis
- **Sử dụng theo ngữ cảnh:** Tự tin trước game, phản ứng trong quiz, ăn mừng sau game
- **Giao tiếp an toàn:** Chỉ emoji để tránh quấy rối văn bản
- **Địa vị xã hội:** Emoji premium từ eggs tier cao/mua hàng

**🌟 Sự Kiện Toàn Cục:**

- **Golden Question:** Câu hỏi ngẫu nhiên nhận ×2 điểm multiplier
- **Speed Zone:** Tăng cường speed bonuses trong 3 câu tiếp theo
- **Community triggers:** Sự kiện đặc biệt dựa trên hiệu suất tập thể

**Tích Hợp Xã Hội:**

- **Chia sẻ thành tích:** Ăn mừng real-time các cột mốc quan trọng
- **Nhận diện tier:** Chỉ báo trạng thái trực quan cho tiến trình
- **Trưng bày bộ sưu tập:** Hiển thị avatars/frames hiếm
- **Củng cố tích cực:** Emoji khích lệ xây dựng cộng đồng

### MODULE 6: QUẢN LÝ VÒNG & LỌC CÂU HỎI

```mermaid
flowchart TD
    A[🔄 Hoàn thành vòng] --> B{Vòng hiện tại?}

    B -->|Vòng 1| C[Tất cả câu hỏi theo thứ tự]
    B -->|Vòng 2+| D[Áp dụng lọc câu hỏi]

    C --> E{Tất cả câu vòng 1 xong?}
    E -->|Chưa| F[Câu tiếp theo theo thứ tự]
    E -->|Rồi| G[Chuẩn bị vòng tiếp theo]

    D --> H[Lọc với logic nâng cao]
    H --> I{Còn câu hỏi phù hợp?}
    I -->|Có| J[Bắt đầu vòng đã lọc]
    I -->|Không| K[Kết thúc quiz - không còn câu]

    G --> H
    F --> L[Tiếp tục vòng lặp game]
    J --> L
    K --> M[Hiển thị kết quả cuối cùng]

    style A fill:#fff3e0
    style K fill:#ffcdd2
    style M fill:#c8e6c9
```

**Logic Lọc Câu Hỏi Nâng Cao (Vòng 2+):**

**✅ PHÙ HỢP (Sẽ xuất hiện lại):**

- **Chưa bao giờ thử:** Câu hỏi chưa làm ở bất kỳ vòng nào
- **Sai một lần:** Câu hỏi trả lời sai đúng 1 lần

**❌ BỊ LỌC BỎ (Sẽ không xuất hiện):**

- **Đã trả lời đúng:** Bất kỳ câu nào đã trả lời đúng ở vòng trước
- **Sai 2+ lần:** Câu hỏi có 2 lần trả lời sai trở lên

**Hệ Thống Học Lại Thông Minh:**

- **Chính sách cơ hội thứ hai:** Học sinh được đúng 1 lần thử lại cho câu trả lời sai
- **Nhận diện thành thạo:** Câu trả lời đúng chứng minh đã hiểu
- **Ôn tập tập trung:** Chỉ những khái niệm khó hiểu mới quay lại để ôn
- **Đảm bảo hoàn thành:** Quiz kết thúc khi không còn câu nào đáp ứng tiêu chí

**Ví Dụ Tiến Trình Vòng:**

- **Vòng 1:** Câu hỏi 1-15 (tất cả có sẵn)
- **Học sinh trả lời:** 10 đúng, 5 sai
- **Vòng 2:** Chỉ 5 câu sai quay lại
- **Học sinh trả lời:** 3 đúng, 2 sai (lần nữa)
- **Vòng 3:** Chỉ 2 câu sai 2 lần - NHỮNG CÂU NÀY BỊ LOẠI
- **Quiz kết thúc:** Không còn câu hỏi phù hợp

**Lợi Ích Giáo Dục:**

- **Học thích ứng:** Tập trung vào những điểm yếu
- **Dựa trên thành thạo:** Không lặp lại những gì đã học
- **Xây dựng tự tin:** Thành công loại bỏ sự lặp lại
- **Hiệu quả:** Tối đa hóa thời gian học cho nhu cầu thực tế

### MODULE 7: REWARD CALCULATION & COMPLETION

```mermaid
flowchart TD
    A[🏁 Quiz completed] --> B[Show top 3 podium]
    B --> C[Calculate comprehensive rewards]

    C --> D[Tính Toán XP]
    D --> E[Tính Toán SynCoin]
    E --> F[Tính Toán Kristal]
    F --> G[Xác Định Rơi Egg]

    D --> D1[Cơ sở: 50 + đúng×10 + điểm÷50 + bonus ranking]
    D1 --> D2[Bonus hiệu suất: Perfect/Speed/Streak]
    D2 --> D3[Áp dụng tier multiplier: 1.0x-2.0x]

    E --> E1[Cơ sở: 25 + đúng×5 + điểm÷100 + bonus ranking]
    E1 --> E2[Bonus hiệu suất: Perfect/Speed/Streak/Skills]
    E2 --> E3[Áp dụng tier multiplier: 1.0x-2.0x]

    F --> F1[Cơ sở: 0 + chỉ bonus ranking]
    F1 --> F2[Bonus hiệu suất: Perfect/Speed/Triple Crown]
    F2 --> F3[Áp dụng tier multiplier: 1.0x-2.0x]

    G --> G1{Tier hiệu suất?}
    G1 -->|50% cuối| H1[Cơ hội Basic Egg]
    G1 -->|Top 30%| H2[Cơ hội Royal Egg]
    G1 -->|Top 10%| H3[Cơ hội Legendary Egg]
    G1 -->|Top 5%| H4[Cơ hội Dragon Egg]
    G1 -->|Top 1%| H5[Cơ hội Rainbow Egg]
    G1 -->|Hạng #1| H6[Cơ hội Dominus Egg]

    G1 -->|Điểm Hoàn Hảo| I1[Bonus Angel Egg]
    G1 -->|Streak 15+| I2[Bonus Ice Egg]
    G1 -->|Triple Crown| I3[Mythical đảm bảo]

    H1 --> J[Tổng hợp tất cả phần thưởng]
    H2 --> J
    H3 --> J
    H4 --> J
    H5 --> J
    H6 --> J
    I1 --> J
    I2 --> J
    I3 --> J

    J --> K[Cập nhật tiến trình player]
    K --> L[Kiểm tra level up và thăng tier]
    L --> M[Mở khóa avatars/achievements mới]
    M --> N[Lưu vào database]
    N --> O[Hiển thị kết quả cuối với ngữ cảnh tier]

    style A fill:#fff3e0
    style O fill:#c8e6c9
    style C fill:#ffd54f
```

**Hệ Thống Phần Thưởng Toàn Diện:**

**💫 Phần Thưởng XP (Nhiên liệu tiến bộ):**

```
XP Cơ Sở = 50 + (Số_Câu_Đúng × 10) + (Tổng_Điểm ÷ 50) + Bonus_XP_Ranking

Bonus XP Ranking:
- Hạng 1: +100 XP
- Hạng 2: +75 XP
- Hạng 3: +50 XP
- Hạng 4-6: +25 XP
- Hạng 7-8: +10 XP

Bonus XP Hiệu Suất:
- Quiz Hoàn Hảo (15/15): +150 XP
- Speed Master (TB <8s): +100 XP
- Streak Master (≥10): +75 XP
- Skill Mastery: +50 XP
- Comeback King: +60 XP

Tier Multipliers (áp dụng cho tổng cuối):
Wood-Master: ×1.0 đến ×2.0 scaling
```

**🪙 Phần Thưởng SynCoin (Tiền tệ chính):**

```
SynCoin Cơ Sở = 25 + (Số_Câu_Đúng × 5) + (Tổng_Điểm ÷ 100) + Bonus_Ranking

Bonus SynCoin Ranking:
- Hạng 1: +50, Hạng 2: +35, Hạng 3: +20, Hạng 4-8: +10

Bonus SynCoin Hiệu Suất:
- Quiz Hoàn Hảo: +50
- Speed Demon: +25
- Streak Master: +35
- Strategic Skills: +20
- Comeback King: +40

Cùng tier multipliers như XP
```

**💎 Phần Thưởng Kristal (Tiền tệ cao cấp):**

```
Kristal Cơ Sở = 0 + Bonus_Kristal_Ranking (chỉ top performers)

Ranking Kristal:
- Hạng 1: +5, Hạng 2: +3, Hạng 3: +2, Hạng 4-5: +1

Bonus Hiệu Suất Cao Cấp:
- Quiz Hoàn Hảo: +8 Kristal
- Speed Master: +5 Kristal
- Triple Crown (Hoàn Hảo+Hạng 1+Speed): +15 Kristal (thay thế cá nhân)

Cùng tier multipliers
```

**🥚 24 Loại Eggs - Dựa Trên Hiệu Suất:**

**Eggs Hiệu Suất Cơ Bản:**

- **basic-egg:** 50% cuối + cột mốc level + shop 100 SynCoin
- **cracked-egg:** Ranking 30-70% + cột mốc + shop 150 SynCoin
- **royal-egg:** Top 30% + cột mốc + shop 50 Kristal
- **legendary-egg:** Top 10% + cột mốc Platinum + shop 80 Kristal
- **dragon-egg:** Top 5% + cột mốc Sapphire + shop 120 Kristal
- **mythical-egg:** Top 3% + cột mốc Ruby + shop 200 Kristal
- **rainbow-egg:** Top 1% + cột mốc Amethyst (không có shop)
- **dominus-egg:** Hạng #1 + cột mốc Master + shop 500 Kristal

**Eggs Điều Kiện Đặc Biệt:**

- **angel-egg:** Điểm số hoàn hảo (100%) + shop Gold tier
- **demon-egg:** Comeback (10% cuối → top 10%) + shop Gold tier
- **ice-egg:** Streak trả lời 15+ + shop 100 Kristal
- **secret-egg:** Thành tích ẩn (không có shop)
- **black-hole-egg:** 0.01% cosmic hiếm (không có shop)
- Cộng 11 themed eggs khác cho các thành tích cụ thể

**Tích Hợp Với Các Hệ Thống Khác:**

**Tiến Trình Level:** XP thúc đẩy hệ thống 10-tier (Wood → Master)
**Mở Khóa Avatar:** Levels mở khóa 30 avatars từ `avatar-animal-pack`
**Tích Hợp Kinh Tế:** SynCoin/Kristal làm nhiên liệu cho mua sắm shop
**Xây Dựng Bộ Sưu Tập:** Eggs cung cấp avatars/frames/emojis cho customization
**Địa Vị Xã Hội:** Hiệu suất ảnh hưởng đến tiến trình tier và nhận diện xã hội

---

## 📡 CÁC SỰ KIỆN WEBSOCKET CẦN XỬ LÝ

### Client gửi lên Server:

- **join-room**: Tham gia phòng với PIN và danh sách kỹ năng
- **submit-answer**: Gửi đáp án kèm thời gian trả lời (để tính thưởng tốc độ)
- **use-skill**: Sử dụng kỹ năng với mục tiêu (nếu có)
- **skip-question**: Bỏ qua câu hỏi hiện tại
- **player-ready**: Báo sẵn sàng cho vòng tiếp theo
- **heartbeat**: Ping để duy trì kết nối

### Server gửi xuống Client:

- **quiz-started**: Bắt đầu quiz với danh sách câu hỏi và thời gian tổng
- **new-question**: Câu hỏi mới (không có thời gian giới hạn)
- **question-result**: Kết quả câu trả lời và điểm nhận được
- **leaderboard-update**: Cập nhật bảng xếp hạng thời gian thực
- **skill-used**: Thông báo kỹ năng được sử dụng
- **global-event**: Sự kiện toàn cục (câu vàng, tăng tốc)
- **energy-update**: Cập nhật thanh năng lượng
- **round-complete**: Hoàn thành vòng, chuẩn bị vòng tiếp theo
- **quiz-time-warning**: Cảnh báo thời gian còn lại của bài quiz
- **quiz-time-expired**: Hết thời gian bài quiz - kết thúc tự động
- **quiz-complete**: Kết thúc quiz với kết quả cuối cùng

---

## 🏗️ CẤU TRÚC DATABASE CẦN THIẾT

### Bảng chính cần có:

- **quiz_sessions**: Lưu thông tin phiên quiz (ID, PIN, giáo viên, trạng thái)
- **player_answers**: Lưu câu trả lời của từng người chơi
- **skill_usage**: Lưu lịch sử sử dụng kỹ năng
- **player_stats**: Thống kê điểm số, streak, năng lượng theo thời gian thực
- **global_events**: Lưu các sự kiện toàn cục đã xảy ra

### Dữ liệu cần theo dõi:

- Trạng thái câu hỏi của từng người chơi (chưa làm/đúng/sai/số lần sai)
- Điểm số và thứ hạng theo thời gian thực
- Chuỗi thắng hiện tại và dài nhất
- Năng lượng và kỹ năng khả dụng
- Hiệu ứng đang hoạt động trên mỗi người chơi
- Thời gian bắt đầu và thời gian còn lại của bài quiz
- Trạng thái kết nối của người chơi

---

_Tài liệu này cung cấp roadmap rõ ràng để triển khai từng module một cách độc lập và tích hợp dần vào hệ thống hoàn chỉnh._
