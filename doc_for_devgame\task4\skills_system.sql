-- =====================================================
-- TASK 4.1: SKILLS SYSTEM DATABASE SCHEMA
-- =====================================================
-- Hệ thống 17 kỹ năng cho Quiz Racing với 5 categories
-- Tích hợp với Economy System và Real-time Quiz Racing

-- =====================================================
-- 1. SKILLS MASTER TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS "Skills" (
    "skill_id" SERIAL PRIMARY KEY,
    "skill_name" VARCHAR(100) NOT NULL UNIQUE,
    "skill_code" VARCHAR(50) NOT NULL UNIQUE, -- e.g., 'blackhole', 'steal'
    "skill_icon" VARCHAR(100) NOT NULL, -- Icon filename from icon-skill-pack
    "category" VARCHAR(20) NOT NULL CHECK ("category" IN ('ATTACK', 'DEFENSE', 'BURST', 'SPECIAL', 'ULTIMATE')),
    "tier" VARCHAR(10) NOT NULL CHECK ("tier" IN ('D', 'C', 'B', 'A', 'S')),
    "cost_type" VARCHAR(10) NOT NULL CHECK ("cost_type" IN ('SYNCOIN', 'KRISTAL')),
    "cost_amount" INTEGER NOT NULL,
    "description" TEXT NOT NULL,
    "effect_description" TEXT NOT NULL,
    "target_type" VARCHAR(20) NOT NULL CHECK ("target_type" IN ('SELF', 'LEADER', 'SPECIFIC_PLAYER', 'ALL_OTHERS', 'HIGHEST_STREAK', 'PLAYER_ABOVE')),
    "duration_type" VARCHAR(20) DEFAULT 'INSTANT' CHECK ("duration_type" IN ('INSTANT', 'QUESTIONS', 'PERMANENT')),
    "duration_value" INTEGER DEFAULT 1, -- Number of questions if duration_type = 'QUESTIONS'
    "cooldown_questions" INTEGER DEFAULT 0, -- Cooldown in questions
    "risk_factor" DECIMAL(3,2) DEFAULT 0.00, -- Risk percentage (0.00 to 1.00)
    "success_rate" DECIMAL(3,2) DEFAULT 1.00, -- Success rate (0.00 to 1.00)
    "is_active" BOOLEAN DEFAULT TRUE,
    "created_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 2. USER SKILL INVENTORY
-- =====================================================

CREATE TABLE IF NOT EXISTS "UserSkills" (
    "user_skill_id" SERIAL PRIMARY KEY,
    "user_id" INTEGER NOT NULL,
    "skill_id" INTEGER NOT NULL,
    "purchased_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "purchase_cost" INTEGER NOT NULL,
    "purchase_currency" VARCHAR(10) NOT NULL CHECK ("purchase_currency" IN ('SYNCOIN', 'KRISTAL')),
    "times_used" INTEGER DEFAULT 0,
    "total_success" INTEGER DEFAULT 0,
    "total_failure" INTEGER DEFAULT 0,
    "last_used_at" TIMESTAMP NULL,
    "is_equipped" BOOLEAN DEFAULT FALSE, -- For current loadout
    
    FOREIGN KEY ("user_id") REFERENCES "Users"("user_id") ON DELETE CASCADE,
    FOREIGN KEY ("skill_id") REFERENCES "Skills"("skill_id") ON DELETE CASCADE,
    UNIQUE("user_id", "skill_id") -- User can only own each skill once
);

-- =====================================================
-- 3. QUIZ SESSION SKILL LOADOUTS
-- =====================================================

CREATE TABLE IF NOT EXISTS "QuizSkillLoadouts" (
    "loadout_id" SERIAL PRIMARY KEY,
    "quiz_session_id" VARCHAR(100) NOT NULL, -- Quiz session identifier
    "user_id" INTEGER NOT NULL,
    "skill_slot_1" INTEGER NOT NULL,
    "skill_slot_2" INTEGER NOT NULL,
    "skill_slot_3" INTEGER NOT NULL,
    "skill_slot_4" INTEGER NOT NULL,
    "created_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY ("user_id") REFERENCES "Users"("user_id") ON DELETE CASCADE,
    FOREIGN KEY ("skill_slot_1") REFERENCES "Skills"("skill_id"),
    FOREIGN KEY ("skill_slot_2") REFERENCES "Skills"("skill_id"),
    FOREIGN KEY ("skill_slot_3") REFERENCES "Skills"("skill_id"),
    FOREIGN KEY ("skill_slot_4") REFERENCES "Skills"("skill_id"),
    UNIQUE("quiz_session_id", "user_id") -- One loadout per user per quiz
);

-- =====================================================
-- 4. SKILL USAGE HISTORY
-- =====================================================

CREATE TABLE IF NOT EXISTS "SkillUsageHistory" (
    "usage_id" SERIAL PRIMARY KEY,
    "quiz_session_id" VARCHAR(100) NOT NULL,
    "user_id" INTEGER NOT NULL, -- Skill user
    "skill_id" INTEGER NOT NULL,
    "target_user_id" INTEGER NULL, -- Target player (if applicable)
    "question_number" INTEGER NOT NULL,
    "energy_level" INTEGER NOT NULL, -- Energy level when skill was used
    "execution_result" VARCHAR(20) NOT NULL CHECK ("execution_result" IN ('SUCCESS', 'FAILED', 'BLOCKED', 'INVALID_TARGET')),
    "effect_data" JSONB NULL, -- Store skill effect details
    "points_affected" INTEGER DEFAULT 0, -- Points gained/lost due to skill
    "used_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY ("user_id") REFERENCES "Users"("user_id") ON DELETE CASCADE,
    FOREIGN KEY ("skill_id") REFERENCES "Skills"("skill_id") ON DELETE CASCADE,
    FOREIGN KEY ("target_user_id") REFERENCES "Users"("user_id") ON DELETE SET NULL
);

-- =====================================================
-- 5. ACTIVE SKILL EFFECTS
-- =====================================================

CREATE TABLE IF NOT EXISTS "ActiveSkillEffects" (
    "effect_id" SERIAL PRIMARY KEY,
    "quiz_session_id" VARCHAR(100) NOT NULL,
    "affected_user_id" INTEGER NOT NULL,
    "skill_id" INTEGER NOT NULL,
    "caster_user_id" INTEGER NOT NULL, -- Who cast the skill
    "effect_type" VARCHAR(30) NOT NULL, -- e.g., 'BLACKHOLE', 'SHIELD', 'SLOW'
    "effect_data" JSONB NOT NULL, -- Effect parameters
    "questions_remaining" INTEGER NOT NULL, -- How many questions left
    "started_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "expires_at" TIMESTAMP NULL,
    "is_active" BOOLEAN DEFAULT TRUE,
    
    FOREIGN KEY ("affected_user_id") REFERENCES "Users"("user_id") ON DELETE CASCADE,
    FOREIGN KEY ("skill_id") REFERENCES "Skills"("skill_id") ON DELETE CASCADE,
    FOREIGN KEY ("caster_user_id") REFERENCES "Users"("user_id") ON DELETE CASCADE
);

-- =====================================================
-- 6. SKILL SHOP PURCHASES LOG
-- =====================================================

CREATE TABLE IF NOT EXISTS "SkillPurchaseHistory" (
    "purchase_id" SERIAL PRIMARY KEY,
    "user_id" INTEGER NOT NULL,
    "skill_id" INTEGER NOT NULL,
    "cost_amount" INTEGER NOT NULL,
    "cost_currency" VARCHAR(10) NOT NULL CHECK ("cost_currency" IN ('SYNCOIN', 'KRISTAL')),
    "user_balance_before" INTEGER NOT NULL,
    "user_balance_after" INTEGER NOT NULL,
    "purchased_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY ("user_id") REFERENCES "Users"("user_id") ON DELETE CASCADE,
    FOREIGN KEY ("skill_id") REFERENCES "Skills"("skill_id") ON DELETE CASCADE
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Skills table indexes
CREATE INDEX IF NOT EXISTS "idx_skills_category" ON "Skills"("category");
CREATE INDEX IF NOT EXISTS "idx_skills_tier" ON "Skills"("tier");
CREATE INDEX IF NOT EXISTS "idx_skills_cost" ON "Skills"("cost_type", "cost_amount");

-- UserSkills indexes
CREATE INDEX IF NOT EXISTS "idx_user_skills_user" ON "UserSkills"("user_id");
CREATE INDEX IF NOT EXISTS "idx_user_skills_equipped" ON "UserSkills"("user_id", "is_equipped");

-- QuizSkillLoadouts indexes
CREATE INDEX IF NOT EXISTS "idx_quiz_loadouts_session" ON "QuizSkillLoadouts"("quiz_session_id");
CREATE INDEX IF NOT EXISTS "idx_quiz_loadouts_user" ON "QuizSkillLoadouts"("user_id");

-- SkillUsageHistory indexes
CREATE INDEX IF NOT EXISTS "idx_skill_usage_session" ON "SkillUsageHistory"("quiz_session_id");
CREATE INDEX IF NOT EXISTS "idx_skill_usage_user" ON "SkillUsageHistory"("user_id");
CREATE INDEX IF NOT EXISTS "idx_skill_usage_skill" ON "SkillUsageHistory"("skill_id");

-- ActiveSkillEffects indexes
CREATE INDEX IF NOT EXISTS "idx_active_effects_session" ON "ActiveSkillEffects"("quiz_session_id", "is_active");
CREATE INDEX IF NOT EXISTS "idx_active_effects_user" ON "ActiveSkillEffects"("affected_user_id", "is_active");

-- =====================================================
-- INSERT 17 SKILLS DATA
-- =====================================================

-- 🗡️ ATTACK SKILLS (4 skills)
INSERT INTO "Skills" ("skill_name", "skill_code", "skill_icon", "category", "tier", "cost_type", "cost_amount", "description", "effect_description", "target_type", "duration_type", "duration_value", "risk_factor") VALUES
('Hố Đen', 'blackhole', 'blackhole.png', 'ATTACK', 'B', 'SYNCOIN', 150, 'Ngăn leader tạo khoảng cách xa', 'Leader nhận 0 điểm trong 3 câu tiếp theo', 'LEADER', 'QUESTIONS', 3, 0.00),
('Cướp Điểm', 'steal', 'steal.png', 'ATTACK', 'C', 'SYNCOIN', 120, 'Lấy điểm từ người trên bạn', 'Lấy 50% điểm nếu họ đúng, mất 10% nếu họ sai', 'PLAYER_ABOVE', 'INSTANT', 1, 0.10),
('Phá Streak', 'break', 'break.png', 'ATTACK', 'D', 'SYNCOIN', 100, 'Reset streak cao nhất về 0', 'Reset streak của người có streak ≥3', 'HIGHEST_STREAK', 'INSTANT', 1, 0.00),
('Làm Chậm', 'slow', 'slow.png', 'ATTACK', 'B', 'SYNCOIN', 160, 'Giảm thời gian speed bonus', 'Giảm speed bonus từ 5s xuống 3s trong 2 câu', 'ALL_OTHERS', 'QUESTIONS', 2, 0.00);

-- 🛡️ DEFENSE SKILLS (3 skills)
INSERT INTO "Skills" ("skill_name", "skill_code", "skill_icon", "category", "tier", "cost_type", "cost_amount", "description", "effect_description", "target_type", "duration_type", "duration_value") VALUES
('Khiên', 'shield', 'shield.png', 'DEFENSE', 'C', 'SYNCOIN', 150, 'Miễn nhiễm tấn công', 'Miễn nhiễm tất cả skill tấn công trong 2 câu', 'SELF', 'QUESTIONS', 2),
('Khóa Vị Trí', 'lock', 'lock.png', 'DEFENSE', 'D', 'SYNCOIN', 110, 'Khóa thứ hạng hiện tại', 'Thứ hạng không thể bị thay đổi trong 2 câu', 'SELF', 'QUESTIONS', 2),
('Thanh Lọc', 'cleanse', 'cleanse.png', 'DEFENSE', 'C', 'SYNCOIN', 130, 'Loại bỏ hiệu ứng xấu', 'Loại bỏ tất cả hiệu ứng tiêu cực đang tác động', 'SELF', 'INSTANT', 1);

-- 💎 BURST SKILLS (5 skills)
INSERT INTO "Skills" ("skill_name", "skill_code", "skill_icon", "category", "tier", "cost_type", "cost_amount", "description", "effect_description", "target_type", "duration_type", "duration_value", "risk_factor", "success_rate") VALUES
('Nhân Đôi', 'double', 'double.png', 'BURST', 'B', 'SYNCOIN', 180, 'Nhân đôi điểm câu tiếp theo', 'Điểm câu tiếp theo x2, mất 20% nếu sai', 'SELF', 'QUESTIONS', 1, 0.20, 1.00),
('May Mắn', 'lucky', 'lucky.png', 'BURST', 'C', 'SYNCOIN', 160, 'Tăng điểm an toàn', 'Tăng 50% điểm câu tiếp theo, không rủi ro', 'SELF', 'QUESTIONS', 1, 0.00, 1.00),
('Nhân Ba', 'triple', 'triple.png', 'BURST', 'A', 'KRISTAL', 50, 'Nhân ba điểm câu tiếp theo', 'Điểm câu tiếp theo x3, mất 30% nếu sai', 'SELF', 'QUESTIONS', 1, 0.30, 1.00),
('Hoàn Hảo', 'perfect', 'perfect.png', 'BURST', 'A', 'KRISTAL', 40, 'Đảm bảo câu trả lời đúng', 'Câu tiếp theo tự động đúng + speed bonus', 'SELF', 'QUESTIONS', 1, 0.00, 1.00),
('Nhân Năm', 'quintuple', 'quintuple.png', 'BURST', 'A', 'KRISTAL', 80, 'Nhân năm điểm câu tiếp theo', 'Điểm câu tiếp theo x5, mất 50% nếu sai', 'SELF', 'QUESTIONS', 1, 0.50, 1.00);

-- 🎲 SPECIAL SKILLS (3 skills)
INSERT INTO "Skills" ("skill_name", "skill_code", "skill_icon", "category", "tier", "cost_type", "cost_amount", "description", "effect_description", "target_type", "duration_type", "duration_value") VALUES
('Hoán Đổi', 'swap', 'swap.png', 'SPECIAL', 'B', 'SYNCOIN', 200, 'Đổi vị trí với người khác', 'Hoán đổi thứ hạng với player bất kỳ', 'SPECIFIC_PLAYER', 'INSTANT', 1),
('Xúc Xắc', 'dice', 'dice.png', 'SPECIAL', 'C', 'SYNCOIN', 140, 'Hiệu ứng ngẫu nhiên', 'Nhận 1 trong 6 hiệu ứng tích cực ngẫu nhiên', 'SELF', 'INSTANT', 1),
('Hồi Năng Lượng', 'energy', 'energy.png', 'SPECIAL', 'B', 'SYNCOIN', 190, 'Hồi phục năng lượng', 'Tăng 50% năng lượng cho tất cả người chơi', 'ALL_OTHERS', 'INSTANT', 1);

-- 🏆 ULTIMATE SKILLS (2 skills)
INSERT INTO "Skills" ("skill_name", "skill_code", "skill_icon", "category", "tier", "cost_type", "cost_amount", "description", "effect_description", "target_type", "duration_type", "duration_value") VALUES
('Vua Quiz', 'king', 'king.png', 'ULTIMATE', 'S', 'KRISTAL', 150, 'Chế độ thần thánh 3 câu', 'Miễn nhiễm mọi tấn công + điểm x2 trong 3 câu', 'SELF', 'QUESTIONS', 3),
('Hồi Sinh', 'phoenix', 'phoenix.png', 'ULTIMATE', 'S', 'KRISTAL', 200, 'Hồi sinh từ vị trí cuối', 'Nếu ở bottom 3, nhảy lên top 3 + shield 2 câu', 'SELF', 'INSTANT', 1);

-- =====================================================
-- STORED PROCEDURES FOR SKILL OPERATIONS
-- =====================================================

-- Function to check if user can purchase skill
CREATE OR REPLACE FUNCTION can_purchase_skill(
    p_user_id INTEGER,
    p_skill_id INTEGER
) RETURNS BOOLEAN AS $$
DECLARE
    skill_cost INTEGER;
    skill_currency VARCHAR(10);
    user_balance INTEGER;
    already_owned BOOLEAN;
BEGIN
    -- Check if user already owns this skill
    SELECT EXISTS(
        SELECT 1 FROM "UserSkills" 
        WHERE "user_id" = p_user_id AND "skill_id" = p_skill_id
    ) INTO already_owned;
    
    IF already_owned THEN
        RETURN FALSE;
    END IF;
    
    -- Get skill cost and currency
    SELECT "cost_amount", "cost_type" 
    INTO skill_cost, skill_currency
    FROM "Skills" 
    WHERE "skill_id" = p_skill_id AND "is_active" = TRUE;
    
    IF NOT FOUND THEN
        RETURN FALSE;
    END IF;
    
    -- Get user balance
    IF skill_currency = 'SYNCOIN' THEN
        SELECT COALESCE("syncoin_balance", 0) INTO user_balance
        FROM "Users" WHERE "user_id" = p_user_id;
    ELSE
        SELECT COALESCE("kristal_balance", 0) INTO user_balance
        FROM "Users" WHERE "user_id" = p_user_id;
    END IF;
    
    RETURN user_balance >= skill_cost;
END;
$$ LANGUAGE plpgsql;

-- Function to purchase skill
CREATE OR REPLACE FUNCTION purchase_skill(
    p_user_id INTEGER,
    p_skill_id INTEGER
) RETURNS JSONB AS $$
DECLARE
    skill_cost INTEGER;
    skill_currency VARCHAR(10);
    user_balance_before INTEGER;
    user_balance_after INTEGER;
    result JSONB;
BEGIN
    -- Check if purchase is valid
    IF NOT can_purchase_skill(p_user_id, p_skill_id) THEN
        RETURN jsonb_build_object(
            'success', false,
            'message', 'Cannot purchase this skill'
        );
    END IF;
    
    -- Get skill details
    SELECT "cost_amount", "cost_type" 
    INTO skill_cost, skill_currency
    FROM "Skills" 
    WHERE "skill_id" = p_skill_id;
    
    -- Get current balance
    IF skill_currency = 'SYNCOIN' THEN
        SELECT COALESCE("syncoin_balance", 0) INTO user_balance_before
        FROM "Users" WHERE "user_id" = p_user_id;
        
        user_balance_after := user_balance_before - skill_cost;
        
        -- Update user balance
        UPDATE "Users" 
        SET "syncoin_balance" = user_balance_after
        WHERE "user_id" = p_user_id;
    ELSE
        SELECT COALESCE("kristal_balance", 0) INTO user_balance_before
        FROM "Users" WHERE "user_id" = p_user_id;
        
        user_balance_after := user_balance_before - skill_cost;
        
        -- Update user balance
        UPDATE "Users" 
        SET "kristal_balance" = user_balance_after
        WHERE "user_id" = p_user_id;
    END IF;
    
    -- Add skill to user inventory
    INSERT INTO "UserSkills" (
        "user_id", "skill_id", "purchase_cost", "purchase_currency"
    ) VALUES (
        p_user_id, p_skill_id, skill_cost, skill_currency
    );
    
    -- Log purchase
    INSERT INTO "SkillPurchaseHistory" (
        "user_id", "skill_id", "cost_amount", "cost_currency",
        "user_balance_before", "user_balance_after"
    ) VALUES (
        p_user_id, p_skill_id, skill_cost, skill_currency,
        user_balance_before, user_balance_after
    );
    
    RETURN jsonb_build_object(
        'success', true,
        'message', 'Skill purchased successfully',
        'balance_before', user_balance_before,
        'balance_after', user_balance_after
    );
END;
$$ LANGUAGE plpgsql;
