"use client";

import React, { useState } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON>b,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/navigation";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/layout";

import { useAvatar } from "@/lib/hooks/use-avatar";
import {
  ConnectedAvatarDisplay,
  CustomizationTabs,
  AvatarGrid,
  FrameGrid,
  NameEffectsGrid,
  EmojiGrid,
  CustomizationPreview,
  type CustomizationTab,
} from "@/components/features/avatar";
import { AvatarData, AvatarFrame, NameEffect } from "@/lib/types/avatar";
import { Loader2, Palette, Frame, Sparkles } from "lucide-react";

// Props interface for the page
interface AvatarCustomizationPageProps {}

/**
 * Avatar Customization Page Component
 *
 * Main page for avatar customization interface allowing users to:
 * - <PERSON>rowse and equip avatars
 * - <PERSON>rowse and equip frames
 * - Browse and equip name effects
 * - Preview combinations before equipping
 */
const AvatarCustomizationPage: React.FC<AvatarCustomizationPageProps> = () => {
  // State management
  const [activeTab, setActiveTab] = useState<CustomizationTab>("avatars");

  // Preview state
  const [previewAvatar, setPreviewAvatar] = useState<AvatarData | null>(null);
  const [previewFrame, setPreviewFrame] = useState<AvatarFrame | null>(null);
  const [previewNameEffect, setPreviewNameEffect] = useState<NameEffect | null>(
    null
  );

  // Avatar hook for data and actions
  const {
    myAvatarData,
    availableItems,
    isLoading,
    isAvailableItemsLoading,
    isEquipping,
    error,
    availableItemsError,
    equipError,
    equippedAvatar,
    equippedFrame,
    totalItems,
    completionRate,
    equipItem,
  } = useAvatar();

  // Debug logging
  console.log("🎯 [Avatar Page] Current state:", {
    availableItems,
    isAvailableItemsLoading,
    availableItemsError,
    myAvatarData,
    equippedAvatar,
  });

  // Preview helpers
  const hasPreviewChanges =
    previewAvatar !== null ||
    previewFrame !== null ||
    previewNameEffect !== null;

  const resetPreview = () => {
    setPreviewAvatar(null);
    setPreviewFrame(null);
    setPreviewNameEffect(null);
  };

  const applyPreviewChanges = async () => {
    try {
      if (previewAvatar) {
        await equipItem({
          itemType: "avatar",
          itemId: previewAvatar.avatar_id,
        });
      }
      if (previewFrame) {
        await equipItem({ itemType: "frame", itemId: previewFrame.frame_id });
      }
      if (previewNameEffect) {
        await equipItem({
          itemType: "name_effect",
          itemId: previewNameEffect.effect_id,
        });
      }
      resetPreview();
    } catch (error) {
      console.error("Failed to apply preview changes:", error);
    }
  };

  // Handle avatar preview/equip
  const handleEquipAvatar = async (avatarId: number) => {
    const avatar = [
      ...(availableItems?.avatars?.owned || []),
      ...(availableItems?.avatars?.unlockable || []),
      ...(availableItems?.avatars?.locked || []),
    ].find((a) => a.avatar_id === avatarId);

    if (avatar) {
      setPreviewAvatar(avatar);
    }
  };

  // Handle frame preview/equip
  const handleEquipFrame = async (frameId: number) => {
    const frame = [
      ...(availableItems?.frames?.owned || []),
      ...(availableItems?.frames?.unlockable || []),
      ...(availableItems?.frames?.locked || []),
      ...((availableItems?.frames as any)?.shop || []), // Thêm shop frames
    ].find((f) => f.frame_id === frameId);

    if (frame) {
      setPreviewFrame(frame);
    }
  };

  // Handle name effect preview/equip
  const handleEquipNameEffect = async (effectId: number) => {
    const effect = [
      ...(availableItems?.name_effects?.owned || []),
      ...(availableItems?.name_effects?.unlockable || []),
      ...(availableItems?.name_effects?.locked || []),
    ].find((e) => e.effect_id === effectId);

    if (effect) {
      setPreviewNameEffect(effect);
    }
  };

  // Handle emoji preview/equip
  const handleEquipEmoji = async (emojiId: number) => {
    // Emojis không có preview, trực tiếp equip
    try {
      await equipItem({ itemType: "emoji", itemId: emojiId });
    } catch (error) {
      console.error("Failed to equip emoji:", error);
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="flex flex-col items-center gap-4">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p className="text-muted-foreground">Đang tải dữ liệu avatar...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="text-destructive">Lỗi tải dữ liệu</CardTitle>
            <CardDescription>
              Không thể tải dữ liệu avatar. Vui lòng thử lại sau.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">{error}</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Breadcrumb Navigation */}
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/dashboard">Dashboard</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Tùy chỉnh Avatar</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      {/* Page Header */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Tùy chỉnh Avatar
          </h1>
          <p className="text-muted-foreground">
            Thay đổi avatar, khung và hiệu ứng tên để thể hiện phong cách của
            bạn
          </p>
        </div>

        {/* Current Avatar Preview */}
        <div className="flex items-center gap-4">
          <div className="text-right">
            <p className="text-sm font-medium">Avatar hiện tại</p>
            <p className="text-xs text-muted-foreground">
              {totalItems} items • {completionRate}% hoàn thành
            </p>
          </div>
          <ConnectedAvatarDisplay
            size="large"
            showName={true}
            showRarity={true}
            className="border-2 border-primary/20 rounded-lg p-2"
          />
        </div>
      </div>

      {/* Main Customization Interface */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Palette className="h-5 w-5" />
            Bộ sưu tập Avatar
          </CardTitle>
          <CardDescription>
            Chọn và trang bị các items để tùy chỉnh appearance của bạn
          </CardDescription>
        </CardHeader>
        <CardContent>
          <CustomizationTabs
            activeTab={activeTab}
            onTabChange={setActiveTab}
            enableUrlSync={true}
            avatarsContent={
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <p className="text-sm text-muted-foreground">
                    {availableItems?.avatars?.owned?.length || 0} /{" "}
                    {(availableItems?.avatars?.owned?.length || 0) +
                      (availableItems?.avatars?.unlockable?.length || 0) +
                      (availableItems?.avatars?.locked?.length || 0)}{" "}
                    avatars
                  </p>
                </div>

                <AvatarGrid
                  ownedAvatars={availableItems?.avatars?.owned || []}
                  unlockableAvatars={availableItems?.avatars?.unlockable || []}
                  lockedAvatars={availableItems?.avatars?.locked || []}
                  equippedAvatarId={equippedAvatar?.avatar_id}
                  onEquipAvatar={handleEquipAvatar}
                  isLoading={isAvailableItemsLoading}
                  isEquipping={isEquipping}
                />
              </div>
            }
            framesContent={
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <p className="text-sm text-muted-foreground">
                    {availableItems?.frames?.owned?.length || 0} /{" "}
                    {(availableItems?.frames?.owned?.length || 0) +
                      (availableItems?.frames?.unlockable?.length || 0) +
                      (availableItems?.frames?.locked?.length || 0) +
                      ((availableItems?.frames as any)?.shop?.length || 0)}{" "}
                    frames
                  </p>
                </div>

                <FrameGrid
                  ownedFrames={availableItems?.frames?.owned || []}
                  unlockableFrames={availableItems?.frames?.unlockable || []}
                  lockedFrames={availableItems?.frames?.locked || []}
                  shopFrames={(availableItems?.frames as any)?.shop || []}
                  equippedFrameId={equippedFrame?.frame_id}
                  onEquipFrame={handleEquipFrame}
                  isLoading={isAvailableItemsLoading}
                  isEquipping={isEquipping}
                />
              </div>
            }
            nameEffectsContent={
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <p className="text-sm text-muted-foreground">
                    {availableItems?.name_effects?.owned?.length || 0} /{" "}
                    {(availableItems?.name_effects?.owned?.length || 0) +
                      (availableItems?.name_effects?.unlockable?.length || 0) +
                      (availableItems?.name_effects?.locked?.length || 0)}{" "}
                    effects
                  </p>
                </div>

                <NameEffectsGrid
                  ownedEffects={availableItems?.name_effects?.owned || []}
                  unlockableEffects={
                    availableItems?.name_effects?.unlockable || []
                  }
                  lockedEffects={availableItems?.name_effects?.locked || []}
                  equippedEffectId={
                    myAvatarData?.customization?.equipped_name_effect_id
                  }
                  onEquipEffect={handleEquipNameEffect}
                  isLoading={isAvailableItemsLoading}
                  isEquipping={isEquipping}
                />
              </div>
            }
            emojisContent={
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <p className="text-sm text-muted-foreground">
                    {availableItems?.emojis?.owned?.length || 0} /{" "}
                    {(availableItems?.emojis?.owned?.length || 0) +
                      (availableItems?.emojis?.unlockable?.length || 0) +
                      (availableItems?.emojis?.locked?.length || 0)}{" "}
                    emojis
                  </p>
                </div>

                <EmojiGrid
                  ownedEmojis={availableItems?.emojis?.owned || []}
                  unlockableEmojis={availableItems?.emojis?.unlockable || []}
                  lockedEmojis={availableItems?.emojis?.locked || []}
                  onEquipEmoji={handleEquipEmoji}
                  isLoading={isAvailableItemsLoading}
                  isEquipping={isEquipping}
                />
              </div>
            }
          />
        </CardContent>
      </Card>

      {/* Preview Panel */}
      {hasPreviewChanges && (
        <CustomizationPreview
          previewAvatar={previewAvatar}
          previewFrame={previewFrame}
          previewNameEffect={previewNameEffect}
          currentAvatar={equippedAvatar}
          currentFrame={equippedFrame}
          currentNameEffect={
            myAvatarData?.customization?.equipped_name_effect_id
              ? [
                  ...(availableItems?.name_effects?.owned || []),
                  ...(availableItems?.name_effects?.unlockable || []),
                  ...(availableItems?.name_effects?.locked || []),
                ].find(
                  (effect) =>
                    effect.effect_id ===
                    myAvatarData.customization.equipped_name_effect_id
                )
              : null
          }
          userName="Người chơi"
          userTier={myAvatarData?.user_tier}
          onApplyChanges={applyPreviewChanges}
          onResetPreview={resetPreview}
          isApplying={isEquipping}
          hasChanges={hasPreviewChanges}
        />
      )}
    </div>
  );
};

export default AvatarCustomizationPage;
