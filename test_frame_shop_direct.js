const https = require('https');
const http = require('http');

const BASE_URL = 'http://localhost:8888';

function makeRequest(url, options = {}) {
    return new Promise((resolve, reject) => {
        const urlObj = new URL(url);
        const requestOptions = {
            hostname: urlObj.hostname,
            port: urlObj.port,
            path: urlObj.pathname + urlObj.search,
            method: options.method || 'GET',
            headers: options.headers || {}
        };

        if (options.data) {
            const postData = JSON.stringify(options.data);
            requestOptions.headers['Content-Type'] = 'application/json';
            requestOptions.headers['Content-Length'] = Buffer.byteLength(postData);
        }

        const req = http.request(requestOptions, (res) => {
            let data = '';
            res.on('data', (chunk) => {
                data += chunk;
            });
            res.on('end', () => {
                try {
                    const jsonData = JSON.parse(data);
                    resolve({ status: res.statusCode, data: jsonData });
                } catch (e) {
                    resolve({ status: res.statusCode, data: data });
                }
            });
        });

        req.on('error', (err) => {
            reject(err);
        });

        if (options.data) {
            req.write(JSON.stringify(options.data));
        }
        req.end();
    });
}

async function testFrameShop() {
    try {
        console.log('🔐 Step 1: Login to get token...');

        // Step 1: Login
        const loginResponse = await makeRequest(`${BASE_URL}/api/users/login`, {
            method: 'POST',
            data: {
                email: '<EMAIL>',
                password: '110123068'
            }
        });

        if (!loginResponse.data.success) {
            console.error('❌ Login failed:', loginResponse.data);
            return;
        }

        const token = loginResponse.data.data.token;
        const user = loginResponse.data.data.user;

        console.log('✅ Login successful!');
        console.log(`   User: ${user.email} (Level ${user.current_level})`);
        console.log(`   Token: ${token.substring(0, 20)}...`);

        // Step 2: Test frame shop endpoint
        console.log('\n🛍️ Step 2: Testing frame shop endpoint...');

        const shopResponse = await makeRequest(`${BASE_URL}/api/avatar/frames/shop`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        console.log('✅ Frame shop response:');
        console.log(JSON.stringify(shopResponse.data, null, 2));

        // Step 3: Test available items to see shop frames
        console.log('\n📦 Step 3: Testing available items (should include shop frames)...');

        const itemsResponse = await makeRequest(`${BASE_URL}/api/avatar/available-items`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        console.log('✅ Available items response:');
        console.log('Frames data:', JSON.stringify(itemsResponse.data.data.frames, null, 2));

    } catch (error) {
        console.error('❌ Error occurred:');
        
        if (error.response) {
            // Server responded with error status
            console.error('Status:', error.response.status);
            console.error('Data:', error.response.data);
            console.error('Headers:', error.response.headers);
        } else if (error.request) {
            // Request was made but no response received
            console.error('No response received:', error.request);
        } else {
            // Something else happened
            console.error('Error message:', error.message);
        }
        
        console.error('Full error:', error);
    }
}

// Run the test
console.log('🚀 Starting Frame Shop Test...\n');
testFrameShop();
