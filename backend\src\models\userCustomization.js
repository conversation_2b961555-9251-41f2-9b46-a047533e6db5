'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
    class UserCustomization extends Model {
        static associate(models) {
            // Associations
            UserCustomization.belongsTo(models.User, { 
                foreignKey: 'user_id', 
                as: 'User' 
            });
            
            UserCustomization.belongsTo(models.Avatar, { 
                foreignKey: 'equipped_avatar_id', 
                as: 'EquippedAvatar' 
            });
            
            UserCustomization.belongsTo(models.AvatarFrame, { 
                foreignKey: 'equipped_frame_id', 
                as: 'EquippedFrame' 
            });
            
            UserCustomization.belongsTo(models.NameEffect, { 
                foreignKey: 'equipped_name_effect_id', 
                as: 'EquippedNameEffect' 
            });
        }

        /**
         * Get user's customization with all equipped items
         * @param {number} userId - User ID
         * @returns {UserCustomization|null}
         */
        static async getUserCustomization(userId) {
            return await UserCustomization.findOne({
                where: { user_id: userId },
                include: [
                    { 
                        model: sequelize.models.Avatar, 
                        as: 'EquippedAvatar',
                        required: false 
                    },
                    { 
                        model: sequelize.models.AvatarFrame, 
                        as: 'EquippedFrame',
                        required: false 
                    },
                    { 
                        model: sequelize.models.NameEffect, 
                        as: 'EquippedNameEffect',
                        required: false 
                    }
                ]
            });
        }

        /**
         * Initialize user customization with default items
         * @param {number} userId - User ID
         * @returns {UserCustomization}
         */
        static async initializeUserCustomization(userId) {
            // Get default avatar and frame
            const defaultAvatar = await sequelize.models.Avatar.findOne({
                where: { is_default: true, is_active: true },
                order: [['sort_order', 'ASC']]
            });

            const defaultFrame = await sequelize.models.AvatarFrame.findOne({
                where: { is_default: true, is_active: true },
                order: [['sort_order', 'ASC']]
            });

            return await UserCustomization.create({
                user_id: userId,
                equipped_avatar_id: defaultAvatar?.avatar_id || null,
                equipped_frame_id: defaultFrame?.frame_id || null,
                equipped_name_effect_id: null,
                customization_settings: {}
            });
        }

        /**
         * Equip avatar for user
         * @param {number} userId - User ID
         * @param {number} avatarId - Avatar ID to equip
         * @returns {boolean} Success status
         */
        static async equipAvatar(userId, avatarId) {
            try {
                // Check if user owns the avatar
                const ownsAvatar = await sequelize.models.UserInventory.checkUserOwnsItem(
                    userId, 'AVATAR', avatarId
                );

                if (!ownsAvatar) {
                    return false; // User doesn't own this avatar
                }

                // Update or create customization
                const [customization, created] = await UserCustomization.findOrCreate({
                    where: { user_id: userId },
                    defaults: {
                        user_id: userId,
                        equipped_avatar_id: avatarId,
                        customization_settings: {}
                    }
                });

                if (!created) {
                    customization.equipped_avatar_id = avatarId;
                    await customization.save();
                }

                return true;
            } catch (error) {
                console.error('Error equipping avatar:', error);
                return false;
            }
        }

        /**
         * Equip frame for user
         * @param {number} userId - User ID
         * @param {number} frameId - Frame ID to equip
         * @returns {boolean} Success status
         */
        static async equipFrame(userId, frameId) {
            try {
                // Check if user owns the frame
                const ownsFrame = await sequelize.models.UserInventory.checkUserOwnsItem(
                    userId, 'FRAME', frameId
                );

                if (!ownsFrame) {
                    return false; // User doesn't own this frame
                }

                // Update or create customization
                const [customization, created] = await UserCustomization.findOrCreate({
                    where: { user_id: userId },
                    defaults: {
                        user_id: userId,
                        equipped_frame_id: frameId,
                        customization_settings: {}
                    }
                });

                if (!created) {
                    customization.equipped_frame_id = frameId;
                    await customization.save();
                }

                return true;
            } catch (error) {
                console.error('Error equipping frame:', error);
                return false;
            }
        }

        /**
         * Equip name effect for user
         * @param {number} userId - User ID
         * @param {number} nameEffectId - Name effect ID to equip
         * @returns {boolean} Success status
         */
        static async equipNameEffect(userId, nameEffectId) {
            try {
                // Check if user owns the name effect
                const ownsNameEffect = await sequelize.models.UserInventory.checkUserOwnsItem(
                    userId, 'NAME_EFFECT', nameEffectId
                );

                if (!ownsNameEffect) {
                    return false; // User doesn't own this name effect
                }

                // Update or create customization
                const [customization, created] = await UserCustomization.findOrCreate({
                    where: { user_id: userId },
                    defaults: {
                        user_id: userId,
                        equipped_name_effect_id: nameEffectId,
                        customization_settings: {}
                    }
                });

                if (!created) {
                    customization.equipped_name_effect_id = nameEffectId;
                    await customization.save();
                }

                return true;
            } catch (error) {
                console.error('Error equipping name effect:', error);
                return false;
            }
        }

        /**
         * Unequip item for user
         * @param {number} userId - User ID
         * @param {string} itemType - Item type (avatar, frame, name_effect)
         * @returns {boolean} Success status
         */
        static async unequipItem(userId, itemType) {
            try {
                const customization = await UserCustomization.findOne({
                    where: { user_id: userId }
                });

                if (!customization) {
                    return false;
                }

                switch (itemType.toLowerCase()) {
                    case 'avatar':
                        customization.equipped_avatar_id = null;
                        break;
                    case 'frame':
                        customization.equipped_frame_id = null;
                        break;
                    case 'name_effect':
                        customization.equipped_name_effect_id = null;
                        break;
                    default:
                        return false;
                }

                await customization.save();
                return true;
            } catch (error) {
                console.error('Error unequipping item:', error);
                return false;
            }
        }

        /**
         * Update customization settings
         * @param {number} userId - User ID
         * @param {Object} settings - New settings to merge
         * @returns {boolean} Success status
         */
        static async updateCustomizationSettings(userId, settings) {
            try {
                const [customization, created] = await UserCustomization.findOrCreate({
                    where: { user_id: userId },
                    defaults: {
                        user_id: userId,
                        customization_settings: settings
                    }
                });

                if (!created) {
                    customization.customization_settings = {
                        ...customization.customization_settings,
                        ...settings
                    };
                    await customization.save();
                }

                return true;
            } catch (error) {
                console.error('Error updating customization settings:', error);
                return false;
            }
        }

        /**
         * Get user's display info for leaderboards/social features
         * @param {number} userId - User ID
         * @returns {Object|null} Display info
         */
        static async getUserDisplayInfo(userId) {
            const customization = await UserCustomization.getUserCustomization(userId);
            
            if (!customization) {
                return null;
            }

            const user = await sequelize.models.User.findByPk(userId, {
                attributes: ['user_id', 'name', 'email']
            });

            if (!user) {
                return null;
            }

            return {
                user_id: user.user_id,
                user_name: user.name,
                avatar: customization.EquippedAvatar ? {
                    avatar_id: customization.EquippedAvatar.avatar_id,
                    avatar_name: customization.EquippedAvatar.avatar_name,
                    image_path: customization.EquippedAvatar.image_path,
                    rarity: customization.EquippedAvatar.rarity
                } : null,
                frame: customization.EquippedFrame ? {
                    frame_id: customization.EquippedFrame.frame_id,
                    frame_name: customization.EquippedFrame.frame_name,
                    image_path: customization.EquippedFrame.image_path,
                    tier_name: customization.EquippedFrame.tier_name,
                    rarity: customization.EquippedFrame.rarity
                } : null,
                name_effect: customization.EquippedNameEffect ? {
                    effect_id: customization.EquippedNameEffect.effect_id,
                    effect_name: customization.EquippedNameEffect.effect_name,
                    css_class: customization.EquippedNameEffect.css_class,
                    css_style: customization.EquippedNameEffect.css_style,
                    tier_name: customization.EquippedNameEffect.tier_name
                } : null,
                customization_settings: customization.customization_settings
            };
        }

        /**
         * Get formatted customization info
         * @returns {Object}
         */
        getFormattedInfo() {
            return {
                customization_id: this.customization_id,
                user_id: this.user_id,
                equipped_avatar: this.EquippedAvatar ? this.EquippedAvatar.getFormattedInfo() : null,
                equipped_frame: this.EquippedFrame ? this.EquippedFrame.getFormattedInfo() : null,
                equipped_name_effect: this.EquippedNameEffect ? this.EquippedNameEffect.getFormattedInfo() : null,
                customization_settings: this.customization_settings,
                last_updated: this.last_updated
            };
        }
    }

    UserCustomization.init(
        {
            customization_id: {
                type: DataTypes.INTEGER,
                primaryKey: true,
                autoIncrement: true,
                autoIncrementIdentity: true
            },
            user_id: {
                type: DataTypes.INTEGER,
                allowNull: false,
                unique: true,
                comment: 'ID người dùng'
            },
            equipped_avatar_id: {
                type: DataTypes.INTEGER,
                allowNull: true,
                comment: 'ID avatar đang trang bị'
            },
            equipped_frame_id: {
                type: DataTypes.INTEGER,
                allowNull: true,
                comment: 'ID khung đang trang bị'
            },
            equipped_name_effect_id: {
                type: DataTypes.INTEGER,
                allowNull: true,
                comment: 'ID hiệu ứng tên đang trang bị'
            },
            customization_settings: {
                type: DataTypes.JSONB,
                allowNull: false,
                defaultValue: {},
                comment: 'Cài đặt tùy chỉnh'
            },
            last_updated: {
                type: DataTypes.DATE,
                allowNull: false,
                defaultValue: DataTypes.NOW,
                comment: 'Lần cập nhật cuối'
            }
        },
        {
            sequelize,
            modelName: 'UserCustomization',
            tableName: 'UserCustomization',
            timestamps: false, // Using custom last_updated field
            indexes: [
                {
                    fields: ['user_id'],
                    unique: true
                },
                {
                    fields: ['equipped_avatar_id']
                },
                {
                    fields: ['equipped_frame_id']
                },
                {
                    fields: ['equipped_name_effect_id']
                }
            ]
        }
    );

    return UserCustomization;
};
