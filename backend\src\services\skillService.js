// backend/src/services/skillService.js
const { Skill, UserSkill, QuizSkillLoadout, SkillUsageHistory, ActiveSkillEffect, User } = require('../models');

class SkillService {
    // =====================================================
    // SKILL SHOP & INVENTORY MANAGEMENT
    // =====================================================

    static async getAllSkills(filters = {}) {
        try {
            const { category, tier, costType } = filters;
            const options = {};

            if (category) options.category = category;
            if (tier) options.tier = tier;
            if (costType) options.costType = costType;

            const skills = await Skill.getAllSkills(options);

            return {
                success: true,
                skills: skills,
                total: skills.length
            };
        } catch (error) {
            return {
                success: false,
                message: 'Failed to fetch skills',
                error: error.message
            };
        }
    }

    static async getSkillsByCategory(category) {
        try {
            const skills = await Skill.getSkillsByCategory(category);

            return {
                success: true,
                category: category,
                skills: skills,
                count: skills.length
            };
        } catch (error) {
            return {
                success: false,
                message: `Failed to fetch ${category} skills`,
                error: error.message
            };
        }
    }

    static async getUserSkills(userId, options = {}) {
        try {
            const userSkills = await UserSkill.getUserSkills(userId, options);

            return {
                success: true,
                user_id: userId,
                skills: userSkills,
                total_owned: userSkills.length
            };
        } catch (error) {
            return {
                success: false,
                message: 'Failed to fetch user skills',
                error: error.message
            };
        }
    }

    static async getAffordableSkills(userId) {
        try {
            // Get user's current balances
            const user = await User.findByPk(userId, {
                attributes: ['syncoin_balance', 'kristal_balance']
            });

            if (!user) {
                return {
                    success: false,
                    message: 'User not found'
                };
            }

            const userBalances = {
                syncoin_balance: user.syncoin_balance || 0,
                kristal_balance: user.kristal_balance || 0
            };

            const affordableSkills = await Skill.getAffordableSkills(userId, userBalances);

            return {
                success: true,
                user_id: userId,
                user_balances: userBalances,
                affordable_skills: affordableSkills,
                count: affordableSkills.length
            };
        } catch (error) {
            return {
                success: false,
                message: 'Failed to fetch affordable skills',
                error: error.message
            };
        }
    }

    static async purchaseSkill(userId, skillId) {
        try {
            const result = await UserSkill.purchaseSkill(userId, skillId);

            if (result.success) {
                // Emit socket event for skill purchase
                if (global.io) {
                    global.io.to(`user_${userId}`).emit('skill_purchased', {
                        user_id: userId,
                        skill_id: skillId,
                        balance_after: result.balance_after,
                        timestamp: new Date().toISOString()
                    });
                }
            }

            return result;
        } catch (error) {
            return {
                success: false,
                message: 'Failed to purchase skill',
                error: error.message
            };
        }
    }

    // =====================================================
    // SKILL LOADOUT MANAGEMENT
    // =====================================================

    static async createQuizLoadout(quizSessionId, userId, skillIds) {
        try {
            // Validate skill loadout
            const validation = await Skill.validateSkillLoadout(skillIds);
            if (!validation.valid) {
                return {
                    success: false,
                    message: validation.message
                };
            }

            // Verify user owns all skills
            const userSkills = await UserSkill.findAll({
                where: {
                    user_id: userId,
                    skill_id: { [require('sequelize').Op.in]: skillIds }
                }
            });

            if (userSkills.length !== 4) {
                return {
                    success: false,
                    message: 'User does not own all selected skills'
                };
            }

            const result = await QuizSkillLoadout.createLoadout(quizSessionId, userId, skillIds);

            if (result.success && global.io) {
                // Emit loadout update to quiz room
                global.io.to(`quiz_${quizSessionId}`).emit('loadout_updated', {
                    user_id: userId,
                    skills: validation.skills.map(s => ({
                        skill_id: s.skill_id,
                        skill_name: s.skill_name,
                        skill_icon: s.skill_icon,
                        category: s.category
                    })),
                    timestamp: new Date().toISOString()
                });
            }

            return result;
        } catch (error) {
            return {
                success: false,
                message: 'Failed to create quiz loadout',
                error: error.message
            };
        }
    }

    static async getQuizLoadout(quizSessionId, userId) {
        try {
            const loadout = await QuizSkillLoadout.getLoadout(quizSessionId, userId);

            if (!loadout) {
                return {
                    success: false,
                    message: 'No loadout found for this quiz session'
                };
            }

            return {
                success: true,
                loadout: loadout
            };
        } catch (error) {
            return {
                success: false,
                message: 'Failed to fetch quiz loadout',
                error: error.message
            };
        }
    }

    static async getAllQuizLoadouts(quizSessionId) {
        try {
            const loadouts = await QuizSkillLoadout.getAllLoadouts(quizSessionId);

            return {
                success: true,
                quiz_session_id: quizSessionId,
                loadouts: loadouts,
                participant_count: loadouts.length
            };
        } catch (error) {
            return {
                success: false,
                message: 'Failed to fetch quiz loadouts',
                error: error.message
            };
        }
    }

    // =====================================================
    // SKILL EXECUTION & EFFECTS
    // =====================================================

    static async executeSkill(executionData) {
        const {
            quizSessionId,
            userId,
            skillId,
            targetUserId = null,
            questionNumber,
            energyLevel,
            gameState = {}
        } = executionData;

        try {
            // Get skill details
            const skill = await Skill.findByPk(skillId);
            if (!skill || !skill.is_active) {
                return {
                    success: false,
                    message: 'Skill not found or inactive'
                };
            }

            // Verify user owns this skill and it's in their loadout
            const loadoutSkills = await QuizSkillLoadout.getLoadoutSkills(quizSessionId, userId);
            const hasSkillInLoadout = loadoutSkills.some(s => s.skill_id === skillId);

            if (!hasSkillInLoadout) {
                return {
                    success: false,
                    message: 'Skill not in current loadout'
                };
            }

            // Check if user has active shield effect (blocks attacks)
            if (skill.category === 'ATTACK' && targetUserId) {
                const hasShield = await ActiveSkillEffect.hasActiveEffect(
                    quizSessionId,
                    targetUserId,
                    'SHIELD'
                );

                if (hasShield) {
                    await SkillUsageHistory.recordSkillUsage({
                        quizSessionId,
                        userId,
                        skillId,
                        targetUserId,
                        questionNumber,
                        energyLevel,
                        executionResult: 'BLOCKED',
                        effectData: { reason: 'Target has shield protection' }
                    });

                    return {
                        success: false,
                        message: 'Attack blocked by shield',
                        execution_result: 'BLOCKED'
                    };
                }
            }

            // Execute skill based on category
            let executionResult;
            let effectData = {};
            let pointsAffected = 0;

            switch (skill.category) {
                case 'ATTACK':
                    executionResult = await this.executeAttackSkill(skill, executionData, gameState);
                    break;
                case 'DEFENSE':
                    executionResult = await this.executeDefenseSkill(skill, executionData);
                    break;
                case 'BURST':
                    executionResult = await this.executeBurstSkill(skill, executionData);
                    break;
                case 'SPECIAL':
                    executionResult = await this.executeSpecialSkill(skill, executionData, gameState);
                    break;
                case 'ULTIMATE':
                    executionResult = await this.executeUltimateSkill(skill, executionData);
                    break;
                default:
                    executionResult = { success: false, message: 'Unknown skill category' };
            }

            if (executionResult.success) {
                effectData = executionResult.effectData || {};
                pointsAffected = executionResult.pointsAffected || 0;

                // Apply skill effect if it has duration
                if (skill.duration_type !== 'INSTANT') {
                    await ActiveSkillEffect.applySkillEffect({
                        quizSessionId,
                        affectedUserId: targetUserId || userId,
                        skillId,
                        casterUserId: userId,
                        effectType: skill.skill_code.toUpperCase(),
                        effectDetails: effectData,
                        duration: skill.duration_value
                    });
                }
            }

            // Record skill usage
            await SkillUsageHistory.recordSkillUsage({
                quizSessionId,
                userId,
                skillId,
                targetUserId,
                questionNumber,
                energyLevel,
                executionResult: executionResult.success ? 'SUCCESS' : 'FAILED',
                effectData,
                pointsAffected
            });

            // Emit skill execution event
            if (global.io) {
                global.io.to(`quiz_${quizSessionId}`).emit('skill_executed', {
                    user_id: userId,
                    skill_id: skillId,
                    skill_name: skill.skill_name,
                    skill_icon: skill.skill_icon,
                    target_user_id: targetUserId,
                    execution_result: executionResult.success ? 'SUCCESS' : 'FAILED',
                    effect_data: effectData,
                    points_affected: pointsAffected,
                    question_number: questionNumber,
                    timestamp: new Date().toISOString()
                });
            }

            return {
                success: executionResult.success,
                message: executionResult.message,
                skill_name: skill.skill_name,
                execution_result: executionResult.success ? 'SUCCESS' : 'FAILED',
                effect_data: effectData,
                points_affected: pointsAffected
            };

        } catch (error) {
            return {
                success: false,
                message: 'Failed to execute skill',
                error: error.message
            };
        }
    }

    static async getRandomSkillFromLoadout(quizSessionId, userId) {
        try {
            const skill = await QuizSkillLoadout.getRandomSkillFromLoadout(quizSessionId, userId);

            if (!skill) {
                return {
                    success: false,
                    message: 'No skills available in loadout'
                };
            }

            return {
                success: true,
                skill: {
                    skill_id: skill.skill_id,
                    skill_name: skill.skill_name,
                    skill_icon: skill.skill_icon,
                    category: skill.category,
                    description: skill.description,
                    target_type: skill.target_type
                }
            };
        } catch (error) {
            return {
                success: false,
                message: 'Failed to get random skill',
                error: error.message
            };
        }
    }

    // =====================================================
    // SKILL EFFECT PROCESSING
    // =====================================================

    static async processQuestionEffects(quizSessionId) {
        try {
            const result = await ActiveSkillEffect.processQuestionEffects(quizSessionId);

            if (result.success && global.io) {
                // Emit effect updates
                global.io.to(`quiz_${quizSessionId}`).emit('effects_processed', {
                    expired_effects: result.expired_effects.length,
                    continuing_effects: result.continuing_effects.length,
                    timestamp: new Date().toISOString()
                });
            }

            return result;
        } catch (error) {
            return {
                success: false,
                message: 'Failed to process question effects',
                error: error.message
            };
        }
    }

    static async getActiveEffects(quizSessionId, userId = null) {
        try {
            const effects = await ActiveSkillEffect.getActiveEffects(quizSessionId, userId);

            return {
                success: true,
                quiz_session_id: quizSessionId,
                user_id: userId,
                active_effects: effects,
                count: effects.length
            };
        } catch (error) {
            return {
                success: false,
                message: 'Failed to fetch active effects',
                error: error.message
            };
        }
    }

    // =====================================================
    // SKILL EXECUTION METHODS BY CATEGORY
    // =====================================================

    static async executeAttackSkill(skill, executionData, gameState) {
        const { quizSessionId, userId, targetUserId } = executionData;

        switch (skill.skill_code) {
            case 'blackhole':
                // Leader nhận 0 điểm trong 3 câu
                return {
                    success: true,
                    message: 'Blackhole applied to leader',
                    effectData: { points_multiplier: 0 },
                    pointsAffected: 0
                };

            case 'steal':
                // Lấy 50% điểm nếu target đúng, mất 10% nếu target sai
                return {
                    success: true,
                    message: 'Steal effect applied',
                    effectData: {
                        steal_percentage: 0.5,
                        risk_percentage: 0.1
                    },
                    pointsAffected: 0
                };

            case 'break':
                // Reset streak cao nhất về 0
                return {
                    success: true,
                    message: 'Streak broken',
                    effectData: { action: 'reset_streak' },
                    pointsAffected: 0
                };

            case 'slow':
                // Giảm speed bonus time từ 5s xuống 3s
                return {
                    success: true,
                    message: 'Speed reduction applied',
                    effectData: { speed_bonus_time: 3 },
                    pointsAffected: 0
                };

            default:
                return { success: false, message: 'Unknown attack skill' };
        }
    }

    static async executeDefenseSkill(skill, executionData) {
        const { quizSessionId, userId } = executionData;

        switch (skill.skill_code) {
            case 'shield':
                // Miễn nhiễm tấn công trong 2 câu
                return {
                    success: true,
                    message: 'Shield activated',
                    effectData: { immunity: 'attack_skills' },
                    pointsAffected: 0
                };

            case 'lock':
                // Khóa thứ hạng trong 2 câu
                return {
                    success: true,
                    message: 'Position locked',
                    effectData: { lock_type: 'ranking_position' },
                    pointsAffected: 0
                };

            case 'cleanse':
                // Loại bỏ hiệu ứng xấu
                await ActiveSkillEffect.removeUserEffects(quizSessionId, userId, 'NEGATIVE');
                return {
                    success: true,
                    message: 'Negative effects cleansed',
                    effectData: { action: 'remove_negative_effects' },
                    pointsAffected: 0
                };

            default:
                return { success: false, message: 'Unknown defense skill' };
        }
    }

    static async executeBurstSkill(skill, executionData) {
        const { quizSessionId, userId } = executionData;

        switch (skill.skill_code) {
            case 'double':
                // Nhân đôi điểm câu tiếp theo
                return {
                    success: true,
                    message: 'Double points activated',
                    effectData: {
                        points_multiplier: 2,
                        risk_percentage: 0.2
                    },
                    pointsAffected: 0
                };

            case 'lucky':
                // Tăng 50% điểm an toàn
                return {
                    success: true,
                    message: 'Lucky boost activated',
                    effectData: {
                        points_multiplier: 1.5,
                        risk_percentage: 0
                    },
                    pointsAffected: 0
                };

            case 'triple':
                // Nhân ba điểm câu tiếp theo
                return {
                    success: true,
                    message: 'Triple points activated',
                    effectData: {
                        points_multiplier: 3,
                        risk_percentage: 0.3
                    },
                    pointsAffected: 0
                };

            case 'perfect':
                // Đảm bảo câu trả lời đúng
                return {
                    success: true,
                    message: 'Perfect answer guaranteed',
                    effectData: {
                        auto_correct: true,
                        speed_bonus: true
                    },
                    pointsAffected: 0
                };

            case 'quintuple':
                // Nhân năm điểm câu tiếp theo
                return {
                    success: true,
                    message: 'Quintuple points activated',
                    effectData: {
                        points_multiplier: 5,
                        risk_percentage: 0.5
                    },
                    pointsAffected: 0
                };

            default:
                return { success: false, message: 'Unknown burst skill' };
        }
    }

    static async executeSpecialSkill(skill, executionData, gameState) {
        const { quizSessionId, userId, targetUserId } = executionData;

        switch (skill.skill_code) {
            case 'swap':
                // Hoán đổi thứ hạng với player khác
                return {
                    success: true,
                    message: 'Position swapped',
                    effectData: {
                        action: 'swap_positions',
                        target_user_id: targetUserId
                    },
                    pointsAffected: 0
                };

            case 'dice':
                // Hiệu ứng ngẫu nhiên
                const randomEffects = [
                    { type: 'points_boost', value: 1.5 },
                    { type: 'speed_boost', value: 2 },
                    { type: 'streak_bonus', value: 1 },
                    { type: 'energy_boost', value: 25 },
                    { type: 'shield', value: 1 },
                    { type: 'double_points', value: 2 }
                ];
                const randomEffect = randomEffects[Math.floor(Math.random() * randomEffects.length)];

                return {
                    success: true,
                    message: `Dice rolled: ${randomEffect.type}`,
                    effectData: randomEffect,
                    pointsAffected: 0
                };

            case 'energy':
                // Hồi phục năng lượng cho tất cả
                return {
                    success: true,
                    message: 'Energy restored for all players',
                    effectData: {
                        action: 'restore_energy',
                        amount: 50,
                        target: 'all_players'
                    },
                    pointsAffected: 0
                };

            default:
                return { success: false, message: 'Unknown special skill' };
        }
    }

    static async executeUltimateSkill(skill, executionData) {
        const { quizSessionId, userId } = executionData;

        switch (skill.skill_code) {
            case 'king':
                // Chế độ thần thánh 3 câu
                return {
                    success: true,
                    message: 'King mode activated',
                    effectData: {
                        immunity: 'all_attacks',
                        points_multiplier: 2,
                        god_mode: true
                    },
                    pointsAffected: 0
                };

            case 'phoenix':
                // Hồi sinh từ vị trí cuối
                return {
                    success: true,
                    message: 'Phoenix resurrection activated',
                    effectData: {
                        action: 'resurrection',
                        condition: 'bottom_3_to_top_3',
                        shield_duration: 2
                    },
                    pointsAffected: 0
                };

            default:
                return { success: false, message: 'Unknown ultimate skill' };
        }
    }

    // =====================================================
    // STATISTICS & ANALYTICS
    // =====================================================

    static async getSkillUsageStats(options = {}) {
        try {
            const stats = await SkillUsageHistory.getMostUsedSkills(options);

            return {
                success: true,
                skill_usage_stats: stats
            };
        } catch (error) {
            return {
                success: false,
                message: 'Failed to fetch skill usage statistics',
                error: error.message
            };
        }
    }

    static async getUserSkillStats(userId) {
        try {
            const stats = await UserSkill.getUserSkillStats(userId);

            return {
                success: true,
                user_id: userId,
                skill_statistics: stats
            };
        } catch (error) {
            return {
                success: false,
                message: 'Failed to fetch user skill statistics',
                error: error.message
            };
        }
    }
}

module.exports = SkillService;
