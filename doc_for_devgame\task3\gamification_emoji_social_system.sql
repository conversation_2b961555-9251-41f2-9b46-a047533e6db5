-- =====================================================
-- GAMIFICATION EMOJI & SOCIAL SYSTEM - DATABASE SCHEMA
-- Task 3.1: <PERSON>ệ thống Emoji & tương tác xã hội
-- =====================================================

-- 1. EMOJI TYPES TABLE - Các loại emoji có sẵn
CREATE TABLE IF NOT EXISTS "EmojiTypes" (
    "emoji_type_id" SERIAL PRIMARY KEY,
    "emoji_name" VARCHAR(100) NOT NULL UNIQUE,
    "emoji_code" VARCHAR(50) NOT NULL UNIQUE, -- e.g., 'slightly-smiling-face'
    "emoji_unicode" VARCHAR(20), -- Unicode character if applicable
    "emoji_image_path" VARCHAR(255), -- Path to emoji image
    "category" VARCHAR(30) DEFAULT 'BASIC' CHECK ("category" IN ('BASIC', 'REACTION', 'EMOTION', 'SPECIAL', 'PREMIUM')),
    "tier_requirement" VARCHAR(20) DEFAULT 'WOOD' CHECK ("tier_requirement" IN ('WOOD', 'BRONZE', 'SILVER', 'GOLD', 'PLATINUM', 'ONYX', 'SAPPHIRE', 'RUBY', 'AMETHYST', 'MASTER')),
    "unlock_method" VARCHAR(30) DEFAULT 'TIER_PROGRESSION' CHECK ("unlock_method" IN ('TIER_PROGRESSION', 'EGG_DROP', 'ACHIEVEMENT', 'KRISTAL_PURCHASE', 'SPECIAL_EVENT')),
    "rarity" VARCHAR(20) DEFAULT 'COMMON' CHECK ("rarity" IN ('COMMON', 'RARE', 'EPIC', 'LEGENDARY')),
    "kristal_price" INTEGER DEFAULT 0, -- Price if purchasable with Kristal
    "is_purchasable" BOOLEAN DEFAULT false,
    "is_active" BOOLEAN DEFAULT true,
    "sort_order" INTEGER DEFAULT 0,
    "description" TEXT,
    "created_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 2. USER EMOJIS TABLE - Emoji mà user đã unlock
CREATE TABLE IF NOT EXISTS "UserEmojis" (
    "user_emoji_id" SERIAL PRIMARY KEY,
    "user_id" INTEGER NOT NULL REFERENCES "Users"("user_id") ON DELETE CASCADE,
    "emoji_type_id" INTEGER NOT NULL REFERENCES "EmojiTypes"("emoji_type_id") ON DELETE CASCADE,
    "unlocked_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "unlock_source" VARCHAR(50) DEFAULT 'TIER_PROGRESSION' CHECK ("unlock_source" IN ('TIER_PROGRESSION', 'EGG_DROP', 'ACHIEVEMENT', 'KRISTAL_PURCHASE', 'ADMIN_GRANT', 'SPECIAL_EVENT')),
    "unlock_metadata" JSONB DEFAULT '{}', -- Additional info about unlock (egg_id, achievement_id, etc.)
    "is_favorite" BOOLEAN DEFAULT false,
    "usage_count" INTEGER DEFAULT 0,
    "created_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE("user_id", "emoji_type_id")
);

-- 3. EMOJI USAGE HISTORY TABLE - Lịch sử sử dụng emoji
CREATE TABLE IF NOT EXISTS "EmojiUsageHistory" (
    "usage_id" SERIAL PRIMARY KEY,
    "user_id" INTEGER NOT NULL REFERENCES "Users"("user_id") ON DELETE CASCADE,
    "emoji_type_id" INTEGER NOT NULL REFERENCES "EmojiTypes"("emoji_type_id") ON DELETE CASCADE,
    "usage_context" VARCHAR(30) NOT NULL CHECK ("usage_context" IN ('PRE_QUIZ', 'DURING_QUIZ', 'POST_QUIZ', 'PROFILE', 'SOCIAL_REACTION', 'ACHIEVEMENT_CELEBRATION')),
    "target_user_id" INTEGER REFERENCES "Users"("user_id") ON DELETE SET NULL, -- If emoji is directed at someone
    "quiz_session_id" INTEGER, -- If used during quiz
    "metadata" JSONB DEFAULT '{}', -- Context-specific data
    "created_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 4. SOCIAL INTERACTIONS TABLE - Tương tác xã hội giữa users
CREATE TABLE IF NOT EXISTS "SocialInteractions" (
    "interaction_id" SERIAL PRIMARY KEY,
    "from_user_id" INTEGER NOT NULL REFERENCES "Users"("user_id") ON DELETE CASCADE,
    "to_user_id" INTEGER NOT NULL REFERENCES "Users"("user_id") ON DELETE CASCADE,
    "interaction_type" VARCHAR(30) NOT NULL CHECK ("interaction_type" IN ('EMOJI_REACTION', 'ENCOURAGEMENT', 'CELEBRATION', 'APPRECIATION', 'SUPPORT')),
    "emoji_type_id" INTEGER REFERENCES "EmojiTypes"("emoji_type_id") ON DELETE SET NULL,
    "context" VARCHAR(50), -- 'quiz_completion', 'tier_promotion', 'achievement', etc.
    "context_id" INTEGER, -- ID of related entity (quiz_id, achievement_id, etc.)
    "is_positive" BOOLEAN DEFAULT true,
    "metadata" JSONB DEFAULT '{}',
    "created_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 5. USER SOCIAL STATS TABLE - Thống kê tương tác xã hội
CREATE TABLE IF NOT EXISTS "UserSocialStats" (
    "user_id" INTEGER PRIMARY KEY REFERENCES "Users"("user_id") ON DELETE CASCADE,
    "total_emojis_unlocked" INTEGER DEFAULT 0,
    "total_emoji_usage" INTEGER DEFAULT 0,
    "positive_interactions_sent" INTEGER DEFAULT 0,
    "positive_interactions_received" INTEGER DEFAULT 0,
    "social_reputation_score" DECIMAL(5,2) DEFAULT 0.00, -- 0-100 score
    "favorite_emoji_id" INTEGER REFERENCES "EmojiTypes"("emoji_type_id") ON DELETE SET NULL,
    "most_used_context" VARCHAR(30),
    "last_social_activity" TIMESTAMP,
    "created_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- User emoji lookups
CREATE INDEX IF NOT EXISTS "idx_user_emojis_user_id" ON "UserEmojis"("user_id");
CREATE INDEX IF NOT EXISTS "idx_user_emojis_emoji_type" ON "UserEmojis"("emoji_type_id");
CREATE INDEX IF NOT EXISTS "idx_user_emojis_unlock_source" ON "UserEmojis"("unlock_source");

-- Emoji usage tracking
CREATE INDEX IF NOT EXISTS "idx_emoji_usage_user_id" ON "EmojiUsageHistory"("user_id");
CREATE INDEX IF NOT EXISTS "idx_emoji_usage_context" ON "EmojiUsageHistory"("usage_context");
CREATE INDEX IF NOT EXISTS "idx_emoji_usage_created_at" ON "EmojiUsageHistory"("created_at");

-- Social interactions
CREATE INDEX IF NOT EXISTS "idx_social_interactions_from_user" ON "SocialInteractions"("from_user_id");
CREATE INDEX IF NOT EXISTS "idx_social_interactions_to_user" ON "SocialInteractions"("to_user_id");
CREATE INDEX IF NOT EXISTS "idx_social_interactions_type" ON "SocialInteractions"("interaction_type");
CREATE INDEX IF NOT EXISTS "idx_social_interactions_created_at" ON "SocialInteractions"("created_at");

-- Emoji types
CREATE INDEX IF NOT EXISTS "idx_emoji_types_tier_requirement" ON "EmojiTypes"("tier_requirement");
CREATE INDEX IF NOT EXISTS "idx_emoji_types_unlock_method" ON "EmojiTypes"("unlock_method");
CREATE INDEX IF NOT EXISTS "idx_emoji_types_rarity" ON "EmojiTypes"("rarity");
CREATE INDEX IF NOT EXISTS "idx_emoji_types_active" ON "EmojiTypes"("is_active");

-- =====================================================
-- INITIAL EMOJI DATA - 100+ Emojis from vector-emojis-pack
-- =====================================================

-- TIER 1-3 EMOJIS (Wood/Bronze/Silver - Level 1-36)
INSERT INTO "EmojiTypes" ("emoji_name", "emoji_code", "emoji_image_path", "category", "tier_requirement", "unlock_method", "rarity", "sort_order", "description") VALUES
-- Basic Happy Expressions
('Slightly Smiling Face', 'slightly-smiling-face', '/vector-emojis-pack/slightly-smiling-face.svg', 'BASIC', 'WOOD', 'TIER_PROGRESSION', 'COMMON', 1, 'Basic friendly expression'),
('Grinning Face', 'grinning-face', '/vector-emojis-pack/grinning-face.svg', 'BASIC', 'WOOD', 'TIER_PROGRESSION', 'COMMON', 2, 'Happy and excited'),
('Beaming Face with Smiling Eyes', 'beaming-face-with-smiling-eyes', '/vector-emojis-pack/beaming-face-with-smiling-eyes.svg', 'BASIC', 'BRONZE', 'TIER_PROGRESSION', 'COMMON', 3, 'Very happy expression'),

-- Basic Reactions
('Astonished Face', 'astonished-face', '/vector-emojis-pack/astonished-face.svg', 'REACTION', 'BRONZE', 'TIER_PROGRESSION', 'COMMON', 4, 'Surprised reaction'),
('Confused Face', 'confused-face', '/vector-emojis-pack/confused-face.svg', 'REACTION', 'BRONZE', 'TIER_PROGRESSION', 'COMMON', 5, 'Puzzled expression'),
('Thinking Face', 'thinking-face', '/vector-emojis-pack/thinking-face.svg', 'REACTION', 'SILVER', 'TIER_PROGRESSION', 'COMMON', 6, 'Contemplating something'),

-- Simple Emotions
('Relieved Face', 'relieved-face', '/vector-emojis-pack/relieved-face.svg', 'EMOTION', 'SILVER', 'TIER_PROGRESSION', 'COMMON', 7, 'Feeling relieved'),
('Worried Face', 'worried-face', '/vector-emojis-pack/worried-face.svg', 'EMOTION', 'SILVER', 'TIER_PROGRESSION', 'COMMON', 8, 'Anxious expression'),
('Sleepy Face', 'sleepy-face', '/vector-emojis-pack/sleepy-face.svg', 'EMOTION', 'SILVER', 'TIER_PROGRESSION', 'COMMON', 9, 'Tired or sleepy'),

-- Encouragement
('Thumbs Up', 'thumbs-up', '/vector-emojis-pack/thumbs-up.svg', 'REACTION', 'WOOD', 'TIER_PROGRESSION', 'COMMON', 10, 'Approval and encouragement'),
('Hugging Face', 'hugging-face', '/vector-emojis-pack/hugging-face.svg', 'EMOTION', 'BRONZE', 'TIER_PROGRESSION', 'COMMON', 11, 'Warm and supportive'),
('Winking Face', 'winking-face', '/vector-emojis-pack/winking-face.svg', 'BASIC', 'BRONZE', 'TIER_PROGRESSION', 'COMMON', 12, 'Playful wink');

-- TIER 4-6 EMOJIS (Gold/Platinum/Onyx - Level 37-72)
INSERT INTO "EmojiTypes" ("emoji_name", "emoji_code", "emoji_image_path", "category", "tier_requirement", "unlock_method", "rarity", "sort_order", "description") VALUES
-- Confidence
('Cowboy Hat Face', 'cowboy-hat-face', '/vector-emojis-pack/cowboy-hat-face.svg', 'SPECIAL', 'GOLD', 'TIER_PROGRESSION', 'RARE', 13, 'Cool and confident'),
('Nerd Face', 'nerd-face', '/vector-emojis-pack/nerd-face.svg', 'SPECIAL', 'GOLD', 'TIER_PROGRESSION', 'RARE', 14, 'Smart and studious'),
('Smirking Face', 'smirking-face', '/vector-emojis-pack/smirking-face.svg', 'EMOTION', 'GOLD', 'TIER_PROGRESSION', 'RARE', 15, 'Confident smirk'),

-- Quiz Reactions
('Star-Struck', 'star-struck', '/vector-emojis-pack/star-struck.svg', 'REACTION', 'GOLD', 'TIER_PROGRESSION', 'RARE', 16, 'Amazed and impressed'),
('Exploding Head', 'exploding-head', '/vector-emojis-pack/exploding-head.svg', 'REACTION', 'PLATINUM', 'TIER_PROGRESSION', 'RARE', 17, 'Mind blown reaction'),
('Dizzy Face', 'dizzy-face', '/vector-emojis-pack/dizzy-face.svg', 'REACTION', 'PLATINUM', 'TIER_PROGRESSION', 'RARE', 18, 'Overwhelmed or confused'),

-- Playful
('Face with Tongue', 'face-with-tongue', '/vector-emojis-pack/face-with-tongue.svg', 'EMOTION', 'PLATINUM', 'TIER_PROGRESSION', 'RARE', 19, 'Playful and cheeky'),
('Zany Face', 'zany-face', '/vector-emojis-pack/zany-face.svg', 'EMOTION', 'ONYX', 'TIER_PROGRESSION', 'RARE', 20, 'Crazy and fun'),
('Partying Face', 'partying-face', '/vector-emojis-pack/partying-face.svg', 'SPECIAL', 'ONYX', 'TIER_PROGRESSION', 'RARE', 21, 'Celebration time'),

-- Advanced Emotions
('Face with Monocle', 'face-with-monocle', '/vector-emojis-pack/face-with-monocle.svg', 'SPECIAL', 'ONYX', 'TIER_PROGRESSION', 'RARE', 22, 'Sophisticated and analytical'),
('Disguised Face', 'disguised-face', '/vector-emojis-pack/disguised-face.svg', 'SPECIAL', 'ONYX', 'TIER_PROGRESSION', 'RARE', 23, 'Mysterious and secretive'),
('Shushing Face', 'shushing-face', '/vector-emojis-pack/shushing-face.svg', 'REACTION', 'ONYX', 'TIER_PROGRESSION', 'RARE', 24, 'Keep it quiet');

-- TIER 7-10 EMOJIS (Sapphire/Ruby/Amethyst/Master - Level 73-120+)
INSERT INTO "EmojiTypes" ("emoji_name", "emoji_code", "emoji_image_path", "category", "tier_requirement", "unlock_method", "rarity", "sort_order", "description") VALUES
-- Mastery Emotions
('Face in Clouds', 'face-in-clouds', '/vector-emojis-pack/face-in-clouds.svg', 'EMOTION', 'SAPPHIRE', 'TIER_PROGRESSION', 'EPIC', 25, 'Dreamy and transcendent'),
('Melting Face', 'melting-face', '/vector-emojis-pack/melting-face.svg', 'EMOTION', 'SAPPHIRE', 'TIER_PROGRESSION', 'EPIC', 26, 'Overwhelmed by heat or emotion'),
('Face with Spiral Eyes', 'face-with-spiral-eyes', '/vector-emojis-pack/face-with-spiral-eyes.svg', 'REACTION', 'RUBY', 'TIER_PROGRESSION', 'EPIC', 27, 'Hypnotized or dizzy'),

-- Special Reactions
('Face Vomiting', 'face-vomiting', '/vector-emojis-pack/face-vomiting.svg', 'REACTION', 'RUBY', 'TIER_PROGRESSION', 'EPIC', 28, 'Disgusted reaction'),
('Face Screaming in Fear', 'face-screaming-in-fear', '/vector-emojis-pack/face-screaming-in-fear.svg', 'EMOTION', 'RUBY', 'TIER_PROGRESSION', 'EPIC', 29, 'Terrified expression'),
('Robot', 'robot', '/vector-emojis-pack/robot.svg', 'SPECIAL', 'AMETHYST', 'TIER_PROGRESSION', 'EPIC', 30, 'Mechanical and precise'),

-- Premium Expressions
('Angry Face with Horns', 'angry-face-with-horns', '/vector-emojis-pack/angry-face-with-horns.svg', 'EMOTION', 'AMETHYST', 'TIER_PROGRESSION', 'EPIC', 31, 'Devilishly angry'),
('Smiling Face with Horns', 'smiling-face-with-horns', '/vector-emojis-pack/smiling-face-with-horns.svg', 'EMOTION', 'AMETHYST', 'TIER_PROGRESSION', 'EPIC', 32, 'Mischievously happy'),
('Skull', 'skull', '/vector-emojis-pack/skull.svg', 'SPECIAL', 'MASTER', 'TIER_PROGRESSION', 'EPIC', 33, 'Death or extreme reaction'),

-- Master Exclusive
('Pile of Poo', 'pile-of-poo', '/vector-emojis-pack/pile-of-poo.svg', 'SPECIAL', 'MASTER', 'TIER_PROGRESSION', 'LEGENDARY', 34, 'Humorous and irreverent'),
('Clown Face', 'clown-face', '/vector-emojis-pack/clown-face.svg', 'SPECIAL', 'MASTER', 'TIER_PROGRESSION', 'LEGENDARY', 35, 'Playful and entertaining'),
('Face with Symbols on Mouth', 'face-with-symbols-on-mouth', '/vector-emojis-pack/face-with-symbols-on-mouth.svg', 'REACTION', 'MASTER', 'TIER_PROGRESSION', 'LEGENDARY', 36, 'Censored strong reaction');

-- EGG REWARD EMOJIS (Unlocked through egg drops)
INSERT INTO "EmojiTypes" ("emoji_name", "emoji_code", "emoji_image_path", "category", "tier_requirement", "unlock_method", "rarity", "sort_order", "description") VALUES
-- Angel Egg Emojis
('Smiling Face with Halo', 'smiling-face-with-halo', '/vector-emojis-pack/smiling-face-with-halo.svg', 'SPECIAL', 'WOOD', 'EGG_DROP', 'RARE', 37, 'Pure and innocent'),

-- Demon Egg Emojis (already added above)

-- Party Egg Emojis (some already added)

-- Ice/Cold Themed
('Cold Face', 'cold-face', '/vector-emojis-pack/cold-face.svg', 'EMOTION', 'WOOD', 'EGG_DROP', 'COMMON', 38, 'Feeling cold'),
('Expressionless Face', 'expressionless-face', '/vector-emojis-pack/expressionless-face.svg', 'EMOTION', 'WOOD', 'EGG_DROP', 'COMMON', 39, 'Neutral expression'),
('Neutral Face', 'neutral-face', '/vector-emojis-pack/neutral-face.svg', 'EMOTION', 'WOOD', 'EGG_DROP', 'COMMON', 40, 'Completely neutral'),

-- Ocean/Sea Themed
('Drooling Face', 'drooling-face', '/vector-emojis-pack/drooling-face.svg', 'EMOTION', 'WOOD', 'EGG_DROP', 'COMMON', 41, 'Desire or hunger'),
('Winking Face with Tongue', 'winking-face-with-tongue', '/vector-emojis-pack/winking-face-with-tongue.svg', 'EMOTION', 'WOOD', 'EGG_DROP', 'RARE', 42, 'Playfully cheeky'),

-- Cat/Dog Themed
('Yawning Face', 'yawning-face', '/vector-emojis-pack/yawning-face.svg', 'EMOTION', 'WOOD', 'EGG_DROP', 'COMMON', 43, 'Tired or bored'),
('Kissing Face', 'kissing-face', '/vector-emojis-pack/kissing-face.svg', 'EMOTION', 'WOOD', 'EGG_DROP', 'COMMON', 44, 'Affectionate kiss'),

-- Tropical/Summer
('Face Savoring Food', 'face-savoring-food', '/vector-emojis-pack/face-savoring-food.svg', 'EMOTION', 'WOOD', 'EGG_DROP', 'COMMON', 45, 'Enjoying something delicious'),
('Face Blowing a Kiss', 'face-blowing-a-kiss', '/vector-emojis-pack/face-blowing-a-kiss.svg', 'EMOTION', 'WOOD', 'EGG_DROP', 'RARE', 46, 'Sending love'),

-- Mining/Work Themed
('Persevering Face', 'persevering-face', '/vector-emojis-pack/persevering-face.svg', 'EMOTION', 'WOOD', 'EGG_DROP', 'COMMON', 47, 'Determined and hardworking'),
('Grimacing Face', 'grimacing-face', '/vector-emojis-pack/grimacing-face.svg', 'EMOTION', 'WOOD', 'EGG_DROP', 'COMMON', 48, 'Awkward or uncomfortable'),
('Tired Face', 'tired-face', '/vector-emojis-pack/tired-face.svg', 'EMOTION', 'WOOD', 'EGG_DROP', 'COMMON', 49, 'Exhausted from work'),

-- Winter/Mountain
('Fearful Face', 'fearful-face', '/vector-emojis-pack/fearful-face.svg', 'EMOTION', 'WOOD', 'EGG_DROP', 'COMMON', 50, 'Scared or anxious');

-- PREMIUM KRISTAL EMOJIS
INSERT INTO "EmojiTypes" ("emoji_name", "emoji_code", "emoji_image_path", "category", "tier_requirement", "unlock_method", "rarity", "kristal_price", "is_purchasable", "sort_order", "description") VALUES
-- Animated Variants (Premium versions of popular emojis)
('Premium Grinning Face', 'premium-grinning-face', '/vector-emojis-pack/premium-grinning-face.svg', 'PREMIUM', 'GOLD', 'KRISTAL_PURCHASE', 'EPIC', 25, true, 51, 'Animated version of grinning face'),
('Premium Star-Struck', 'premium-star-struck', '/vector-emojis-pack/premium-star-struck.svg', 'PREMIUM', 'GOLD', 'KRISTAL_PURCHASE', 'EPIC', 30, true, 52, 'Animated star-struck with sparkles'),
('Premium Partying Face', 'premium-partying-face', '/vector-emojis-pack/premium-partying-face.svg', 'PREMIUM', 'PLATINUM', 'KRISTAL_PURCHASE', 'EPIC', 35, true, 53, 'Animated party face with confetti'),

-- Master Tier Exclusive Kristal Emojis
('Diamond Face', 'diamond-face', '/vector-emojis-pack/diamond-face.svg', 'PREMIUM', 'MASTER', 'KRISTAL_PURCHASE', 'LEGENDARY', 100, true, 54, 'Exclusive diamond-encrusted face'),
('Golden Crown Face', 'golden-crown-face', '/vector-emojis-pack/golden-crown-face.svg', 'PREMIUM', 'MASTER', 'KRISTAL_PURCHASE', 'LEGENDARY', 80, true, 55, 'Royal golden crown emoji'),
('Master Mind', 'master-mind', '/vector-emojis-pack/master-mind.svg', 'PREMIUM', 'MASTER', 'KRISTAL_PURCHASE', 'LEGENDARY', 120, true, 56, 'Ultimate master tier emoji');

-- =====================================================
-- FUNCTIONS AND TRIGGERS
-- =====================================================

-- Function to update user social stats
CREATE OR REPLACE FUNCTION update_user_social_stats()
RETURNS TRIGGER AS $$
BEGIN
    -- Update stats when user unlocks new emoji
    IF TG_TABLE_NAME = 'UserEmojis' AND TG_OP = 'INSERT' THEN
        INSERT INTO "UserSocialStats" ("user_id", "total_emojis_unlocked", "created_at")
        VALUES (NEW.user_id, 1, CURRENT_TIMESTAMP)
        ON CONFLICT ("user_id") 
        DO UPDATE SET 
            "total_emojis_unlocked" = "UserSocialStats"."total_emojis_unlocked" + 1,
            "updated_at" = CURRENT_TIMESTAMP;
    END IF;
    
    -- Update stats when user uses emoji
    IF TG_TABLE_NAME = 'EmojiUsageHistory' AND TG_OP = 'INSERT' THEN
        -- Update user emoji usage count
        UPDATE "UserEmojis" 
        SET "usage_count" = "usage_count" + 1,
            "updated_at" = CURRENT_TIMESTAMP
        WHERE "user_id" = NEW.user_id AND "emoji_type_id" = NEW.emoji_type_id;
        
        -- Update user social stats
        INSERT INTO "UserSocialStats" ("user_id", "total_emoji_usage", "last_social_activity", "created_at")
        VALUES (NEW.user_id, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        ON CONFLICT ("user_id") 
        DO UPDATE SET 
            "total_emoji_usage" = "UserSocialStats"."total_emoji_usage" + 1,
            "last_social_activity" = CURRENT_TIMESTAMP,
            "updated_at" = CURRENT_TIMESTAMP;
    END IF;
    
    -- Update stats for social interactions
    IF TG_TABLE_NAME = 'SocialInteractions' AND TG_OP = 'INSERT' THEN
        -- Update sender stats
        INSERT INTO "UserSocialStats" ("user_id", "positive_interactions_sent", "created_at")
        VALUES (NEW.from_user_id, CASE WHEN NEW.is_positive THEN 1 ELSE 0 END, CURRENT_TIMESTAMP)
        ON CONFLICT ("user_id") 
        DO UPDATE SET 
            "positive_interactions_sent" = "UserSocialStats"."positive_interactions_sent" + CASE WHEN NEW.is_positive THEN 1 ELSE 0 END,
            "updated_at" = CURRENT_TIMESTAMP;
            
        -- Update receiver stats
        INSERT INTO "UserSocialStats" ("user_id", "positive_interactions_received", "created_at")
        VALUES (NEW.to_user_id, CASE WHEN NEW.is_positive THEN 1 ELSE 0 END, CURRENT_TIMESTAMP)
        ON CONFLICT ("user_id") 
        DO UPDATE SET 
            "positive_interactions_received" = "UserSocialStats"."positive_interactions_received" + CASE WHEN NEW.is_positive THEN 1 ELSE 0 END,
            "updated_at" = CURRENT_TIMESTAMP;
    END IF;
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Create triggers
DROP TRIGGER IF EXISTS trigger_update_social_stats_emoji_unlock ON "UserEmojis";
CREATE TRIGGER trigger_update_social_stats_emoji_unlock
    AFTER INSERT ON "UserEmojis"
    FOR EACH ROW EXECUTE FUNCTION update_user_social_stats();

DROP TRIGGER IF EXISTS trigger_update_social_stats_emoji_usage ON "EmojiUsageHistory";
CREATE TRIGGER trigger_update_social_stats_emoji_usage
    AFTER INSERT ON "EmojiUsageHistory"
    FOR EACH ROW EXECUTE FUNCTION update_user_social_stats();

DROP TRIGGER IF EXISTS trigger_update_social_stats_interactions ON "SocialInteractions";
CREATE TRIGGER trigger_update_social_stats_interactions
    AFTER INSERT ON "SocialInteractions"
    FOR EACH ROW EXECUTE FUNCTION update_user_social_stats();

-- =====================================================
-- VIEWS FOR ANALYTICS
-- =====================================================

-- View for emoji popularity
CREATE OR REPLACE VIEW "EmojiPopularityStats" AS
SELECT 
    et."emoji_type_id",
    et."emoji_name",
    et."emoji_code",
    et."category",
    et."tier_requirement",
    et."rarity",
    COUNT(ue."user_emoji_id") as "total_unlocked_by_users",
    COALESCE(SUM(ue."usage_count"), 0) as "total_usage_count",
    COALESCE(AVG(ue."usage_count"), 0) as "avg_usage_per_user",
    COUNT(CASE WHEN ue."is_favorite" = true THEN 1 END) as "favorite_count"
FROM "EmojiTypes" et
LEFT JOIN "UserEmojis" ue ON et."emoji_type_id" = ue."emoji_type_id"
WHERE et."is_active" = true
GROUP BY et."emoji_type_id", et."emoji_name", et."emoji_code", et."category", et."tier_requirement", et."rarity"
ORDER BY "total_usage_count" DESC;

-- View for user social rankings
CREATE OR REPLACE VIEW "UserSocialRankings" AS
SELECT 
    u."user_id",
    u."username",
    u."display_name",
    uss."total_emojis_unlocked",
    uss."total_emoji_usage",
    uss."positive_interactions_sent",
    uss."positive_interactions_received",
    uss."social_reputation_score",
    RANK() OVER (ORDER BY uss."social_reputation_score" DESC) as "reputation_rank",
    RANK() OVER (ORDER BY uss."total_emoji_usage" DESC) as "usage_rank",
    RANK() OVER (ORDER BY uss."positive_interactions_sent" DESC) as "kindness_rank"
FROM "Users" u
JOIN "UserSocialStats" uss ON u."user_id" = uss."user_id"
ORDER BY uss."social_reputation_score" DESC;

-- =====================================================
-- INITIAL SETUP COMPLETE
-- =====================================================

-- Add comment for documentation
COMMENT ON TABLE "EmojiTypes" IS 'Master table of all available emojis with unlock requirements and metadata';
COMMENT ON TABLE "UserEmojis" IS 'Tracks which emojis each user has unlocked and their usage statistics';
COMMENT ON TABLE "EmojiUsageHistory" IS 'Complete history of emoji usage across different contexts';
COMMENT ON TABLE "SocialInteractions" IS 'Records all social interactions between users using emojis';
COMMENT ON TABLE "UserSocialStats" IS 'Aggregated social statistics for each user';
