# Story 4.3: Tạo Avatar Customization Interface

## Status

In Progress

## Story

**As a** sinh viên,
**I want** có giao diện để thay đổi avatar, khung và hiệu ứng tên,
**so that** tùy chỉnh appearance theo sở thích và thể hiện achievements.

## Acceptance Criteria

1. Tạo `AvatarCustomization` page trong `/app/dashboard/avatar/page.tsx`
2. Tabs cho từng loại item:
   - **Avatars**: Grid hiển thị 30 avatars với unlock status
   - **Frames**: Grid hiển thị frames theo tier và achievements
   - **Name Effects**: Preview effects với unlock requirements
3. Item states với visual indicators:
   - **Owned**: <PERSON><PERSON> thể equip ngay (green border)
   - **Unlockable**: Hiển thị requirements (yellow border)
   - **Locked**: Grayed out với unlock conditions (gray border)
4. Preview functionality trước khi equip
5. Equip/unequip với real-time updates
6. Search và filter functionality cho large collections
7. Responsive design cho mobile devices

## Tasks / Subtasks

- [x] Task 1: Tạo Avatar Customization Page Structure (AC: 1, 7)

  - [x] Tạo file `frontend/src/app/dashboard/avatar/page.tsx`
  - [x] Setup page layout với responsive design
  - [x] Implement navigation breadcrumbs
  - [x] Add proper TypeScript interfaces cho page props
  - [x] Integrate với existing dashboard layout

- [x] Task 2: Implement Tabs Navigation System (AC: 2)

  - [x] Tạo `CustomizationTabs` component trong `/components/features/avatar/`
  - [x] Implement tabs cho Avatars, Frames, Name Effects
  - [x] Add tab state management với URL synchronization
  - [x] Style tabs với Radix UI và Tailwind CSS
  - [x] Add keyboard navigation support

- [x] Task 3: Build Avatar Grid Component (AC: 2, 3, 6)

  - [x] Tạo `AvatarGrid` component với lazy loading
  - [x] Implement item state visual indicators (owned/unlockable/locked)
  - [x] Add search và filter functionality
  - [x] Implement grid layout với responsive breakpoints
  - [x] Add loading states và skeleton animations

- [ ] Task 4: Build Frame Grid Component (AC: 2, 3, 6)

  - [ ] Tạo `FrameGrid` component với tier-based organization
  - [ ] Implement unlock status indicators
  - [ ] Add achievement-based filtering
  - [ ] Integrate với tier system từ gamification
  - [ ] Add hover effects và tooltips

- [ ] Task 5: Build Name Effects Preview (AC: 2, 3)

  - [ ] Tạo `NameEffectsGrid` component
  - [ ] Implement live preview cho name effects
  - [ ] Show unlock requirements cho mỗi effect
  - [ ] Add tier-based color coding
  - [ ] Implement CSS effects preview

- [ ] Task 6: Implement Preview Functionality (AC: 4)

  - [ ] Tạo `CustomizationPreview` component
  - [ ] Show combined preview của avatar + frame + name effect
  - [ ] Implement real-time preview updates
  - [ ] Add preview modal với larger display
  - [ ] Integrate với AvatarDisplay component

- [ ] Task 7: Implement Equip/Unequip Actions (AC: 5)

  - [ ] Add equip/unequip buttons với proper states
  - [ ] Integrate với avatar service API calls
  - [ ] Implement optimistic updates
  - [ ] Add confirmation dialogs cho important changes
  - [ ] Handle error states và rollback

- [ ] Task 8: Integration và Testing (AC: 1-7)
  - [ ] Connect tất cả components với useAvatar hook
  - [ ] Test responsive design trên mobile devices
  - [ ] Verify real-time updates across app
  - [ ] Test search và filter performance
  - [ ] Validate accessibility features

## Dev Agent Record

### Agent Model Used

Claude Sonnet 4 (Augment Agent)

### Debug Log References

- Task 1 started: 2025-07-29 - Tạo Avatar Customization Page Structure
- Task 1 completed: 2025-07-29 - Page structure created successfully
- Task 2 started: 2025-07-29 - Implement Tabs Navigation System
- Task 2 completed: 2025-07-29 - CustomizationTabs component created with URL sync
- Task 3 started: 2025-07-29 - Build Avatar Grid Component
- Task 3 completed: 2025-07-29 - AvatarGrid component created with full functionality

### Completion Notes

- Task 1 completed: Created main page with responsive layout, breadcrumbs, and tab structure
- Task 2 completed: Created CustomizationTabs component with URL synchronization, keyboard navigation, and responsive design
- Task 3 completed: Created AvatarGrid component with lazy loading, search/filter, visual state indicators, and responsive grid layout

### File List

- frontend/src/app/dashboard/avatar/page.tsx (created, modified)
- frontend/src/components/ui/navigation/breadcrumb.tsx (modified - added BreadcrumbPage)
- frontend/src/components/ui/navigation/index.ts (modified - exported BreadcrumbPage)
- frontend/src/components/features/avatar/customization-tabs.tsx (created)
- frontend/src/components/features/avatar/avatar-grid.tsx (created)
- frontend/src/components/features/avatar/index.ts (modified - exported CustomizationTabs, AvatarGrid)

## Dev Notes

### Previous Story Insights

**Architecture Lessons** [Source: Story 4.1, 4.2 completion notes]:

- Avatar service pattern đã được establish trong `/lib/services/api/avatar.service.ts` với full API integration
- AvatarDisplay component đã được implement trong `/components/features/avatar/avatar-display.tsx` với proper TypeScript typing
- useAvatar hook đã có sẵn trong `/lib/hooks/use-avatar.ts` với caching strategy và error handling
- TypeScript types structure đã được setup trong `/lib/types/avatar.ts` với comprehensive interfaces
- Error handling utilities đã có sẵn trong existing gamification components

### Data Models

**Avatar Data Structure** [Source: frontend/src/lib/types/avatar.ts]:

```typescript
interface AvatarData {
  avatar_id: number;
  name: string;
  description: string;
  image_path: string;
  rarity: AvatarRarity;
  unlock_type: UnlockType;
  unlock_level?: number;
}

interface UserCustomization {
  equipped_avatar_id: number;
  equipped_frame_id?: number;
  equipped_name_effect_id?: number;
}

interface UserInventory {
  avatars: AvatarData[];
  frames: AvatarFrame[];
  name_effects: NameEffect[];
}
```

**Item States** [Source: Epic 4.3 AC]:

- `owned`: User có thể equip ngay (green border)
- `unlockable`: Hiển thị requirements (yellow border)
- `locked`: Grayed out với unlock conditions (gray border)

**Rarity Types** [Source: frontend/src/lib/types/avatar.ts]:

- `AvatarRarity = "COMMON" | "UNCOMMON" | "RARE" | "EPIC" | "LEGENDARY"`

### API Specifications

**Avatar API Endpoints** [Source: frontend/src/lib/services/api/avatar.service.ts]:

- `getMyAvatarData()` - Returns complete user avatar data với customization và inventory
- `getAvailableItems()` - Returns categorized items (owned, unlockable, locked)
- `equipItem(itemType, itemId)` - Equips item với validation ownership
- `getCollectionProgress()` - Returns completion percentages by category

**API Integration Pattern** [Source: Story 4.1 completion notes]:

```typescript
const { avatarData, loading, error, equipItem, refreshData } = useAvatar();
```

### Component Specifications

**Page Location** [Source: docs/architecture/project-structure.md]:

- Main page: `frontend/src/app/dashboard/avatar/page.tsx`
- Components: `frontend/src/components/features/avatar/`
- Follow App Router pattern với Next.js 15

**Component Architecture** [Source: Story 4.2 completion notes]:

- Use existing AvatarDisplay component cho preview
- Follow feature-based organization pattern
- Use Radix UI primitives cho accessibility
- Styling với Tailwind CSS utility classes

**Responsive Design** [Source: Epic 4.3 AC]:

- Mobile-first approach với touch-friendly interfaces
- Grid layouts với responsive breakpoints
- Proper spacing và sizing cho mobile devices

### File Locations

**Page Files** [Source: docs/architecture/project-structure.md]:

- Main page: `frontend/src/app/dashboard/avatar/page.tsx`
- Components: `frontend/src/components/features/avatar/`
- Types: Already available trong `frontend/src/lib/types/avatar.ts`

**Existing Components** [Source: Story 4.2 completion notes]:

- AvatarDisplay: `frontend/src/components/features/avatar/avatar-display.tsx`
- Component exports: `frontend/src/components/features/avatar/index.ts`

**Integration Points** [Source: docs/architecture/project-structure.md]:

- Dashboard layout: `frontend/src/app/dashboard/layout.tsx`
- Navigation: `frontend/src/components/features/navigation/`

### Technical Constraints

**Framework Requirements** [Source: docs/architecture/technology-stack.md]:

- Next.js 15.3.0 với App Router và React 19
- TypeScript ^5 (Frontend only)
- Radix UI (Latest) - Component primitives cho tabs, dialogs
- Tailwind CSS ^4 - Utility-first CSS

**Performance Requirements** [Source: Epic 4.3 AC]:

- Lazy loading cho large avatar grids
- Efficient search và filtering
- Optimistic updates cho better UX
- Responsive design performance

**Visual Assets** [Source: Epic 4 description]:

- Avatar images: `/public/avatar-animal-pack/` (30 animals)
- Frame images: `/public/vector-ranks-pack/` (tier-based frames)
- Proper fallback mechanisms cho missing assets

### Integration với Existing Systems

**Gamification Integration** [Source: Epic 4 description]:

- Tier system integration cho frame unlocks
- Level progression triggers avatar unlocks
- Achievement-based unlock mechanisms

**Design System Integration** [Source: Story 4.2 completion notes]:

- Use existing Radix UI + Tailwind patterns
- Follow existing color schemes và spacing
- Consistent với existing dashboard design

**Navigation Integration** [Source: docs/architecture/project-structure.md]:

- Integrate với dashboard navigation
- Breadcrumb navigation cho better UX
- URL state management cho tabs

### Testing

**NO TESTING POLICY** [Source: docs/architecture/testing-strategy.md]:

Theo project testing policy, tất cả test files, test directories, test configurations, và test-related dependencies phải được removed hoàn toàn. Focus solely on functionality implementation với quality assurance through TypeScript compilation và basic runtime verification.

**Quality Assurance Approach**:

- TypeScript compilation success
- Application startup without errors
- Basic functionality verification trong browser
- Code review processes
- Runtime error monitoring

## Change Log

| Date       | Version | Description            | Author             |
| ---------- | ------- | ---------------------- | ------------------ |
| 2025-07-29 | 1.0     | Initial story creation | Bob (Scrum Master) |
