# 🎭 Hướng Dẫn Test Hệ Thống Emoji & Tương Tác Xã Hội

## 📋 Tổng Quan

Hệ thống Emoji & Tương tác xã hội đã được triển khai thành công với các tính năng:

- **100+ Emoji** phân loại theo 10 tier progression
- **Hệ thống mở khóa đa dạng**: Tier, Egg rewards, Kristal purchase
- **6 ngữ cảnh sử dụng**: PRE_QUIZ, DURING_QUIZ, POST_QUIZ, PROFILE, SOCIAL_REACTION, ACHIEVEMENT_CELEBRATION
- **Tương tác xã hội**: Emoji reactions, encouragement, celebration
- **Thống kê và leaderboard**: Social score, reputation, rankings

## 🚀 Khởi Động Server

```bash
cd backend
npm start
```

Server sẽ chạy trên port 8888. Lỗi Redis là bình thường nếu không có Redis server.

## 🔐 Authentication

Tất cả API endpoints yêu cầu JWT token trong header:
```
Authorization: Bearer <your_jwt_token>
```

## 📊 Database Setup

Trước khi test, cần chạy SQL script để tạo database schema:

```sql
-- Chạy file: doc_for_devgame/task3/gamification_emoji_social_system.sql
```

## 🧪 Test Cases với Postman

### 1. EMOJI COLLECTION ENDPOINTS

#### 1.1 Initialize User Emoji System
```
POST http://localhost:8888/api/emojis/initialize
Headers: Authorization: Bearer <token>
```

**Expected Response:**
```json
{
    "success": true,
    "message": "User emoji system initialized successfully",
    "data": {
        "emojis_unlocked": 5,
        "basic_emojis": ["😊", "👍", "❤️", "🎉", "💪"]
    }
}
```

#### 1.2 Get User Emoji Collection
```
GET http://localhost:8888/api/emojis/collection
Headers: Authorization: Bearer <token>
Query Params: 
- category: "emotions" (optional)
- rarity: "common" (optional)
- is_favorite: true (optional)
```

#### 1.3 Get Available Emojis for User's Tier
```
GET http://localhost:8888/api/emojis/available
Headers: Authorization: Bearer <token>
Query Params:
- tier: "BRONZE" (optional)
```

#### 1.4 Get Emojis by Category
```
GET http://localhost:8888/api/emojis/category/emotions
Headers: Authorization: Bearer <token>
```

### 2. EMOJI SHOP ENDPOINTS

#### 2.1 Get Emoji Shop
```
GET http://localhost:8888/api/emojis/shop
Headers: Authorization: Bearer <token>
```

#### 2.2 Purchase Emoji with Kristal
```
POST http://localhost:8888/api/emojis/purchase
Headers: Authorization: Bearer <token>
Body:
{
    "emoji_type_id": 15
}
```

### 3. EMOJI USAGE ENDPOINTS

#### 3.1 Use Emoji
```
POST http://localhost:8888/api/emojis/use
Headers: Authorization: Bearer <token>
Body:
{
    "emoji_type_id": 1,
    "context": "PRE_QUIZ",
    "target_user_id": 2,
    "quiz_session_id": 123,
    "metadata": {
        "quiz_subject": "Web Design",
        "difficulty": "intermediate"
    }
}
```

#### 3.2 Get Emoji Usage History
```
GET http://localhost:8888/api/emojis/usage/history
Headers: Authorization: Bearer <token>
Query Params:
- context: "PRE_QUIZ" (optional)
- timeframe: "7d" (optional)
- limit: 20 (optional)
```

#### 3.3 Get Emoji Usage Statistics
```
GET http://localhost:8888/api/emojis/usage/stats
Headers: Authorization: Bearer <token>
Query Params:
- timeframe: "30d" (optional)
```

### 4. SOCIAL INTERACTION ENDPOINTS

#### 4.1 Send Emoji Reaction
```
POST http://localhost:8888/api/social/emoji-reaction
Headers: Authorization: Bearer <token>
Body:
{
    "to_user_id": 2,
    "emoji_type_id": 5,
    "context": "quiz_completion",
    "context_id": 123,
    "metadata": {
        "quiz_score": 95,
        "subject": "CSS"
    }
}
```

#### 4.2 Send Encouragement
```
POST http://localhost:8888/api/social/encouragement
Headers: Authorization: Bearer <token>
Body:
{
    "to_user_id": 3,
    "context": "tier_promotion",
    "context_id": 456,
    "message": "Congratulations on reaching Silver tier!"
}
```

#### 4.3 Celebrate Achievement
```
POST http://localhost:8888/api/social/celebrate
Headers: Authorization: Bearer <token>
Body:
{
    "to_user_id": 4,
    "achievement_type": "perfect_score",
    "emoji_type_id": 8,
    "message": "Amazing perfect score!"
}
```

### 5. SOCIAL STATS ENDPOINTS

#### 5.1 Get User Social Stats
```
GET http://localhost:8888/api/social/stats
Headers: Authorization: Bearer <token>
Query Params:
- timeframe: "7d" (1d, 7d, 30d)
```

#### 5.2 Get Social Interaction History
```
GET http://localhost:8888/api/social/interactions/history
Headers: Authorization: Bearer <token>
Query Params:
- type: "sent" (sent, received, both)
- interaction_type: "EMOJI_REACTION" (optional)
- limit: 50 (optional)
```

#### 5.3 Get Top Social Users
```
GET http://localhost:8888/api/social/top-users
Headers: Authorization: Bearer <token>
Query Params:
- timeframe: "30d"
- limit: 10
```

### 6. SOCIAL LEADERBOARD ENDPOINTS

#### 6.1 Get Social Leaderboard
```
GET http://localhost:8888/api/social/leaderboard
Headers: Authorization: Bearer <token>
Query Params:
- criteria: "reputation" (reputation, emojis, usage, kindness, popularity)
- limit: 20
```

#### 6.2 Get User Social Rank
```
GET http://localhost:8888/api/social/rank
Headers: Authorization: Bearer <token>
Query Params:
- criteria: "reputation"
```

### 7. SOCIAL PROFILE ENDPOINTS

#### 7.1 Set Favorite Emoji
```
POST http://localhost:8888/api/social/favorite-emoji
Headers: Authorization: Bearer <token>
Body:
{
    "emoji_type_id": 12
}
```

#### 7.2 Get Own Social Profile
```
GET http://localhost:8888/api/social/profile
Headers: Authorization: Bearer <token>
```

#### 7.3 Get Another User's Social Profile
```
GET http://localhost:8888/api/social/profile/5
Headers: Authorization: Bearer <token>
```

#### 7.4 Get Emoji Details
```
GET http://localhost:8888/api/emojis/25
Headers: Authorization: Bearer <token>
```

## 🎯 Test Scenarios

### Scenario 1: New User Emoji Journey
1. Initialize emoji system → Should unlock 5 basic emojis
2. Check collection → Should show unlocked emojis
3. Use emoji in PRE_QUIZ context → Should record usage
4. Check usage stats → Should show 1 usage

### Scenario 2: Social Interaction Flow
1. User A sends emoji reaction to User B
2. User B receives notification (if implemented)
3. Check User A's sent interactions
4. Check User B's received interactions
5. Verify social stats update

### Scenario 3: Emoji Shop & Purchase
1. Check shop → Should show purchasable emojis for user's tier
2. Purchase emoji with Kristal → Should deduct currency and unlock emoji
3. Check collection → Should show newly purchased emoji

## 🔍 Debugging Tips

1. **Server không khởi động**: Kiểm tra port 8888 có bị chiếm không
2. **Database errors**: Đảm bảo đã chạy SQL script tạo tables
3. **Authentication errors**: Kiểm tra JWT token hợp lệ
4. **Model errors**: Kiểm tra associations trong models/index.js

## 📈 Expected Performance

- API response time: < 200ms
- Database queries: Optimized with indexes
- Memory usage: Stable với connection pooling
- Concurrent users: Support 100+ users

## ✅ Success Criteria

- [ ] Tất cả 21 API endpoints hoạt động
- [ ] Database schema tạo thành công
- [ ] Emoji unlocking logic đúng theo tier
- [ ] Social interactions được ghi nhận
- [ ] Statistics và leaderboard chính xác
- [ ] Error handling và validation hoạt động

## 🚨 Known Issues

1. Redis connection error là bình thường nếu không có Redis server
2. WebSocket features cần Redis để hoạt động đầy đủ
3. Background jobs sẽ không chạy nếu không có Redis

---

**Task 3.1: Hệ thống Emoji & tương tác xã hội** - ✅ **COMPLETED**

Hệ thống đã sẵn sàng cho việc test và tích hợp với frontend!
