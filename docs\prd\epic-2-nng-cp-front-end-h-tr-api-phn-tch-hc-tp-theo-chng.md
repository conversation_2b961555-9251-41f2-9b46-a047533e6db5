# Epic 2: <PERSON><PERSON><PERSON>ấp Front-end Hỗ Trợ API Phân Tích Học Tập Theo <PERSON>

**Epic Goal**: <PERSON><PERSON><PERSON> dựng lại giao diện front-end để tích hợp với hệ thống API phân tích học tập mới, chuyển từ hiển thị theo Learning Outcomes (LO) sang **hiển thị theo <PERSON>** để sinh viên dễ hiểu và sử dụng hơn.

**Business Context**: Backend đã phát triển API mới với phân tích theo chương thay vì LO. Frontend hiện tại sử dụng RadarChart và analytics cũ theo LO cần được cập nhật để phù hợp với cách tiếp cận mới.

**Integration Requirements**: Tích hợp với 3 API endpoints mới từ backend: detailed-analysis, comprehensive-analysis, và teacher-analytics APIs.

## Story 2.1: <PERSON><PERSON><PERSON> API Chapter Analytics Mới

Là một **developer**,
<PERSON><PERSON><PERSON> muố<PERSON> **tích hợp các API phân tích theo chương mới vào hệ thống**,
Để **có thể hiển thị dữ liệu phân tích theo chương thay vì LO**.

### Acceptance Criteria

1. Tạo service layer mới `chapterAnalyticsService` trong `lib/services/api/`
2. Implement TypeScript types cho Chapter Analytics API responses
3. Tích hợp 3 API endpoints chính:
   - `/quiz-results/detailed-analysis/:quiz_id/:user_id`
   - `/reports/subject/:subject_id/comprehensive-analysis/:user_id`
   - `/teacher-analytics/quiz/:quizId/comprehensive-report`
4. Maintain backward compatibility với existing radar APIs
5. Add error handling và loading states cho new APIs

### Integration Verification

- **IV1**: API calls return correct chapter-based data structure
- **IV2**: TypeScript compilation successful với new types
- **IV3**: Existing functionality không bị break

## Story 2.2: Cập Nhật Student Quiz Results Page

Là một **sinh viên**,
Tôi muốn **xem kết quả quiz theo chương thay vì LO**,
Để **hiểu rõ chương nào cần ôn tập và sections cụ thể cần xem lại**.

### Acceptance Criteria

1. Cập nhật `/dashboard/student/quizzes/result/[id]/page.tsx`
2. Thay thế `StudentRadarChart` bằng `ChapterAnalysisChart` mới
3. Hiển thị chapter strengths/weaknesses thay vì LO analysis
4. Show sections cụ thể cần ôn tập với content type (text/video/exercise)
5. Maintain existing UI layout và styling

### Integration Verification

- **IV1**: Chapter analysis hiển thị chính xác với sections
- **IV2**: Performance không giảm so với radar chart cũ
- **IV3**: Mobile responsiveness maintained

## Story 2.3: Xây Dựng Student Learning Results Dashboard

Là một **sinh viên**,
Tôi muốn **có dashboard tổng quan về tiến độ học tập theo chương**,
Để **theo dõi progress và nhận gợi ý cải thiện cụ thể**.

### Acceptance Criteria

1. Cập nhật `/dashboard/student/learning-results/page.tsx`
2. Thay thế `StudentLearningOutcomeMasteryChart` bằng `ChapterMasteryChart`
3. Implement `ChapterCompletionChart` với progress tracking
4. Add `SectionRecommendationCard` cho gợi ý cụ thể
5. Integrate với subject comprehensive analysis API

### Integration Verification

- **IV1**: Chapter mastery data accurate và up-to-date
- **IV2**: Recommendations actionable và relevant
- **IV3**: Chart performance optimized

## Story 2.4: Nâng Cấp Teacher Analytics Dashboard

Là một **giảng viên**,
Tôi muốn **có insights chi tiết về hiệu suất lớp theo chương**,
Để **điều chỉnh phương pháp giảng dạy và hỗ trợ học sinh**.

### Acceptance Criteria

1. Cập nhật `/dashboard/reports/quiz-results/page.tsx`
2. Implement `TeacherChapterAnalyticsChart` thay thế `TeacherRadarChart`
3. Add `StudentGroupChapterAnalysis` cho phân tích nhóm
4. Integrate teaching insights và recommendations
5. Add quiz comparison và benchmark features

### Integration Verification

- **IV1**: Teacher insights accurate và actionable
- **IV2**: Student group analysis helpful cho intervention
- **IV3**: Comparison features provide valuable context

## Story 2.5: Cập Nhật Real-time Quiz Monitor

Là một **giảng viên**,
Tôi muốn **monitor quiz real-time với chapter-based insights**,
Để **có thể can thiệp kịp thời khi học sinh gặp khó khăn**.

### Acceptance Criteria

1. Cập nhật `/quiz-monitor/[id]/page.tsx`
2. Add real-time chapter performance tracking
3. Implement alerts cho chapters có performance thấp
4. Show live chapter completion rates
5. Maintain existing real-time functionality

### Integration Verification

- **IV1**: Real-time updates work correctly
- **IV2**: Chapter insights update in real-time
- **IV3**: Performance monitoring không impact quiz experience

## Story 2.6: Responsive Design và Mobile Optimization

Là một **user (sinh viên/giảng viên)**,
Tôi muốn **sử dụng chapter analytics trên mobile devices**,
Để **có thể truy cập insights mọi lúc mọi nơi**.

### Acceptance Criteria

1. Optimize tất cả chapter charts cho mobile screens
2. Implement swipeable chapter navigation
3. Collapsible section recommendations
4. Touch-friendly interactive elements
5. Maintain performance trên low-end devices

### Integration Verification

- **IV1**: Mobile experience smooth và intuitive
- **IV2**: Charts readable trên small screens
- **IV3**: Touch interactions responsive

---
