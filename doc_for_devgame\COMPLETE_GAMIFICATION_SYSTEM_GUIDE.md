# 🎮 Complete Gamification System - Frontend Integration Guide

## 📋 System Overview

**Synlearnia Gamification System** - Hệ thống gamification hoàn chỉnh với 4 phases đã implement:

- **Phase 1**: XP/Level, Titles/Badges, Dynamic Scoring ✅
- **Phase 2**: Currency (SynCoin/Kristal), Avatar/Customization, Egg Rewards ✅  
- **Phase 3**: Emoji/Social, Leaderboard/Ranking ✅
- **Phase 4**: Skills System (17 skills), Real-time Quiz Racing ✅

**Total**: **50+ API endpoints**, **15+ WebSocket events**, **25+ database tables**

---

## 🗂️ Complete File Structure

### Backend Implementation
```
backend/src/
├── services/
│   ├── gamificationService.js          # XP/Level system
│   ├── titleService.js                 # Titles & badges
│   ├── dynamicScoringService.js        # Dynamic scoring
│   ├── currencyService.js              # SynCoin/Kristal economy
│   ├── avatarCustomizationService.js   # Avatar system
│   ├── eggRewardService.js             # Egg rewards
│   ├── emojiService.js                 # Emoji system
│   ├── socialService.js                # Social interactions
│   ├── leaderboardService.js           # Rankings & leaderboard
│   ├── skillService.js                 # Skills management
│   └── quizRacingService.js            # Real-time racing
├── controllers/
│   ├── gamificationController.js       # XP/Level endpoints
│   ├── titleController.js              # Titles endpoints
│   ├── dynamicScoringController.js     # Scoring endpoints
│   ├── currencyController.js           # Currency endpoints
│   ├── avatarCustomizationController.js # Avatar endpoints
│   ├── eggRewardController.js          # Egg endpoints
│   ├── emojiController.js              # Emoji endpoints
│   ├── socialController.js             # Social endpoints
│   ├── leaderboardController.js        # Leaderboard endpoints
│   ├── skillController.js              # Skills endpoints
│   └── quizRacingController.js         # Racing WebSocket events
├── routes/
│   ├── gamificationRoutes.js           # /api/gamification/*
│   ├── title.js                        # /api/titles/*
│   ├── dynamicScoringRoutes.js         # /api/scoring/*
│   ├── currencyRoutes.js               # /api/currency/*
│   ├── avatarCustomizationRoutes.js    # /api/avatar/*
│   ├── eggRewardRoutes.js              # /api/eggs/*
│   ├── emojiRoutes.js                  # /api/emojis/*
│   ├── socialRoutes.js                 # /api/social/*
│   ├── leaderboardRoutes.js            # /api/leaderboard/*
│   ├── skillRoutes.js                  # /api/skills/*
│   └── quizRacingRoutes.js             # /api/quiz-racing/*
└── models/
    ├── gamificationLevel.js            # XP/Level model
    ├── title.js                        # Titles model
    ├── userTitle.js                     # User titles
    ├── achievement.js                   # Achievements model
    ├── userAchievement.js              # User achievements
    ├── currency.js                     # Currency model
    ├── userCurrency.js                 # User balances
    ├── avatar.js                       # Avatar items
    ├── avatarFrame.js                  # Avatar frames
    ├── userInventory.js                # User inventory
    ├── eggReward.js                    # Egg rewards
    ├── userEggHistory.js               # Egg opening history
    ├── emoji.js                        # Emoji model
    ├── userEmoji.js                    # User emojis
    ├── emojiUsage.js                   # Emoji usage tracking
    ├── socialInteraction.js           # Social interactions
    ├── skill.js                       # Skills model
    ├── userSkill.js                   # User skills inventory
    ├── quizSkillLoadout.js            # Racing loadouts
    ├── skillUsageHistory.js           # Skill usage tracking
    └── activeSkillEffect.js           # Active skill effects
```

### Database Schema Files
```
doc_for_devgame/
├── phase1_gamification_schema.sql      # XP, Titles, Achievements
├── phase2_economy_schema.sql           # Currency, Avatar, Eggs
├── phase3_social_schema.sql            # Emoji, Social, Leaderboard
└── task4/
    ├── skills_system.sql               # Skills system (17 skills)
    └── quiz_racing_schema_updates.sql  # Racing tables
```

---

## 🔌 Complete API Endpoints (50+)

### Phase 1: Core Gamification (8 endpoints)
| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/api/gamification/level` | Get user XP/level info |
| POST | `/api/gamification/add-xp` | Add XP to user |
| GET | `/api/titles` | Get all available titles |
| GET | `/api/titles/user/:userId` | Get user's titles |
| POST | `/api/titles/assign` | Assign title to user |
| GET | `/api/achievements` | Get all achievements |
| GET | `/api/achievements/user/:userId` | Get user achievements |
| POST | `/api/scoring/calculate` | Calculate dynamic score |

### Phase 2: Economy & Customization (12 endpoints)
| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/api/currency/balance` | Get user currency balance |
| POST | `/api/currency/add` | Add currency to user |
| POST | `/api/currency/spend` | Spend user currency |
| GET | `/api/currency/history` | Get transaction history |
| GET | `/api/avatar/items` | Get available avatar items |
| GET | `/api/avatar/frames` | Get available frames |
| GET | `/api/avatar/inventory/:userId` | Get user inventory |
| POST | `/api/avatar/purchase` | Purchase avatar item |
| POST | `/api/avatar/equip` | Equip avatar item |
| GET | `/api/eggs/available` | Get available eggs |
| POST | `/api/eggs/open` | Open an egg |
| GET | `/api/eggs/history/:userId` | Get opening history |

### Phase 3: Social & Leaderboard (12 endpoints)
| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/api/emojis` | Get all available emojis |
| GET | `/api/emojis/user/:userId` | Get user's emojis |
| POST | `/api/emojis/purchase` | Purchase emoji |
| POST | `/api/emojis/use` | Use emoji in quiz |
| GET | `/api/social/interactions/:userId` | Get user interactions |
| POST | `/api/social/send-emoji` | Send emoji to user |
| GET | `/api/social/received/:userId` | Get received interactions |
| GET | `/api/leaderboard/global` | Global leaderboard |
| GET | `/api/leaderboard/subject/:subjectId` | Subject leaderboard |
| GET | `/api/leaderboard/weekly` | Weekly rankings |
| GET | `/api/leaderboard/tier/:tier` | Tier-based rankings |
| GET | `/api/leaderboard/analytics/:userId` | User performance analytics |

### Phase 4: Skills & Racing (18 endpoints)
| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/api/skills/shop` | Browse skills shop |
| GET | `/api/skills/shop/category/:category` | Get skills by category |
| POST | `/api/skills/purchase` | Purchase skill |
| GET | `/api/skills/inventory` | Get user's skills |
| POST | `/api/skills/loadout` | Set skill loadout |
| GET | `/api/skills/loadout` | Get current loadout |
| POST | `/api/skills/execute` | Execute skill |
| GET | `/api/skills/usage-history` | Get usage history |
| GET | `/api/skills/stats` | Get skill statistics |
| GET | `/api/skills/effects/active` | Get active effects |
| POST | `/api/skills/effects/clear` | Clear expired effects |
| GET | `/api/skills/categories` | Get skill categories |
| POST | `/api/quiz-racing/initialize` | Initialize racing session |
| GET | `/api/quiz-racing/session/:sessionId` | Get session data |
| POST | `/api/quiz-racing/loadout` | Set racing loadout |
| GET | `/api/quiz-racing/loadout/:sessionId` | Get racing loadout |
| GET | `/api/quiz-racing/stats/:sessionId` | Get racing statistics |
| GET | `/api/quiz-racing/leaderboard/:sessionId` | Get racing leaderboard |

---

## 📡 WebSocket Events (15+)

### Leaderboard Real-time Events
- `leaderboard-update` - Real-time ranking changes
- `user-level-up` - User level progression
- `achievement-unlocked` - New achievement earned

### Social Interaction Events  
- `emoji-received` - Emoji sent to user
- `social-interaction` - Social activity notification

### Quiz Racing Events
- `join-quiz-racing` - Join racing session
- `submit-racing-answer` - Submit answer in racing
- `use-skill` - Execute skill
- `skip-question` - Skip question
- `get-random-skill` - Request random skill
- `player-ready` - Ready for next round
- `energy-update` - Energy level changed
- `skill-executed` - Skill was used
- `racing-answer-result` - Answer result with bonuses
- `leaderboard-update` - Racing leaderboard update

---

## 💾 Database Schema Summary

### Phase 1 Tables (5)
- `GamificationLevels` - XP requirements per level
- `Titles` - Available titles/ranks
- `UserTitles` - User title assignments
- `Achievements` - Achievement definitions
- `UserAchievements` - User achievement progress

### Phase 2 Tables (8)
- `Currencies` - Currency types (SynCoin, Kristal)
- `UserCurrencies` - User currency balances
- `CurrencyTransactions` - Transaction history
- `Avatars` - Avatar items
- `AvatarFrames` - Avatar frames
- `UserInventory` - User item inventory
- `EggRewards` - Egg reward definitions
- `UserEggHistory` - Egg opening history

### Phase 3 Tables (6)
- `Emojis` - Available emojis
- `UserEmojis` - User emoji inventory
- `EmojiUsage` - Emoji usage tracking
- `SocialInteractions` - Social activity log
- `LeaderboardCache` - Cached leaderboard data
- `UserPerformanceAnalytics` - Performance metrics

### Phase 4 Tables (6)
- `Skills` - 17 skills definitions
- `UserSkills` - User skill inventory
- `QuizSkillLoadouts` - 4-skill loadouts per session
- `SkillUsageHistory` - Skill usage tracking
- `ActiveSkillEffects` - Active effects during racing
- `QuizRacingSessions` - Racing session metadata

**Total: 25+ tables**

---

## 🎯 Key Game Mechanics

### XP & Leveling System
```javascript
// XP calculation
const xpGain = baseXP * difficultyMultiplier * streakBonus * speedBonus;

// Level progression  
const xpRequired = 100 * Math.pow(1.5, level - 1);
```

### Currency Economy
```javascript
// SynCoin earning
const synCoinReward = correctAnswers * 10 + streakBonus + speedBonus;

// Kristal earning (premium)
const kristalReward = perfectQuiz ? 50 : achievementUnlocked ? 25 : 0;
```

### Dynamic Scoring
```javascript
// Quiz scoring with bonuses
const finalScore = baseScore + speedBonus + streakBonus + difficultyBonus;
const speedBonus = responseTime < 3000 ? 50 : responseTime < 5000 ? 25 : 0;
const streakBonus = streak >= 5 ? 100 : streak >= 3 ? 50 : 0;
```

### Energy System (Racing)
```javascript
// Energy gain in racing
const energyGain = 20 + (speedBonus ? 10 : 0) + (streak >= 3 ? 5 : 0);
// 100% energy = skill selection available
```

### Skills System (17 Skills)
```javascript
// 5 Categories
const skillCategories = {
  ATTACK: ['Blackhole', 'Steal', 'Break', 'Slow'],           // 4 skills
  DEFENSE: ['Shield', 'Lock', 'Cleanse'],                    // 3 skills  
  BURST: ['Double', 'Lucky', 'Triple', 'Perfect', 'Quintuple'], // 5 skills
  SPECIAL: ['Swap', 'Dice', 'Energy'],                       // 3 skills
  ULTIMATE: ['King', 'Phoenix']                              // 2 skills
};
```

---

## 🚀 Frontend Integration Examples

### 1. Complete Gamification Hook
```typescript
// hooks/useGamification.ts
export const useGamification = () => {
  const [userLevel, setUserLevel] = useState(1);
  const [userXP, setUserXP] = useState(0);
  const [currencies, setCurrencies] = useState({ syncoin: 0, kristal: 0 });
  const [titles, setTitles] = useState([]);
  const [achievements, setAchievements] = useState([]);
  const [skills, setSkills] = useState([]);
  const [avatar, setAvatar] = useState(null);
  
  // Load all gamification data
  const loadGamificationData = async () => {
    const [levelData, currencyData, titlesData, achievementsData, skillsData, avatarData] = 
      await Promise.all([
        api.get('/gamification/level'),
        api.get('/currency/balance'),
        api.get('/titles/user/' + userId),
        api.get('/achievements/user/' + userId),
        api.get('/skills/inventory'),
        api.get('/avatar/inventory/' + userId)
      ]);
    
    setUserLevel(levelData.data.level);
    setUserXP(levelData.data.current_xp);
    setCurrencies(currencyData.data);
    setTitles(titlesData.data);
    setAchievements(achievementsData.data);
    setSkills(skillsData.data);
    setAvatar(avatarData.data);
  };
  
  return {
    userLevel, userXP, currencies, titles, achievements, skills, avatar,
    loadGamificationData
  };
};
```

### 2. Quiz Racing Integration
```typescript
// hooks/useQuizRacing.ts
export const useQuizRacing = (sessionId: string) => {
  const [socket, setSocket] = useState<Socket | null>(null);
  const [energy, setEnergy] = useState(0);
  const [availableSkill, setAvailableSkill] = useState(null);
  const [activeEffects, setActiveEffects] = useState([]);
  const [leaderboard, setLeaderboard] = useState([]);
  
  useEffect(() => {
    const newSocket = io('http://localhost:8888');
    setSocket(newSocket);
    
    // Join racing session
    newSocket.emit('join-quiz-racing', {
      quiz_session_id: sessionId,
      user_id: userId,
      username: username
    });
    
    // Listen for all racing events
    newSocket.on('energy-update', (data) => setEnergy(data.energy_percent));
    newSocket.on('skill-available', (data) => setAvailableSkill(data.skill));
    newSocket.on('skill-executed', (data) => {
      // Handle skill effects
      if (data.effect_data) {
        setActiveEffects(prev => [...prev, data.effect_data]);
      }
    });
    newSocket.on('leaderboard-update', (data) => setLeaderboard(data.leaderboard));
    
    return () => newSocket.close();
  }, [sessionId]);
  
  const useSkill = (skillId: number, targetUserId?: number) => {
    socket?.emit('use-skill', {
      quiz_session_id: sessionId,
      user_id: userId,
      skill_id: skillId,
      target_user_id: targetUserId
    });
  };
  
  const submitAnswer = (questionId: number, answerId: number, responseTime: number) => {
    socket?.emit('submit-racing-answer', {
      quiz_session_id: sessionId,
      user_id: userId,
      question_id: questionId,
      answer_id: answerId,
      response_time: responseTime
    });
  };
  
  return { energy, availableSkill, activeEffects, leaderboard, useSkill, submitAnswer };
};
```

### 3. Complete Gamification Dashboard
```typescript
// components/GamificationDashboard.tsx
const GamificationDashboard = () => {
  const { userLevel, userXP, currencies, titles, achievements, skills } = useGamification();
  
  return (
    <div className="gamification-dashboard">
      {/* Level & XP Display */}
      <div className="level-section">
        <h3>Level {userLevel}</h3>
        <div className="xp-bar">
          <div className="xp-fill" style={{ width: `${(userXP / xpRequired) * 100}%` }} />
        </div>
        <span>{userXP} / {xpRequired} XP</span>
      </div>
      
      {/* Currency Display */}
      <div className="currency-section">
        <div className="syncoin">
          <img src="/icons/syncoin.png" alt="SynCoin" />
          <span>{currencies.syncoin}</span>
        </div>
        <div className="kristal">
          <img src="/icons/kristal.png" alt="Kristal" />
          <span>{currencies.kristal}</span>
        </div>
      </div>
      
      {/* Active Title */}
      <div className="title-section">
        <h4>Current Title</h4>
        <span className="active-title">{titles.find(t => t.is_active)?.title_name}</span>
      </div>
      
      {/* Skills Loadout */}
      <div className="skills-section">
        <h4>Skills Loadout</h4>
        <div className="skill-slots">
          {skills.slice(0, 4).map((skill, index) => (
            <div key={skill.skill_id} className="skill-slot">
              <img src={`/skills/${skill.skill_code}.png`} alt={skill.skill_name} />
              <span>{skill.skill_name}</span>
            </div>
          ))}
        </div>
      </div>
      
      {/* Recent Achievements */}
      <div className="achievements-section">
        <h4>Recent Achievements</h4>
        {achievements.slice(0, 3).map(achievement => (
          <div key={achievement.achievement_id} className="achievement-item">
            <img src={`/achievements/${achievement.achievement_code}.png`} />
            <span>{achievement.achievement_name}</span>
          </div>
        ))}
      </div>
    </div>
  );
};
```

---

## 📦 Testing Package

### Postman Collections
```
doc_for_devgame/task4/
├── Quiz_Racing_Skills.postman_collection.json    # Complete API collection
└── Quiz_Racing_Environment.postman_environment.json # Environment setup
```

### Test Scripts
```
doc_for_devgame/task4/
└── test_quiz_racing_api.js                       # Node.js API testing
```

---

## 🎯 Implementation Priority cho Frontend

### High Priority (Core Features)
1. **User Dashboard** - Level, XP, Currency display
2. **Skills Shop** - Browse and purchase 17 skills
3. **Skills Loadout** - Select 4 skills for racing
4. **Basic Quiz Integration** - XP gain, currency rewards

### Medium Priority (Enhanced Features)  
5. **Avatar Customization** - Equip avatar items and frames
6. **Achievements Display** - Show unlocked achievements
7. **Leaderboard** - Global and subject rankings
8. **Egg Opening** - Egg reward system

### Low Priority (Advanced Features)
9. **Real-time Racing** - WebSocket racing interface
10. **Skill Effects UI** - Active effects display
11. **Social Features** - Emoji interactions
12. **Advanced Analytics** - Performance tracking

---

## 📞 Support & Resources

### Quick Setup
1. **Import Postman**: `Quiz_Racing_Skills.postman_collection.json`
2. **Read Main Guide**: `FRONTEND_INTEGRATION_GUIDE.md`
3. **Test APIs**: Run Postman collection
4. **Check Examples**: Use provided React/TypeScript code

### Backend Status
- ✅ **All 4 Phases Complete**: 50+ endpoints, 15+ WebSocket events
- ✅ **Database Ready**: 25+ tables with sample data
- ✅ **Testing Ready**: Postman + Node.js scripts
- ✅ **Documentation Complete**: Detailed guides and examples

**System ready for full frontend integration! 🚀**
