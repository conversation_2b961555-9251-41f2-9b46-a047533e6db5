const express = require('express');
const router = express.Router();
const learningOutcomeController = require('../controllers/learningOutcomeController');
const { authenticateToken, authorize } = require('../middleware/authMiddleware');

/**
 * LEARNING OUTCOME ROUTES
 * Routes cho phân tích Learning Outcomes theo % hoàn thành
 */

// API chính: Phân tích chi tiết LO theo % hoàn thành
router.get('/completion-analysis/:subject_id/:user_id',
    authenticateToken,
    authorize(['admin', 'teacher', 'student']),
    learningOutcomeController.getLOCompletionAnalysis
);

// API hỗ trợ: <PERSON><PERSON><PERSON> danh sách LO của một môn học
router.get('/subject/:subject_id',
    authenticateToken,
    authorize(['admin', 'teacher', 'student']),
    learningOutcomeController.getLOsBySubject
);

// API hỗ trợ: <PERSON><PERSON><PERSON> chi tiết một LO cụ thể
router.get('/:lo_id/details',
    authenticateToken,
    authorize(['admin', 'teacher', 'student']),
    learningOutcomeController.getLODetails
);

module.exports = router;
