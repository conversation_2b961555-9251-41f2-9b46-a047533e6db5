# LEADERBOARD SYSTEM - <PERSON><PERSON><PERSON>AN TESTING GUIDE
## Task 3.2: <PERSON><PERSON>ng xếp hạng và leaderboard

### Base URL: `http://localhost:3000/api/leaderboard`

---

## 🔧 SETUP REQUIREMENTS

### 1. Database Setup
Trước khi test, chạy SQL script để tạo database schema:
```sql
-- Run the SQL file
\i doc_for_devgame/task3/leaderboard_system.sql
```

### 2. Authentication Token
Để test các authenticated endpoints, cần lấy JWT token:
```
POST http://localhost:3000/api/auth/login
Content-Type: application/json

{
    "username": "your_username",
    "password": "your_password"
}
```

Copy `access_token` từ response để sử dụng trong header:
```
Authorization: Bearer YOUR_ACCESS_TOKEN
```

---

## 📊 PUBLIC ENDPOINTS TESTING

### Test 1: Get Global Leaderboard
```
GET http://localhost:3000/api/leaderboard/global
```

**With Parameters:**
```
GET http://localhost:3000/api/leaderboard/global?criteria=TOTAL_XP&limit=10&offset=0
```

**Expected Response:**
- Status: 200 OK
- Body contains: `leaderboard_type: "GLOBAL"`, `entries` array, `pagination` info

### Test 2: Get Tier-Based Leaderboard
```
GET http://localhost:3000/api/leaderboard/tier?tier=GOLD&criteria=LEVEL&limit=10
```

**Expected Response:**
- Status: 200 OK
- Body contains: `tier_filter: "GOLD"`, filtered entries by tier

**Error Test:**
```
GET http://localhost:3000/api/leaderboard/tier?tier=INVALID_TIER
```
- Expected Status: 400 Bad Request
- Error message about invalid tier

### Test 3: Get Time-Based Leaderboard
```
GET http://localhost:3000/api/leaderboard/time?time_type=WEEKLY&criteria=QUIZ_SCORE&limit=20
```

**Expected Response:**
- Status: 200 OK
- Body contains: `leaderboard_type: "WEEKLY"`, time-filtered entries

**Error Test:**
```
GET http://localhost:3000/api/leaderboard/time?time_type=INVALID_TYPE
```
- Expected Status: 400 Bad Request

### Test 4: Get Top Performers
```
GET http://localhost:3000/api/leaderboard/top-performers?criteria=accuracy_rate&time_period=ALL_TIME&limit=10
```

**With Tier Filter:**
```
GET http://localhost:3000/api/leaderboard/top-performers?criteria=average_score&tier=SILVER&limit=5
```

**Expected Response:**
- Status: 200 OK
- Body contains: `performers` array with performance metrics

### Test 5: Get Top Movers
```
GET http://localhost:3000/api/leaderboard/top-movers?direction=up&limit=10
```

**Movers Down:**
```
GET http://localhost:3000/api/leaderboard/top-movers?direction=down&limit=5
```

**Expected Response:**
- Status: 200 OK
- Body contains: `movers` array with rank changes

### Test 6: Get Leaderboard Statistics
```
GET http://localhost:3000/api/leaderboard/stats?leaderboard_type=GLOBAL&criteria=TOTAL_XP
```

**Expected Response:**
- Status: 200 OK
- Body contains: `statistics` object with aggregated data

### Test 7: Get System Information
```
GET http://localhost:3000/api/leaderboard/info
```

**Expected Response:**
- Status: 200 OK
- Body contains: Complete system configuration info

---

## 👤 AUTHENTICATED ENDPOINTS TESTING

### Test 8: Get My Rank
```
GET http://localhost:3000/api/leaderboard/my-rank
Authorization: Bearer YOUR_ACCESS_TOKEN
```

**With Parameters:**
```
GET http://localhost:3000/api/leaderboard/my-rank?leaderboard_type=GLOBAL&criteria=TOTAL_XP
Authorization: Bearer YOUR_ACCESS_TOKEN
```

**Expected Response:**
- Status: 200 OK
- Body contains: User's rank info, percentile, rank changes

**Error Test (No Auth):**
```
GET http://localhost:3000/api/leaderboard/my-rank
```
- Expected Status: 401 Unauthorized

### Test 9: Get My Rankings
```
GET http://localhost:3000/api/leaderboard/my-rankings
Authorization: Bearer YOUR_ACCESS_TOKEN
```

**Expected Response:**
- Status: 200 OK
- Body contains: All user rankings (global_xp, global_level, tier_based, etc.)

### Test 10: Get My Performance Stats
```
GET http://localhost:3000/api/leaderboard/my-performance
Authorization: Bearer YOUR_ACCESS_TOKEN
```

**With Time Period:**
```
GET http://localhost:3000/api/leaderboard/my-performance?time_period=WEEKLY
Authorization: Bearer YOUR_ACCESS_TOKEN
```

**Expected Response:**
- Status: 200 OK
- Body contains: Detailed performance metrics, calculated grades

### Test 11: Compare Users
```
GET http://localhost:3000/api/leaderboard/compare?compare_user_id=456
Authorization: Bearer YOUR_ACCESS_TOKEN
```

**With Time Period:**
```
GET http://localhost:3000/api/leaderboard/compare?compare_user_id=456&time_period=MONTHLY
Authorization: Bearer YOUR_ACCESS_TOKEN
```

**Expected Response:**
- Status: 200 OK
- Body contains: Comparison data with differences

**Error Test (Missing Parameter):**
```
GET http://localhost:3000/api/leaderboard/compare
Authorization: Bearer YOUR_ACCESS_TOKEN
```
- Expected Status: 400 Bad Request

---

## 🔧 ADMIN ENDPOINTS TESTING

### Test 12: Initialize User Leaderboards
```
POST http://localhost:3000/api/leaderboard/initialize-user
Authorization: Bearer ADMIN_ACCESS_TOKEN
Content-Type: application/json

{
    "user_id": 123
}
```

**Expected Response:**
- Status: 200 OK
- Success message about initialization

**Error Test (Non-Admin):**
```
POST http://localhost:3000/api/leaderboard/initialize-user
Authorization: Bearer USER_ACCESS_TOKEN
Content-Type: application/json

{
    "user_id": 123
}
```
- Expected Status: 403 Forbidden

### Test 13: Update User Score
```
POST http://localhost:3000/api/leaderboard/update-score
Authorization: Bearer ADMIN_ACCESS_TOKEN
Content-Type: application/json

{
    "user_id": 123,
    "criteria": "TOTAL_XP",
    "score": 15000,
    "leaderboard_type": "GLOBAL"
}
```

**With Tier Filter:**
```
POST http://localhost:3000/api/leaderboard/update-score
Authorization: Bearer ADMIN_ACCESS_TOKEN
Content-Type: application/json

{
    "user_id": 123,
    "criteria": "LEVEL",
    "score": 25,
    "leaderboard_type": "TIER_BASED",
    "tier": "GOLD"
}
```

**Expected Response:**
- Status: 200 OK
- Success message with updated data

---

## 🧪 COMPREHENSIVE TEST SCENARIOS

### Scenario 1: New User Journey
1. **Initialize User**: POST `/initialize-user` (Admin)
2. **Check Initial Rank**: GET `/my-rank` (User)
3. **View Global Leaderboard**: GET `/global`
4. **Check Performance**: GET `/my-performance` (User)

### Scenario 2: Quiz Completion Flow
1. **Before Quiz**: GET `/my-rank` (User)
2. **Simulate Quiz Update**: POST `/update-score` (Admin)
3. **After Quiz**: GET `/my-rank` (User)
4. **Check Rank Changes**: GET `/top-movers`

### Scenario 3: Leaderboard Analytics
1. **Global Stats**: GET `/stats?leaderboard_type=GLOBAL`
2. **Top Performers**: GET `/top-performers?criteria=accuracy_rate`
3. **Tier Comparison**: GET `/tier?tier=GOLD` vs GET `/tier?tier=SILVER`
4. **Time Analysis**: GET `/time?time_type=WEEKLY` vs GET `/time?time_type=MONTHLY`

### Scenario 4: User Comparison
1. **Get My Stats**: GET `/my-performance` (User A)
2. **Compare with Friend**: GET `/compare?compare_user_id=456` (User A)
3. **View Friend's Rank**: GET `/my-rank` (User B)

---

## 🚨 ERROR TESTING CHECKLIST

### Authentication Errors:
- [ ] Test endpoints without token (401)
- [ ] Test admin endpoints with user token (403)
- [ ] Test with expired token (401)

### Validation Errors:
- [ ] Invalid criteria values (400)
- [ ] Invalid tier values (400)
- [ ] Invalid time_type values (400)
- [ ] Missing required parameters (400)
- [ ] Invalid user_id values (400)

### Data Errors:
- [ ] Non-existent user_id (404)
- [ ] Empty leaderboard results (200 with empty array)
- [ ] Invalid date formats (400)

---

## 📊 PERFORMANCE TESTING

### Load Testing Endpoints:
1. **High Traffic**: GET `/global` (Most accessed)
2. **User Specific**: GET `/my-rank` (Per user)
3. **Analytics**: GET `/top-performers` (CPU intensive)

### Expected Response Times:
- Public endpoints: < 200ms
- Authenticated endpoints: < 300ms
- Analytics endpoints: < 500ms
- Admin endpoints: < 400ms

---

## 🔍 DEBUGGING TIPS

### Common Issues:
1. **Empty Leaderboards**: Run SQL initialization script
2. **Rank Not Found**: Initialize user leaderboards first
3. **Permission Denied**: Check user role and token validity
4. **Invalid Parameters**: Refer to `/info` endpoint for valid values

### Useful Debug Endpoints:
- GET `/info` - System configuration
- GET `/stats` - Data availability check
- GET `/my-rankings` - User data verification

### Database Verification:
```sql
-- Check leaderboard entries
SELECT * FROM "LeaderboardEntries" LIMIT 10;

-- Check user performance stats
SELECT * FROM "UserPerformanceStats" WHERE user_id = 123;

-- Check ranking history
SELECT * FROM "RankingHistory" ORDER BY created_at DESC LIMIT 10;
```
