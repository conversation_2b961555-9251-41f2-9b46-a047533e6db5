-- =====================================================
-- UPDATE AVATAR ASSETS PATHS TO MATCH FRONTEND
-- =====================================================
-- File: UPDATE_AVATAR_ASSETS_PATHS.sql
-- Purpose: Update database avatar paths to match frontend public assets
-- Date: 2025-07-28

-- Clear existing avatar data
DELETE FROM "UserInventory" WHERE item_type = 'AVATAR';
DELETE FROM "UserCustomization";
DELETE FROM "Avatars";

-- Reset avatar sequence
ALTER SEQUENCE "Avatars_avatar_id_seq" RESTART WITH 1;

-- =====================================================
-- INSERT 30 AVATARS MATCHING FRONTEND ASSETS
-- =====================================================

INSERT INTO "Avatars" ("avatar_name", "avatar_code", "description", "image_path", "rarity", "unlock_type", "unlock_condition", "is_default", "sort_order") VALUES

-- Default avatars (3 avatars - always available)
('Chó', 'DOG', 'Avatar chó đáng yêu và thân thiện', '/avatar-animal-pack/dog.png', 'COMMON', 'DEFAULT', '{}', true, 1),
('Thỏ', 'RABBIT', 'Avatar thỏ nhỏ xinh và ngọt ngào', '/avatar-animal-pack/rabbit.png', 'COMMON', 'DEFAULT', '{}', true, 2),
('Gấu', 'BEAR', 'Avatar gấu nâu đáng yêu', '/avatar-animal-pack/bear.png', 'COMMON', 'DEFAULT', '{}', true, 3),

-- Level-based avatars (15 avatars - unlock by level progression)
('Trâu', 'BUFFALO', 'Avatar trâu mạnh mẽ', '/avatar-animal-pack/buffalo.png', 'COMMON', 'LEVEL', '{"required_level": 5}', false, 4),
('Gà Con', 'CHICK', 'Avatar gà con nhỏ bé', '/avatar-animal-pack/chick.png', 'COMMON', 'LEVEL', '{"required_level": 8}', false, 5),
('Gà', 'CHICKEN', 'Avatar gà trống oai vệ', '/avatar-animal-pack/chicken.png', 'UNCOMMON', 'LEVEL', '{"required_level": 10}', false, 6),
('Bò', 'COW', 'Avatar bò sữa đáng yêu', '/avatar-animal-pack/cow.png', 'UNCOMMON', 'LEVEL', '{"required_level": 12}', false, 7),
('Cá Sấu', 'CROCODILE', 'Avatar cá sấu hung dữ', '/avatar-animal-pack/crocodile.png', 'RARE', 'LEVEL', '{"required_level": 15}', false, 8),
('Vịt', 'DUCK', 'Avatar vịt bơi lội', '/avatar-animal-pack/duck.png', 'COMMON', 'LEVEL', '{"required_level": 18}', false, 9),
('Voi', 'ELEPHANT', 'Avatar voi khổng lồ', '/avatar-animal-pack/elephant.png', 'RARE', 'LEVEL', '{"required_level": 20}', false, 10),
('Ếch', 'FROG', 'Avatar ếch xanh nhảy nhót', '/avatar-animal-pack/frog.png', 'UNCOMMON', 'LEVEL', '{"required_level": 22}', false, 11),
('Hươu Cao Cổ', 'GIRAFFE', 'Avatar hươu cao cổ thanh lịch', '/avatar-animal-pack/giraffe.png', 'RARE', 'LEVEL', '{"required_level": 25}', false, 12),
('Dê', 'GOAT', 'Avatar dê leo núi', '/avatar-animal-pack/goat.png', 'UNCOMMON', 'LEVEL', '{"required_level": 28}', false, 13),
('Khỉ Đột', 'GORILLA', 'Avatar khỉ đột mạnh mẽ', '/avatar-animal-pack/gorilla.png', 'EPIC', 'LEVEL', '{"required_level": 30}', false, 14),
('Hà Mã', 'HIPPO', 'Avatar hà mã to lớn', '/avatar-animal-pack/hippo.png', 'RARE', 'LEVEL', '{"required_level": 32}', false, 15),
('Ngựa', 'HORSE', 'Avatar ngựa phi nhanh', '/avatar-animal-pack/horse.png', 'RARE', 'LEVEL', '{"required_level": 35}', false, 16),
('Khỉ', 'MONKEY', 'Avatar khỉ tinh nghịch', '/avatar-animal-pack/monkey.png', 'UNCOMMON', 'LEVEL', '{"required_level": 38}', false, 17),
('Nai Sừng Tấm', 'MOOSE', 'Avatar nai sừng tấm oai phong', '/avatar-animal-pack/moose.png', 'EPIC', 'LEVEL', '{"required_level": 40}', false, 18),

-- High-level avatars (6 avatars - unlock at high levels)
('Cá Voi Một Sừng', 'NARWHAL', 'Avatar cá voi một sừng huyền bí', '/avatar-animal-pack/narwhal.png', 'EPIC', 'LEVEL', '{"required_level": 45}', false, 19),
('Cú', 'OWL', 'Avatar cú mèo thông thái', '/avatar-animal-pack/owl.png', 'RARE', 'LEVEL', '{"required_level": 50}', false, 20),
('Gấu Trúc', 'PANDA', 'Avatar gấu trúc đáng yêu', '/avatar-animal-pack/panda.png', 'EPIC', 'LEVEL', '{"required_level": 55}', false, 21),
('Vẹt', 'PARROT', 'Avatar vẹt nhiều màu sắc', '/avatar-animal-pack/parrot.png', 'RARE', 'LEVEL', '{"required_level": 60}', false, 22),
('Chim Cánh Cụt', 'PENGUIN', 'Avatar chim cánh cụt đáng yêu', '/avatar-animal-pack/penguin.png', 'EPIC', 'LEVEL', '{"required_level": 65}', false, 23),
('Heo', 'PIG', 'Avatar heo hồng đáng yêu', '/avatar-animal-pack/pig.png', 'UNCOMMON', 'LEVEL', '{"required_level": 70}', false, 24),

-- Legendary avatars (6 avatars - special unlock conditions)
('Tê Giác', 'RHINO', 'Avatar tê giác mạnh mẽ', '/avatar-animal-pack/rhino.png', 'LEGENDARY', 'ACHIEVEMENT', '{"required_achievement": "quiz_master"}', false, 25),
('Lười', 'SLOTH', 'Avatar lười chậm chạp', '/avatar-animal-pack/sloth.png', 'EPIC', 'EGG', '{"egg_types": ["LEGENDARY", "MYTHICAL"]}', false, 26),
('Rắn', 'SNAKE', 'Avatar rắn linh hoạt', '/avatar-animal-pack/snake.png', 'RARE', 'EGG', '{"egg_types": ["ROYAL", "DRAGON"]}', false, 27),
('Hải Mã', 'WALRUS', 'Avatar hải mã to lớn', '/avatar-animal-pack/walrus.png', 'EPIC', 'EGG', '{"egg_types": ["ICE", "KRAKEN"]}', false, 28),
('Cá Voi', 'WHALE', 'Avatar cá voi khổng lồ', '/avatar-animal-pack/whale.png', 'LEGENDARY', 'SPECIAL', '{"special_event": "ocean_master"}', false, 29),
('Ngựa Vằn', 'ZEBRA', 'Avatar ngựa vằn sọc đẹp', '/avatar-animal-pack/zebra.png', 'LEGENDARY', 'SHOP', '{"price_syncoin": 5000, "price_kristal": 100}', false, 30);

-- =====================================================
-- UPDATE AVATAR FRAMES TO MATCH RANK ASSETS
-- =====================================================

-- Clear existing frame data
DELETE FROM "UserInventory" WHERE item_type = 'FRAME';
DELETE FROM "AvatarFrames";

-- Reset frame sequence
ALTER SEQUENCE "AvatarFrames_frame_id_seq" RESTART WITH 1;

INSERT INTO "AvatarFrames" ("frame_name", "frame_code", "description", "image_path", "rarity", "unlock_type", "tier_name", "tier_min_level", "tier_max_level", "is_default", "sort_order") VALUES

-- Default frame (Wood tier - variant 1)
('Khung Cơ Bản', 'BASIC_FRAME', 'Khung avatar cơ bản cho mọi người', '/vector-ranks-pack/wood/diamond-wood-1.png', 'COMMON', 'DEFAULT', 'Wood', 1, 10, true, 1),

-- Tier-based frames (using actual frontend paths - 1 frame per tier)
('Khung Gỗ', 'WOOD_FRAME', 'Khung gỗ cho người mới bắt đầu', '/vector-ranks-pack/wood/diamond-wood-2.png', 'COMMON', 'TIER', 'Wood', 1, 10, false, 2),
('Khung Đồng', 'BRONZE_FRAME', 'Khung đồng cho người chơi tích cực', '/vector-ranks-pack/bronze/diamond-bronze-1.png', 'COMMON', 'TIER', 'Bronze', 11, 20, false, 3),
('Khung Bạc', 'SILVER_FRAME', 'Khung bạc cho người chơi giỏi', '/vector-ranks-pack/silver/diamond-silver-1.png', 'UNCOMMON', 'TIER', 'Silver', 21, 30, false, 4),
('Khung Vàng', 'GOLD_FRAME', 'Khung vàng cho người chơi xuất sắc', '/vector-ranks-pack/gold/diamond-gold-1.png', 'UNCOMMON', 'TIER', 'Gold', 31, 40, false, 5),
('Khung Bạch Kim', 'PLATINUM_FRAME', 'Khung bạch kim cho cao thủ', '/vector-ranks-pack/platinum/diamond-platinum-1.png', 'RARE', 'TIER', 'Platinum', 41, 50, false, 6),
('Khung Onyx', 'ONYX_FRAME', 'Khung onyx cho chuyên gia', '/vector-ranks-pack/onyx/diamond-onyx-1.png', 'RARE', 'TIER', 'Onyx', 51, 60, false, 7),
('Khung Sapphire', 'SAPPHIRE_FRAME', 'Khung sapphire cho bậc thầy', '/vector-ranks-pack/sapphire/diamond-sapphire-1.png', 'EPIC', 'TIER', 'Sapphire', 61, 70, false, 8),
('Khung Ruby', 'RUBY_FRAME', 'Khung ruby cho siêu sao', '/vector-ranks-pack/ruby/diamond-ruby-1.png', 'EPIC', 'TIER', 'Ruby', 71, 80, false, 9),
('Khung Amethyst', 'AMETHYST_FRAME', 'Khung amethyst cho huyền thoại', '/vector-ranks-pack/amethyst/diamond-amethyst-1.png', 'LEGENDARY', 'TIER', 'Amethyst', 81, 90, false, 10),
('Khung Master', 'MASTER_FRAME', 'Khung master cho người vĩ đại nhất', '/vector-ranks-pack/master/diamond-master-1.png', 'LEGENDARY', 'TIER', 'Master', 91, 999, false, 11),

-- Special frames from avatar-frame-pack
('Khung Phượng Hoàng', 'CRIMSON_PHOENIX_FRAME', 'Khung phượng hoàng đỏ rực', '/avatar-frame-pack/crimson-phoenix-frame.png', 'LEGENDARY', 'EGG', NULL, NULL, NULL, false, 12),
('Khung Cyber', 'CYBER_GLITCH_FRAME', 'Khung cyber tương lai', '/avatar-frame-pack/cyber-glitch-frame.png', 'LEGENDARY', 'EGG', NULL, NULL, NULL, false, 13),
('Khung Lễ Hội', 'DRUMALONG_FESTIVAL_FRAME', 'Khung lễ hội sôi động', '/avatar-frame-pack/drumalong-festival-frame.png', 'EPIC', 'EGG', NULL, NULL, NULL, false, 14),
('Khung Lửa', 'NATION_OF_PYRO_FRAME', 'Khung ngọn lửa mạnh mẽ', '/avatar-frame-pack/nation-of-pyro-frame.png', 'EPIC', 'EGG', NULL, NULL, NULL, false, 15),
('Khung Đại Dương', 'OCEAN_SONG_FRAME', 'Khung bài ca đại dương', '/avatar-frame-pack/ocean-song-frame.png', 'RARE', 'EGG', NULL, NULL, NULL, false, 16),
('Khung Sao Tím', 'VIOLET_STARLIGHT_FRAME', 'Khung ánh sao tím', '/avatar-frame-pack/violet-starlight-frame.png', 'RARE', 'EGG', NULL, NULL, NULL, false, 17);

-- =====================================================
-- UPDATE CURRENCY ICONS
-- =====================================================

-- Update currency table to use correct icon paths
UPDATE "Currencies" SET 
    icon_path = '/icons-gems-pack/coin.png',
    description = 'Đồng tiền cơ bản trong game, kiếm được từ quiz và hoạt động hàng ngày'
WHERE currency_code = 'SYNCOIN';

UPDATE "Currencies" SET 
    icon_path = '/icons-gems-pack/gem.png',
    description = 'Đá quý cao cấp, kiếm được từ phân giải vật phẩm và thành tích đặc biệt'
WHERE currency_code = 'KRISTAL';

-- =====================================================
-- UPDATE EMOJIS TO MATCH FRONTEND ASSETS
-- =====================================================

-- Clear existing emoji data
DELETE FROM "UserInventory" WHERE item_type = 'EMOJI';
DELETE FROM "Emojis";

-- Reset emoji sequence
ALTER SEQUENCE "Emojis_emoji_id_seq" RESTART WITH 1;

-- Insert popular emojis matching frontend vector-emojis-pack
INSERT INTO "Emojis" ("emoji_name", "emoji_code", "emoji_unicode", "emoji_image", "category", "rarity", "unlock_type", "unlock_condition", "is_default", "sort_order") VALUES

-- Default emojis (GENERAL category - always available)
('Mặt Cười', 'SMILING_FACE', '😊', '/vector-emojis-pack/smiling-face.png', 'GENERAL', 'COMMON', 'DEFAULT', '{}', true, 1),
('Mặt Cười Với Mắt Cười', 'SMILING_FACE_WITH_SMILING_EYES', '😊', '/vector-emojis-pack/smiling-face-with-smiling-eyes.png', 'GENERAL', 'COMMON', 'DEFAULT', '{}', true, 2),
('Mặt Hơi Cười', 'SLIGHTLY_SMILING_FACE', '🙂', '/vector-emojis-pack/slightly-smiling-face.png', 'GENERAL', 'COMMON', 'DEFAULT', '{}', true, 3),

-- HAPPY category emojis
('Mặt Cười Rạng Rỡ', 'BEAMING_FACE_WITH_SMILING_EYES', '😁', '/vector-emojis-pack/beaming-face-with-smiling-eyes.png', 'HAPPY', 'COMMON', 'LEVEL', '{"required_level": 5}', false, 4),
('Mặt Cười Với Tim', 'SMILING_FACE_WITH_HEARTS', '🥰', '/vector-emojis-pack/smiling-face-with-hearts.png', 'LOVE', 'UNCOMMON', 'LEVEL', '{"required_level": 10}', false, 5),
('Mặt Cười Với Mắt Tim', 'SMILING_FACE_WITH_HEART_EYES', '😍', '/vector-emojis-pack/smiling-face-with-heart-eyes.png', 'LOVE', 'UNCOMMON', 'LEVEL', '{"required_level": 15}', false, 6),
('Mặt Tiệc Tung', 'PARTYING_FACE', '🥳', '/vector-emojis-pack/partying-face.png', 'CELEBRATION', 'RARE', 'LEVEL', '{"required_level": 20}', false, 7),

-- SAD category emojis
('Mặt Hơi Buồn', 'SLIGHTLY_FROWNING_FACE', '🙁', '/vector-emojis-pack/slightly-frowning-face.png', 'SAD', 'COMMON', 'LEVEL', '{"required_level": 8}', false, 8),
('Mặt Khóc', 'CRYING_FACE', '😢', '/vector-emojis-pack/crying-face.png', 'SAD', 'UNCOMMON', 'LEVEL', '{"required_level": 12}', false, 9),
('Mặt Khóc To', 'LOUDLY_CRYING_FACE', '😭', '/vector-emojis-pack/loudly-crying-face.png', 'SAD', 'UNCOMMON', 'LEVEL', '{"required_level": 18}', false, 10),

-- ANGRY category emojis
('Mặt Tức Giận', 'ANGRY_FACE', '😠', '/vector-emojis-pack/angry-face.png', 'ANGRY', 'COMMON', 'LEVEL', '{"required_level": 25}', false, 11),
('Mặt Tức Giận Với Sừng', 'ANGRY_FACE_WITH_HORNS', '👿', '/vector-emojis-pack/angry-face-with-horns.png', 'ANGRY', 'RARE', 'LEVEL', '{"required_level": 30}', false, 12),

-- SURPRISED category emojis
('Mặt Ngạc Nhiên', 'ASTONISHED_FACE', '😲', '/vector-emojis-pack/astonished-face.png', 'SURPRISED', 'COMMON', 'LEVEL', '{"required_level": 22}', false, 13),
('Mặt Sốc', 'FACE_WITH_OPEN_MOUTH', '😮', '/vector-emojis-pack/face-with-open-mouth.png', 'SURPRISED', 'UNCOMMON', 'LEVEL', '{"required_level": 28}', false, 14),

-- SPECIAL category emojis (high-level unlocks)
('Robot', 'ROBOT', '🤖', '/vector-emojis-pack/robot.png', 'SPECIAL', 'EPIC', 'LEVEL', '{"required_level": 40}', false, 15),
('Sao Mắt', 'STAR_STRUCK', '🤩', '/vector-emojis-pack/star-struck.png', 'SPECIAL', 'EPIC', 'LEVEL', '{"required_level": 45}', false, 16),
('Mặt Suy Nghĩ', 'THINKING_FACE', '🤔', '/vector-emojis-pack/thinking-face.png', 'SPECIAL', 'RARE', 'LEVEL', '{"required_level": 35}', false, 17),
('Mặt Nháy Mắt', 'WINKING_FACE', '😉', '/vector-emojis-pack/winking-face.png', 'SPECIAL', 'UNCOMMON', 'LEVEL', '{"required_level": 32}', false, 18),

-- EGG-based emojis (rare unlocks)
('Mặt Hề', 'CLOWN_FACE', '🤡', '/vector-emojis-pack/clown-face.png', 'SPECIAL', 'LEGENDARY', 'EGG', '{"egg_types": ["PARTY", "RAINBOW"]}', false, 19),
('Đầu Lâu', 'SKULL', '💀', '/vector-emojis-pack/skull.png', 'SPECIAL', 'LEGENDARY', 'EGG', '{"egg_types": ["DEMON", "BLACK_HOLE"]}', false, 20);

-- =====================================================
-- SUMMARY
-- =====================================================

-- This update ensures all avatar assets match the frontend public folder structure:
-- ✅ 30 avatars from /avatar-animal-pack/
-- ✅ 13 frames from /vector-ranks-pack/
-- ✅ 20 emojis from /vector-emojis-pack/
-- ✅ Currency icons from /icons-gems-pack/
-- ✅ Ready for egg system from /eggs-icon-pack/

SELECT CONCAT('Avatar assets updated successfully! Total avatars: ', COUNT(*)) FROM "Avatars";
SELECT CONCAT('Frame assets updated successfully! Total frames: ', COUNT(*)) FROM "AvatarFrames";
SELECT CONCAT('Emoji assets updated successfully! Total emojis: ', COUNT(*)) FROM "Emojis";
